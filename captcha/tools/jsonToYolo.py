import os, json


def jsonToTxt(data_json):
    """
        需要从json数据中获取的信息
            图片尺寸、标签类型、标注中心点坐标、标注目标尺寸
    :param data_json:json数据
    :return:[“<object-class> <x_center> <y_center> <width> <height>”,]
    """
    # 图片的宽和高
    image_width = data_json.get("imageWidth")
    image_height = data_json.get("imageHeight")
    box_list = []
    for i in data_json.get("shapes", []):
        # 标签类型
        # labels = i.get("labels")
        label = "1"
        # 标签坐标
        label_x = i.get("points")[0][0]
        label_y = i.get("points")[0][1]
        # 标签大小
        label_width = i.get("points")[1][0] - label_x
        label_height = i.get("points")[1][1] - label_y

        # 计算中心点坐标(归一后)
        central_point_x = (label_x + (label_width / 2)) / image_width
        central_point_y = (label_y + (label_height / 2)) / image_height
        # 标签的宽和高(归一后)
        label_width = label_width / image_width
        label_height = label_height / image_height
        result = [str(label), str(central_point_x), str(central_point_y), str(label_width), str(label_height)]
        box_list.append(result)
    return box_list


if __name__ == '__main__':
    # 需要格式化的标签路径
    source_path = r"img"
    for i in os.listdir(source_path):
        if i.endswith(".json"):
            # 读取标签json数据
            with open(f"{source_path}/{i}", "r", encoding="utf-8") as r1:
                data_json = r1.read()
            # 转为python数据
            data_json = json.loads(data_json.strip())
            # 进行yolov5数据转换
            result = jsonToTxt(data_json)
            # 数据按yolov5格式保存
            with open(f"label/{i.replace('json', 'txt')}", "w", encoding="utf-8") as w1:
                w1.write('\n'.join([' '.join(i) for i in result]))