import torch
from loguru import logger


def get_device():
    """
    自动检测并返回可用的计算设备
    优先级: CUDA > MPS > CPU

    Returns:
        torch.device: 可用的计算设备
    """
    if torch.cuda.is_available():
        logger.info(f"使用CUDA设备")
        return torch.device('cuda').type
    elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
        logger.info("使用Apple Silicon GPU (MPS)设备")
        return torch.device('mps').type
    else:
        logger.info("使用CPU设备")
        return torch.device('cpu').type


if __name__ == "__main__":
    device = get_device()
    print(f"使用设备: {device}")
    # 可选: 显示更多设备信息
    if device.type == 'cuda':
        print(f"GPU名称: {torch.cuda.get_device_name(0)}")
        print(f"GPU数量: {torch.cuda.device_count()}")
    elif device.type == 'mps':
        print("使用Apple Silicon GPU (MPS)")
    else:
        print("使用CPU")
