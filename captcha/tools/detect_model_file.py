import os
import shutil


def find_project_root(current_path=None, markers=('.gitignore',)):
    """
    递归查找项目根目录
    :param current_path: 当前路径，默认为脚本所在路径
    :param markers: 用于标识根目录的文件或文件夹
    :return: 项目根目录路径
    """
    if current_path is None:
        current_path = os.getcwd()

    for marker in markers:
        if os.path.exists(os.path.join(current_path, marker)):
            return current_path

    parent_path = os.path.dirname(current_path)
    if parent_path == current_path:  # 已经到达文件系统根目录
        return None

    return find_project_root(parent_path, markers)


project_root = find_project_root()
if project_root:
    print(f"项目根目录为: {project_root}")
else:
    print("未找到项目根目录")

for dirpath, dirnames, filenames in os.walk(project_root):
    for i in filenames:
        name, ext = os.path.splitext(i)
        if ext in ['.klm', '.pth', '.weights', '.onnx', '.hdf5', '.h5', '.pt']:
            print(os.path.join(dirpath, i))
