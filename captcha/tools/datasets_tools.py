import os
import shutil
import random

original_dir = "/Users/<USER>/Downloads/jy_char_order/train"
new_root = "/Users/<USER>/Downloads/jy_char_order/dataset"
train_ratio = 0.8
min_images = 6  # 阈值：小于此数量时复制到6张
special_train_split = 4  # 对于<6的类别，train分配4张
special_val_split = 2  # 对于<6的类别，val分配2张

os.makedirs(os.path.join(new_root, 'train'), exist_ok=True)
os.makedirs(os.path.join(new_root, 'val'), exist_ok=True)

for class_name in os.listdir(original_dir):
    class_path = os.path.join(original_dir, class_name)
    if not os.path.isdir(class_path):
        continue

    images = [f for f in os.listdir(class_path) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

    # 如果图片少于6张，随机复制直到达到6张
    if len(images) < min_images:
        needed = min_images - len(images)
        for i in range(needed):
            # 随机选择一张现有图片复制
            dup = random.choice(images)
            new_name = f"dup_{i}_{dup}"  # 重命名以避免冲突
            shutil.copy(os.path.join(class_path, dup), os.path.join(class_path, new_name))
            images.append(new_name)  # 添加到列表

    random.shuffle(images)  # 随机化以进行分割

    # 计算train数量
    if len(images) == min_images:  # 对于刚复制到6张的类别，使用特殊分割4:2
        num_train = special_train_split
    else:  # 其他类别，使用0.8比例，但至少1张
        num_train = max(1, int(len(images) * train_ratio))

    train_images = images[:num_train]
    val_images = images[num_train:]

    # 创建类别子文件夹
    train_class_dir = os.path.join(new_root, 'train', class_name)
    val_class_dir = os.path.join(new_root, 'val', class_name)
    os.makedirs(train_class_dir, exist_ok=True)
    if val_images:  # 仅在val有图像时创建
        os.makedirs(val_class_dir, exist_ok=True)

    # 移动文件
    for img in train_images:
        shutil.move(os.path.join(class_path, img), os.path.join(train_class_dir, img))
    for img in val_images:
        shutil.move(os.path.join(class_path, img), os.path.join(val_class_dir, img))

print("手动分割完成！")
