import os
import kenlm
from itertools import permutations


class WordOrder(object):
    def __init__(self, model_path):
        self.model = kenlm.LanguageModel(model_path)

    def n_gram(self, word):
        word_list = list(word)
        # n-gram
        candidate_list = list(permutations(word_list, r=len(word_list)))
        a_c_s = -100
        a_c = ""
        b_c_s = 1000
        for candidate in candidate_list:
            candidate = ' '.join(candidate)
            a = self.model.score(candidate)
            b = self.model.perplexity(candidate)
            if a > a_c_s:
                a_c = candidate
                a_c_s = a
            if b_c_s > b:
                b_c_s = b
        return a_c.replace(" ", '')

    def predict(self, text):
        return self.n_gram(text)


if __name__ == "__main__":
    # https://deepspeech.bj.bcebos.com/zh_lm/zh_giga.no_cna_cmn.prune01244.klm
    # 模型路径
    model_dir = os.path.join(os.path.dirname(__file__), 'models')
    language_model_path = os.path.join(model_dir, 'zh_giga.no_cna_cmn.prune01244.klm')
    # language_model_path = os.path.join(os.path.dirname(__file__), 'zh_giga.no_cna_cmn.prune01244.klm')
    char_order = WordOrder(language_model_path)

    word = ""
    order = char_order.predict(word)
    print(word + " => " + order)
