import os
import random


def is_number(s):
    try:
        int(s)
        return True
    except ValueError:
        return False


class GeneratorSamplePair:

    def __init__(self, path):
        self.data = {}
        for i in os.listdir(path):
            dir_ = os.path.join(path, i)
            self.data[i] = [os.path.join(path, i, filename) for filename in os.listdir(dir_)]

    def get_random_different_sample(self, ownKey):
        key_list = list(self.data.keys())
        key_list.remove(ownKey)
        pics = self.data[random.choice(key_list)]

        for i in pics:
            filename = os.path.splitext(os.path.split(i)[1])[0]
            if not is_number(filename):
                return i

        return random.choice(pics)

    def generate(self):
        sample_pairs = []
        for key in self.data:
            pics = self.data[key]
            pic_ = None
            for i in pics:
                pic_name = os.path.splitext(os.path.split(i)[1])[0]
                if is_number(pic_name):
                    pic_ = i
                    break
            pair = [[pics[0], pics[1], 1], [pic_, self.get_random_different_sample(key), 0]]
            random.shuffle(pair)
            sample_pairs.append(pair)

        return sample_pairs
