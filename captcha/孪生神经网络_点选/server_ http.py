from io import BytesIO
from fastapi import FastAP<PERSON>, File, UploadFile
from typing import List
import uvicorn
from pydantic import BaseModel
import base64
from PIL import Image
import time

from server_similarity import Similarity, Siamese

similarity = Similarity()
app = FastAPI()

# 初始化 提前加载模型
a = time.time()
xsd = similarity.get_similarity(Image.open('./val2/character180/1.jpg'),
                                Image.open('./val2/character180/切割后图片_4.jpg'))
print("图片相似度为", round(xsd, 4))
b = time.time()
print('time: ', b - a)


class Item(BaseModel):
    item_id: str = None
    image_base64: str = None
    image_base642: str = None


@app.get('/')
def hello_word():
    return "欢迎访问验证码识别系统"


@app.post("/post")
async def get_detect_result(postData: Item):
    image_buffer = base64.b64decode(postData.image_base64)
    image_data = BytesIO(image_buffer)
    image = Image.open(image_data)

    image_buffer2 = base64.b64decode(postData.image_base642)
    image_data2 = BytesIO(image_buffer2)
    image2 = Image.open(image_data2)

    similarity_ = similarity.get_similarity(image, image2)
    # similarity_ = getdata(Image.open('./val2/character185/3.jpg'), Image.open('./val2/character185/切割后图片_2.jpg'))

    return {
        "item_id": postData.item_id,
        "slider_distance": str(similarity_)
    }


@app.post('/similarity')
async def files_(files: List[bytes] = File()):
    # target图片
    # target_pic = await files[0].read()
    target_pic = files[0]
    image_data_ = BytesIO(target_pic)
    image_target = Image.open(image_data_)

    files.pop(0)
    similarity_result = []

    a = time.time()
    for pic in files:
        image_data = BytesIO(pic)
        image = Image.open(image_data)
        similarity_ = similarity.get_similarity(image_target, image)
        similarity_result.append(similarity_)
    b = time.time()
    print('time: ', b - a)

    return {
        "相似度列表": similarity_result
    }


if __name__ == '__main__':
    uvicorn.run(app, port=6666)

    # similarity.get_similarity(Image.open('./val2/character181/2.jpg'),
    #                           Image.open('./val2/character181/切割后图片_2.jpg'))
    # a = time.time()
    # xsd = similarity.get_similarity(Image.open('./val2/character180/1.jpg'),
    #                                 Image.open('./val2/character180/切割后图片_4.jpg'))
    # print("图片相似度为", round(xsd, 4))
    # b = time.time()
    # print('time: ', b - a)
