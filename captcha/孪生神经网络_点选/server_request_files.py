import os
import time

from requests_toolbelt import MultipartEncoder
import requests

# data = ··· multipart/form-data 文件上传
data = MultipartEncoder(
    fields={
        'files': ('filename', open('zhihu2/切割后图片_2.jpg', 'rb'), 'text/plain')
    }
)

# target图片
files = {
    'files': open(os.path.join(os.getcwd(), 'zhihu/1.jpg'), 'rb').read()
}
files_list = [
    ('files', open(os.path.join(os.getcwd(), 'zhihu/3.jpg'), 'rb').read())
]

files_name = os.listdir(os.path.join(os.getcwd(), 'zhihu2'))
for index, f in enumerate(files_name):
    files['files'] = open(os.path.join(os.getcwd(), 'zhihu2', f), 'rb').read()
    files_list.append(('files', open(os.path.join(os.getcwd(), 'zhihu2', f), 'rb').read()))

a = time.time()
response = requests.post('http://127.0.0.1:6666/similarity', files=files_list)
b = time.time()
print(response.json())
print('time: ', b - a)
