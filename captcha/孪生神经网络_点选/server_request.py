import requests
import base64
import time

data = open(r'./val2/character185/3.jpg', 'rb',
            ).read()
data_base64 = base64.b64encode(data)

data2 = open(r'./val2/character185/切割后图片_2.jpg', 'rb',
             ).read()
data_base642 = base64.b64encode(data2)

t1 = time.time()
response = requests.post('http://127.0.0.1:6666/post', json={
    "item_id": str(1),
    "image_base64": data_base64.decode(),
    "image_base642": data_base642.decode(),
})
t2 = time.time()

print(response.json())
print('time: ', t2 - t1)
