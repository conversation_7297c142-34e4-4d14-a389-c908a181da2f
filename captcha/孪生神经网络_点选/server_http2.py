from io import BytesIO
from fastapi import FastAPI, File, UploadFile
from typing import List
import uvicorn
from pydantic import BaseModel
import base64
from PIL import Image
import time
import torch.nn as nn
import torch
import torch.utils
import os
import torchvision.models as models
import torchvision.transforms as transforms

# * ----------------------------------------------------------------
os.environ["CUDA_VISIBLE_DEVICES"] = "0"
device = torch.device('cuda')

mymod = models.vgg16(pretrained=True)
del mymod.avgpool
del mymod.classifier


class Siamese(nn.Module):
    def __init__(self, pretrained=True):
        super(Siamese, self).__init__()
        self.resnet = mymod.features
        self.resnet = self.resnet.eval()
        self.resnet.to(device)
        flat_shape = 512 * 3 * 3
        self.fully_connect1 = torch.nn.Linear(flat_shape, 512)
        self.fully_connect2 = torch.nn.Linear(512, 1)
        self.sgm = nn.Sigmoid()

    def forward(self, x1, x2):
        x1 = self.resnet(x1)
        x2 = self.resnet(x2)

        x1 = torch.flatten(x1, 1)
        x2 = torch.flatten(x2, 1)
        x = torch.abs(x1 - x2)
        x = self.fully_connect1(x)
        x = self.fully_connect2(x)
        x = self.sgm(x)
        return x


def get_similarity(p1, p2):
    ch = tpzq(p1)
    ch = ch.to(device).unsqueeze(0)

    ch2 = tpzq(p2)
    ch2 = ch2.to(device).unsqueeze(0)

    h = mymox(ch, ch2)
    return round(h[0, 0].item(), 4)


tpzq = transforms.Compose([
    transforms.Resize((105, 105)),
    transforms.ToTensor()])

mymox = torch.load('./logs/bj0.1056.pth')
mymox.to(device)
# * ----------------------------------------------------------------

app = FastAPI()


# 初始化 提前加载模型
# a = time.time()
# xsd = similarity.get_similarity(Image.open('./val2/character180/1.jpg'),
#                                 Image.open('./val2/character180/切割后图片_4.jpg'))
# print("图片相似度为", round(xsd, 4))
# b = time.time()
# print('time: ', b - a)


class Item(BaseModel):
    item_id: str = None
    image_base64: str = None
    image_base642: str = None


@app.get('/')
def hello_word():
    return "欢迎访问验证码识别系统"


@app.post("/post")
async def get_detect_result(postData: Item):
    image_buffer = base64.b64decode(postData.image_base64)
    image_data = BytesIO(image_buffer)
    image = Image.open(image_data)

    image_buffer2 = base64.b64decode(postData.image_base642)
    image_data2 = BytesIO(image_buffer2)
    image2 = Image.open(image_data2)

    similarity_ = get_similarity(image, image2)
    # similarity_ = get_similarity(Image.open('./val2/character185/3.jpg'), Image.open('./val2/character185/切割后图片_2.jpg'))

    return {
        "item_id": postData.item_id,
        "slider_distance": str(similarity_)
    }


@app.post('/similarity')
async def files_(files: List[bytes] = File()):
    # target图片
    # target_pic = await files[0].read()
    target_pic = files[0]
    image_data_ = BytesIO(target_pic)
    image_target = Image.open(image_data_)

    files.pop(0)
    similarity_result = []

    a = time.time()
    for pic in files:
        image_data = BytesIO(pic)
        image = Image.open(image_data)
        similarity_ = get_similarity(image_target, image)
        similarity_result.append(similarity_)
    b = time.time()
    print('time: ', b - a)

    return {
        "相似度列表": similarity_result
    }


if __name__ == '__main__':
    uvicorn.run(app, port=6666)
