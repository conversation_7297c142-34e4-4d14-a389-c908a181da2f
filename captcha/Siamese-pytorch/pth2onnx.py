from nets.siamese import Siamese
import torch


def pth2onnx():
    # device = torch.device('cuda')
    device = torch.device('mps')
    model_path = "./model_data/best_epoch_weights.pth"
    model = Siamese((105, 105))
    model.load_state_dict(torch.load(model_path, map_location=device))
    model.eval()
    dummy_input = [torch.randn(1, 3, 105, 105), torch.randn(1, 3, 105, 105)]
    torch.onnx.export(model, dummy_input, 'jy3_nine_siamese.onnx', verbose=True, input_names=['x1', 'x2'], output_names=['output'])
    print('Successful!')


pth2onnx()
