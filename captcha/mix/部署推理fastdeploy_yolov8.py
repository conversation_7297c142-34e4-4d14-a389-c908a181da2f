import fastdeploy as fd
import cv2
import os


def build_option(args):
    option = fd.RuntimeOption()

    if args.get("device").lower() == "gpu":
        option.use_gpu()

    if args.get("device").lower() == "ascend":
        option.use_ascend()

    if args.get("use_trt"):
        option.use_trt_backend()
        option.set_trt_input_shape("images", [1, 3, 640, 640])
    return option


args = {
    "model": r"runs\detect\slider2\weights\best.onnx",
    "device": "cpu",
    "use_trt": False
}

# Configure runtime, load model
runtime_option = build_option(args)
model = fd.vision.detection.YOLOv8(args.get("model"), runtime_option=runtime_option)

# Predicting image
# if args.image is None:
#     image = fd.utils.get_detection_test_image()
# else:
#     image = args.image
im = cv2.imread("2.png")
result = model.predict(im)

# Visualization
vis_im = fd.vision.vis_detection(im, result)
cv2.imwrite("visualized_result.jpg", vis_im)
print("Visualized result save in ./visualized_result.jpg")
