# 轨迹生成代码
import random

# 定义轨迹的终点
end_point = [320, 30, 1052]

# 调节生成轨迹元素个数
n = 10  # 生成的轨迹个数越少，滑动越快

# 生成并打印10条不同的轨迹，这些轨迹都以 [0, 0, 0] 为起点
for _ in range(10):
    # 随机改变x, y, z坐标的变化量
    delta_x_variation = random.randint(4, 5)  # 调节生成的x 如[x,.,.]大于320
    delta_y_variation = random.randint(-5, 5)
    delta_z_variation = random.randint(-5, 5)
    delta_x_randomized = (end_point[0] - 0) / n + delta_x_variation
    delta_y_randomized = (end_point[1] - 0) / n + delta_y_variation
    delta_z_randomized = (end_point[2] - 0) / n + delta_z_variation

    # 创建新的轨迹，确保起点是 [0, 0, 0]
    new_trajectory = []
    for i in range(n):  #
        x = max(0, int(0 + i * delta_x_randomized))
        y = max(0, int(0 + i * delta_y_randomized))
        z = max(0, int(0 + i * delta_z_randomized))
        new_trajectory.append([x, y, z])

    # 打印新的轨迹
    print(new_trajectory)
    # 打印轨迹个数
    print(len(new_trajectory))

"""
e.g:
轨迹1 [[0,0,0],[0,0,2],[2,0,159],[3,0,168],[5,0,177],[8,1,185],[13,2,194],[17,3,203],[21,4,212],[27,5,220],[35,6,230],[44,6,238],[54,7,247],[65,8,255],[326,29,1035],[316,30,1043]]
轨迹2 [[0,0,0],[0,0,1],[1,0,98],[3,0,106],[5,-1,115],[8,-1,124],[11,-1,133],[15,-1,141],[21,-1,150],[30,-1,160],[41,-1,168],[55,-1,176],[69,-1,185],[84,-1,194],[97,-1,202],[111,-1,212],[125,-1,220],[138,-1,229],[151,-1,238],[163,-1,247],[175,-1,255],[184,-1,264],[194,-1,272],[202,-1,283],[210,-1,290],[216,-1,299],[223,-1,308],[230,-1,317],[237,-1,325],[245,-1,334],[251,-1,343],[258,-1,351],[265,-1,360],[272,-1,369],[278,-1,378],[285,-1,386],[290,-1,395],[297,-1,412],[303,-1,414],[309,-1,421],[317,-1,430],[327,-1,439],[343,11,1008],[337,14,1016],[331,17,1025],[324,22,1034],[316,26,1043],[308,30,1052]]
轨迹3 [[0,0,0],[0,0,151],[2,0,160],[5,0,168],[9,0,177],[14,0,186],[18,0,195],[24,0,203],[32,0,213],[42,0,221],[52,0,230],[63,0,238],[75,0,248],[87,-1,256],[98,-1,266],[110,-2,274],[120,-3,283],[131,-4,291],[142,-5,300],[152,-5,309],[164,-6,317],[176,-6,326],[188,-6,335],[200,-6,344],[212,-6,352],[224,-6,362],[236,-6,370],[247,-6,379],[256,-6,387],[265,-6,396],[275,-6,405],[284,-6,414],[292,-6,422],[301,-6,433],[311,-6,440],[320,-6,449],[353,4,1736],[351,5,1744],[348,6,1752],[346,7,1770],[343,8,1779],[339,10,1788],[337,11,1797],[336,11,1805],[333,12,1815],[330,14,1822],[327,14,1833],[326,14,1840],[323,15,1851],[320,16,1858],[317,17,1867],[315,17,1875]]
"""
