import fastdeploy as fd
import fastdeploy.vision as vision
import os
import cv2

labels = {1: ''}


def cv_show(image):
    cv2.imshow(f"png", image)
    cv2.waitKey(0)
    cv2.destroyAllWindows()


model = fd.vision.detection.YOLOv5("models/best.onnx")

images = os.listdir("test")
for img in images:
    im = cv2.imread("test/" + img, cv2.IMREAD_UNCHANGED)
    im = cv2.cvtColor(im, cv2.COLOR_BGRA2BGR)
    result = model.predict(im)
    for box, label, score in zip(result.boxes, result.label_ids, result.scores):
        im = cv2.rectangle(im, (int(box[0]), int(box[1])), (int(box[2]), int(box[3])), (0, 0, 255), 2)
        im = cv2.putText(im, labels[label], (int(box[0]), int(box[1])), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 1)

    cv_show(im)

    vis_im = vision.vis_detection(im, result, score_threshold=0.5)
    cv2.imwrite("vis_image.jpg", vis_im)
