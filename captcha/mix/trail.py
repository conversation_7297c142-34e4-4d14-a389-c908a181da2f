import math
import random
import numpy as np
import matplotlib.pyplot as plt
from tracks import list_


def show_result(data):
    x_trail = []
    y_trail = []
    t_trail = []
    for trail in data:
        x = trail[0]
        y = trail[1]
        t = trail[2]
        x_trail.append(x)
        y_trail.append(y)
        t_trail.append(t)
    # diff = np.diff(x_trail)
    # print('diff: ', list(diff))
    # print('diff: ', diff)
    # print('零：', len(diff) - np.count_nonzero(diff))
    # print('-1: ', x_trail[-1])
    # print('len: ', len(x_trail))
    # print('t: ', np.diff(t_trail))
    a = list(np.diff(y_trail))
    a[0] = 0
    print('y: ', sum(a))
    print(np.diff(y_trail))
    print('-----------------------------------')
    plt.plot(t_trail, x_trail)

    # plt.show()


def show_plt(tracks_):
    label = []
    for index, i in enumerate(tracks_):
        show_result(i)
        label.append(f"trail_{index}")

    # plt.legend(label)
    # plt.show()


def easeOutQuint(x):
    return 1 - math.pow(1 - x, 4.7)


def get_trail(move_distence, show=False):
    def __set_pt_time(_dist):
        if _dist < 100:
            __need_time = int(random.uniform(500, 1500))
        else:
            __need_time = int(random.uniform(1000, 2000))
        __end_pt_time = []
        __move_pt_time = []
        __pos_z = []

        total_move_time = __need_time * random.uniform(0.8, 0.9)
        start_point_time = random.uniform(110, 200)
        __start_pt_time = [int(start_point_time)]

        sum_move_time = 0

        _tmp_total_move_time = total_move_time
        while True:
            delta_time = random.uniform(15, 20)
            if _tmp_total_move_time < delta_time:
                break

            sum_move_time += delta_time
            _tmp_total_move_time -= delta_time
            __move_pt_time.append(int(start_point_time + sum_move_time))

        last_pt_time = __move_pt_time[-1]
        __move_pt_time.append(int(last_pt_time + _tmp_total_move_time))

        sum_end_time = start_point_time + total_move_time
        other_point_time = __need_time - sum_end_time
        end_first_ptime = other_point_time / 2

        while True:
            delta_time = random.uniform(110, 200)
            if end_first_ptime - delta_time <= 0:
                break

            end_first_ptime -= delta_time
            sum_end_time += delta_time
            __end_pt_time.append(int(sum_end_time))

        __end_pt_time.append(int(sum_end_time + (other_point_time / 2 + end_first_ptime)))
        __pos_z.extend(__start_pt_time)
        __pos_z.extend(__move_pt_time)
        __pos_z.extend(__end_pt_time)
        return __pos_z

    def __get_pos_y(point_count):
        _pos_y = []
        start_y = random.randint(-1, 1)
        end_y = random.randint(-13, -5)
        x = np.linspace(start_y, end_y, point_count)
        for _, val in enumerate(x):
            _pos_y.append(int(val))

        return _pos_y

    time_list = __set_pt_time(move_distence)
    trail_length = len(time_list)
    t = np.linspace(-0.5, 1, trail_length)  # t
    mult = move_distence / 7.59375
    x = [int(mult * (easeOutQuint(i) + 6.59375)) for i in t]
    y = __get_pos_y(trail_length)
    # t=-0.5 x=-6.59375
    # t=1 x=7.59375
    delta_pt = abs(np.random.normal(scale=3, size=trail_length))
    for index in range(len(delta_pt)):
        change_x = int(x[index] + delta_pt[index])
        if index + 1 < trail_length and x[index + 1] > change_x:
            x[index] = change_x

    if show:
        delta_t = [i for i in range(trail_length)]
        plt.plot(delta_t, delta_pt, color='green')
        # plt.plot(time_list, x, color='red')
        plt.show()
    result = []
    print(x[-1] - x[0])
    for idx in range(trail_length):
        result.append([x[idx], y[idx], time_list[idx]])
    return result


def showEaseOutQuint(distance, func):
    max_t = 0.72  # 时间最大作用域截至
    trail_length = 200  # 时间点个数
    t = np.linspace(0, max_t, trail_length)
    s = [distance / func(max_t) * func(x) for x in t]

    delta_pt = abs(np.random.normal(scale=1, size=trail_length))
    for index in range(trail_length):
        change_x = int(s[index] + delta_pt[index])
        if index < trail_length - 5 and s[index + 1] > change_x:
            s[index] = change_x

    plt.plot(t, s)
    plt.show()


if __name__ == '__main__':
    show_plt(list_)
    # show_result(list_[7])
    # distance = 215
    # showEaseOutQuint(distance, easeOutQuint)
    # result = get_trail(215, False)
    # print(result)
    # show_result(result)
    """        
    1. 收集轨迹
    2. 画出轨迹图像 找缓动函数相同形状
    3. 绘制缓动函数 找到符合形状的作用域
    4. 找到作用域内最大值 最小值 上下移动 *距离系数
    5. 替换时间（t）轴为自己的
    6. 高斯函数增加波动
    7. 细节修改 x轴 t轴 np.diff对比
    """
