import random


def get_slide_track(distance):
    def __ease_out_expo(sep):
        if sep == 1:
            return 1
        else:
            return 1 - pow(2, -10 * sep)

    if not isinstance(distance, int) or distance < 0:
        raise ValueError(f"distance类型必须是大于等于0的整数: distance: {distance}, type: {type(distance)}")

    slide_track = [
        [random.randint(-50, -10), random.randint(-50, -10), 0],
        [0, 0, 0],
    ]
    # 共记录count次滑块位置信息
    # count = random.randint(30, 40) + int(distance / 2)
    count = 30 + int(distance / 2)
    # 初始化滑动时间
    t = random.randint(50, 100)
    # 记录上一次滑动的距离
    _x = 0
    _y = 0
    for i in range(count):
        # 已滑动的横向距离
        x = round(__ease_out_expo(i / count) * distance)
        # 滑动过程消耗的时间
        t += random.randint(10, 20)
        if x == _x:
            continue
        y = random.randint(-5, 0)

        slide_track.append([x, y, t])
        if x == distance:
            break
        _x = x

    slide_track.append(slide_track[-1])
    return slide_track, slide_track[-1][2]


a, b = get_slide_track(172)
print(a)
print(len(a))

# aa = []
# t = 0
# for i in a:
#     if i == 0:
#         t = i[-1]
#         continue
#     aa.append(i[-1] - t)
#     t = i[-1]
#
# print(aa)
