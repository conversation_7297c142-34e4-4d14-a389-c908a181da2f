import os
from ultralytics import <PERSON><PERSON><PERSON>
from captcha.tools.detect_device import get_device
import time

if __name__ == '__main__':
    device = get_device()
    # model = YOLO("../../models/yolo_jy3_nine_classify.pt")
    model = YOLO("../../models/yolo_jy3_nine_classify.onnx", task='classify')
    model('/Users/<USER>/learn/pycharm/temp/nine/小图/1.png')
    base_dir = '/Users/<USER>/learn/pycharm/temp/nine/小图'

    # # 收集所有图片路径
    # image_paths = []
    # image_info = {}  # 存储图片信息以便后续输出

    for i in os.listdir(base_dir):
        a = time.time()
        results = model.predict(source=os.path.join(base_dir, i), imgsz=64, device=device)
        print(f'{time.time() - a}: {results[0].names[results[0].probs.top1]}')

    # # 批量推理
    # batch_size = 10  # 可根据内存调整
    # start_time = time.time()
    #
    # results = model.predict(source=image_paths, imgsz=64, device=device, batch=batch_size)
    #
    # # 输出结果
    # total_time = time.time() - start_time
    # print(f"总耗时: {round(total_time, 3)}秒, 平均每张: {round(total_time / len(image_paths), 3)}秒")
    #
    # # 处理结果
    # for i, result in enumerate(results):
    #     path = image_paths[i]
    #     predicted_class = result.names[result.probs.top1]
    #     print(f"{i + 1}: {predicted_class}")
