from ultralytics import YOLO

"""
参数的作用：
- data: 数据集路径，包含train和val文件夹
- epochs: 训练轮数
- imgsz: 输入图像的大小
- batch: 每个批次的图像数量
- device: 训练使用的设备，'cuda'，'cpu'，'mps'
参数名称	类型	默认值	范围	如何修改图像
hsv_h	float	0.015	0.0 - 1.0	按颜色轮的一小部分调整色调，引入颜色变异。
hsv_s	float	0.7	0.0 - 1.0	按比例调整饱和度，影响颜色强度。
hsv_v	float	0.4	0.0 - 1.0	按比例调整亮度，帮助适应不同照明条件。
degrees	float	0.0	0.0 - 180	在指定范围内随机旋转图像，提高对方向的处理能力。
translate	float	0.1	0.0 - 1.0	按图像尺寸的一小部分水平/垂直平移，帮助检测部分可见物体。
scale	float	0.5	>=0.0	按增益因子缩放图像，模拟物体不同距离。
shear	float	0.0	-180 - +180	按度数剪切图像，模拟不同观察角度。
perspective	float	0.0	0.0 - 0.001	应用透视变换，提高对3D空间的理解。
flipud	float	0.0	0.0 - 1.0	以指定概率上下翻转图像，增加变异性。
fliplr	float	0.5	0.0 - 1.0	以指定概率左右翻转图像，有助于对称物体。
bgr	float	0.0	0.0 - 1.0	以指定概率将RGB通道交换为BGR，提高通道鲁棒性。
mosaic	float	1.0	0.0 - 1.0	以概率将四张图像拼接成一张，模拟复杂场景组合。
mixup	float	0.0	0.0 - 1.0	以概率混合两张图像及其标签，通过视觉变异增强泛化。
cutmix	float	0.0	0.0 - 1.0	以概率切割并粘贴图像部分，引入遮挡场景，提高泛化。
copy_paste	float	0.0	0.0 - 1.0	以概率将物体从一张图像复制粘贴到另一张（仅分割任务），可结合模式如'flip'或'mixup'。
"""

if __name__ == '__main__':
    model = YOLO("../models/yolo11l-cls.pt")
    results = model.train(data="/Users/<USER>/Downloads/jy_char_order/dataset",
                          epochs=100, imgsz=64, batch=64, device='mps',
                          # 数据增强
                          degrees=20.0
                          )
