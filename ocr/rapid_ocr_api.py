import asyncio
from concurrent.futures import <PERSON><PERSON>oolExecutor

import uvicorn
from fastapi import FastAPI, File, UploadFile
from loguru import logger
from rapidocr import LangDet, ModelType, OCRVersion, RapidOCR, EngineType

# Placeholder for per-worker OCR engine
engine: RapidOCR


def init_engine():
    """Initialize the OCR engine once per process."""
    global engine
    engine = RapidOCR(
        params={
            "Rec.ocr_version": OCRVersion.PPOCRV4,
            "Rec.engine_type": EngineType.ONNXRUNTIME,
            "Rec.model_type": ModelType.SERVER,
            "Rec.lang_type": LangDet.CH,
        }
    )


def run_ocr_in_process(image_content: bytes):
    """Run OCR inference in a separate process using the per-process engine."""
    global engine
    if engine is None:
        raise RuntimeError("OCR engine not initialized in this process.")
    result = engine(image_content, use_det=False, use_cls=True, use_rec=True)
    return result


app = FastAPI()

# Create a fixed-size pool of workers, each running init_engine once
executor = ProcessPoolExecutor(
    max_workers=10,
    initializer=init_engine
)


@app.post("/ocr/")
async def ocr_endpoint(image: UploadFile = File(...)):
    """Handle OCR requests by offloading processing to a process pool."""
    # Read the uploaded image content
    image_content = await image.read()

    # Get the current asyncio event loop
    loop = asyncio.get_event_loop()

    # Run the OCR function in the process pool
    try:
        result = await loop.run_in_executor(executor, run_ocr_in_process, image_content)
    except Exception as e:
        logger.error(f'file: {image.filename}, error: {e}')
        return {"result": "", "score": 0.0}

    # await asyncio.sleep(1)
    if result.txts:
        return {"result": result.txts[0], "score": result.scores[0]}
    else:
        return {"result": "", "score": 0.0}


if __name__ == "__main__":
    # Note: In production, use a process manager like Gunicorn to run Uvicorn
    uvicorn.run(app, host="127.0.0.1", port=9000)
    # print(os.cpu_count())
