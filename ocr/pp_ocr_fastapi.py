from paddleocr import PaddleOCR
from fastapi import FastAPI, UploadFile
import uvicorn
from typing import List
from loguru import logger
import os

app = FastAPI()
pp_ocr = PaddleOCR(use_angle_cls=True, lang="ch", use_mp=True, total_process_num=os.cpu_count())


@app.post("/ocr")
def ocr_(files: List[UploadFile]):
    str_ = ''
    for file in files:
        image_data = file.file.read()
        result = pp_ocr.ocr(image_data, cls=True, det=True)
        # for i in result:
        #     if i:
        #         for j in i:
        #             str_ += f" {j[-1][0]} "

    # logger.info(str_)

    return {
        # 'result': str_
        'result': result
    }


if __name__ == '__main__':
    uvicorn.run("__main__:app", host="0.0.0.0", port=1314)
