import requests
import urllib3

urllib3.disable_warnings()

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) uTools/5.1.1 Chrome/108.0.5359.215 Electron/22.3.27 Safari/537.36",
}
url = "https://ocr.u-tools.cn:7999/ocr"
params = {
    # "access_token": "ekcux1gllejvmfukuiyzix1s15enzq12ixveagkj",
    "model": "ocr_system"
    # "model": "structure_table"
}
response = requests.post(url, headers=headers, params=params, verify=False,
                         files={
                             "image": open(r"/Users/<USER>/Downloads/jy3_chars/0a420e524410641e078bb988cd0ac3cc.png", 'rb')
                         })

print(response.json())
