import cv2
import time
from onnxocr.onnx_paddleocr import ONNXPaddleOcr, sav2Img
import sys
import time

# 固定到onnx路径·
# sys.path.append('./paddle_to_onnx/onnx')

model = ONNXPaddleOcr(
    use_angle_cls=True, use_gpu=False,
    # det_model_dir="models/ppocrv5/det/det.onnx",
    # rec_model_dir="models/ppocrv5/rec/rec.onnx",
)

img = cv2.imread('/Users/<USER>/temp/jy3_语序点选/jy3_chars/今_89c91f6fd22f215df07cdf30c30ef692.png')
s = time.time()
result = model.ocr(img, cls=True, det=False, rec=True)
e = time.time()
print("total time: {:.3f}".format(e - s))
print("result:", result)
# for box in result[0]

# sav2Img(img, result, name=str(time.time()) + '.jpg')
