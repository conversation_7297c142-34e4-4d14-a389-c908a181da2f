from rapidocr import RapidOCR, OCRVersion, EngineType, ModelType, LangDet

engine = RapidOCR(
    params={
        "Rec.ocr_version": OCRVersion.PPOCRV4,
        "Rec.engine_type": EngineType.ONNXRUNTIME,
        "Rec.model_type": ModelType.SERVER,
        "Rec.lang_type": LangDet.CH
    }
)


def rapid_ocr(img):
    result = engine(img, use_det=False, use_cls=True, use_rec=True)
    return result


if __name__ == '__main__':
    # img_url = "/Users/<USER>/learn/pycharm/ocr/output/國_2530e54a2afa0704609207508244f10b_closed.png"
    img_url_ = "/Users/<USER>/temp/jy3_语序点选/error/08fe200ad71375e9e71e100553a1a8b3.png"
    result_ = engine(img_url_, use_det=False, use_cls=True, use_rec=True)
    print(result_.txts)
    # result_.vis("vis_result.jpg")
