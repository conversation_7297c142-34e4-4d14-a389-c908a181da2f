# import pymysql
#
# db_config = {
#     'host': 'localhost',
#     'port': 3306,
#     'password': 'ss520999',
#     'user': 'root',
#     'db': 'world'
# }
# # 连接
# conn = pymysql.connect(**db_config)
# # 建立光标 用于执行sql语句
# cur = conn.cursor()
#
# sql = "select * from city"
#
# cur.execute(sql)
#
# print(cur.fetchall())
#
# conn.commit()
#
# # 关闭
# cur.close()
# conn.close()

import MySQLdb

connect = MySQLdb.connect(
    host='127.0.0.1',
    port=3306,
    user='root',
    passwd='ss520999',
    # db='article_spider',
    db='world',
    # charset='utf8',
    # use_unicode=True
)

cur = connect.cursor()

sql = "select * from city"

cur.execute(sql)

print(cur.fetchall())

cur.close()
connect.close()
