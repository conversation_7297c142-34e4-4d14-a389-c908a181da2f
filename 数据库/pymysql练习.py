import random
import threading
from threading import Lock

import pymysql

# 不支持并发

config = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'ss520999',
    'database': 'temp'
}

lock = Lock()
conn = pymysql.connect(**config)


def test():
    with pymysql.connect(**config) as conn:
        with conn.cursor() as cursor:
            cursor.execute('select * from temp1')
            result = cursor.fetchall()
            print(result)

            cursor.execute('insert into temp1 values("jiejieshushu",22,"2023-10-10")')

        # * 默认开启事务 保存rollback
        conn.commit()


def read():
    print(f'子线程: {threading.current_thread().getName()}')
    cursor = conn.cursor()
    lock.acquire()
    cursor.execute('select * from temp1 where age1 = 18')
    result = cursor.fetchall()
    lock.release()
    for row in result:
        print(row)
    cursor.close()


def write():
    try:
        print(f'子线程: {threading.current_thread().getName()}')
        cursor = conn.cursor()
        lock.acquire()
        for i in range(10):
            cursor.execute(f'insert into temp1(name,age1) values({i},{random.randint(18, 19)})')
        conn.commit()
        lock.release()
    except:
        conn.rollback()


if __name__ == '__main__':
    a = threading.Thread(target=write)
    b = threading.Thread(target=read)

    a.start()
    b.start()

    a.join()
    b.join()

    print('主线程完成')
