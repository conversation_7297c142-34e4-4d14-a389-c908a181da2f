import pymongo

client = pymongo.MongoClient(host='localhost', port=27017)
db = client.test
a = db.students
student1 = {
    'id': '3',
    'name': 'jiejie',
    'age': 21,
    'gender': 'woman'
}
student2 = {
    'id': '1',
    'name': 'shushu',
    'age': 19,
    'gender': 'woman'
}
student3 = {
    'id': '2',
    'name': 'dingding',
    'age': 20,
    'gender': 'man'
}
result = a.insert_many([student1, student2, student3])
print(result.inserted_ids)
