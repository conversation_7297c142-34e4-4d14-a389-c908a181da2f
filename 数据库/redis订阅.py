import redis

db = redis.StrictRedis(host='127.0.0.1', port=6379)
pub = db.pubsub()
pub.subscribe('test')
# pub.parse_response()
#
# while True:
#     print('开始监听')
#     message = pub.parse_response()
#     print(message[2].decode('utf-8'))
#
#     if message[2].decode('utf-8') == 'exit':
#         pub.unsubscribe('test')
#         print('取消订阅成功')
#         break

for item in pub.listen():
    if item['type'] == 'message':
        print(item)
