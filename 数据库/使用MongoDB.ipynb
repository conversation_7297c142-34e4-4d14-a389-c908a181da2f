#%%
import pymongo

client = pymongo.MongoClient(host='localhost', port=27017)
db = client.test
a = db.students
#%%
result = a.find_one({'name': 'jiejie'})
print(result)
print(type(result))
#%%
from bson.objectid import ObjectId

result = a.find_one({'_id': ObjectId('6255392898e4b1ccbe59d847')})
print(result)
#%%
results = a.find({'name': {'$regex': '^j.*'}})
print(results)
for result in results:
    print(result)
#%%
count = a.find({'age': 19}).count()
print(count)
#%%
results = a.find().sort('name', pymongo.ASCENDING).skip(6).limit(2)
print([result['name'] for result in results])
#%%
condition = {'name': 'jiejie'}
student = a.find_one(condition)
student['age'] = 25
result = a.update_one(condition, {'$set': student})
print(result)
print(result.matched_count, result.modified_count)
#%%
result = a.update(condition, {'$set': student})
print(result)
#%%
condition = {'age': {'$gt': 20}}
result = a.update_one(condition, {'$inc': {'age': 1}})
print(result)
print(result.matched_count, result.modified_count)
#%%
condition = {'age': {'$gt': 19}}
result = a.update_many(condition, {'$inc': {'age': 1}})
print(result)
print(result.matched_count, result.modified_count)
#%%
result = a.remove({'name':'jiejie'})
print(result)
#%%
count = a.find({'name':'shushu'}).count()
print(count)
#%%
count = a.find({'age':{'$gt':18}}).count()
print(count)
#%%
result = a.delete_many({'age':{'$gt':18}})
print(result)
#%%
print(result.deleted_count)
#%%
