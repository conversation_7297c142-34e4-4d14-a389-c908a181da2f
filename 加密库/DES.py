import os
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import padding
import base64


def des3_encrypt(key, plaintext):
    # 生成一个随机的IV（Initialization Vector）
    iv = b'\x00' * 8

    # 使用PKCS7填充方式进行填充
    padder = padding.PKCS7(64).padder()
    padded_plaintext = padder.update(plaintext) + padder.finalize()

    # 创建一个DES3算法实例
    cipher = Cipher(algorithms.TripleDES(key), modes.CBC(iv), backend=default_backend())

    # 创建加密器
    encryptor = cipher.encryptor()

    # 执行加密操作
    ciphertext = encryptor.update(padded_plaintext) + encryptor.finalize()

    # 返回加密后的结果（Base64编码）
    return base64.b64encode(ciphertext)


def des3_decrypt(key, ciphertext):
    # 解码Base64
    ciphertext = base64.b64decode(ciphertext)

    # 创建一个DES3算法实例
    cipher = Cipher(algorithms.TripleDES(key), modes.CBC(b'\x00' * 8), backend=default_backend())

    # 创建解密器
    decryptor = cipher.decryptor()

    # 执行解密操作
    padded_plaintext = decryptor.update(ciphertext) + decryptor.finalize()

    # 使用PKCS7填充方式进行去除填充
    unpadder = padding.PKCS7(64).unpadder()
    plaintext = unpadder.update(padded_plaintext) + unpadder.finalize()

    # 返回解密后的明文
    return plaintext


if __name__ == '__main__':
    # 明文
    plaintext = b'a6860cb2b170c6fafd2a722ea1665b9e'

    # 加密
    # 密钥（需要保证长度为16或24字节）
    key = bytes("1771b31b7bce64f6", "utf-8")
    ciphertext = des3_encrypt(key, plaintext)
    print("加密后的结果：", ciphertext)

    # 解密
    # key = bytes("1771b31b7bce64f6", "utf-8")
    # ciphertext = "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"
    # decrypted_text = des3_decrypt(key, ciphertext)
    # print("解密后的结果：", decrypted_text.decode())


