from Crypto.Cipher import AES
import base64


def pkcs7_pad(data: bytes, block_size: int = 16) -> bytes:
    """
    PKCS7 填充
    """
    padding_len = block_size - (len(data) % block_size)
    padding = bytes([padding_len] * padding_len)
    return data + padding


def pkcs7_unpad(data: bytes) -> bytes:
    """
    PKCS7 反填充
    """
    padding_len = data[-1]  # 根据最后一个字节判断填充长度
    return data[:-padding_len]


def aes_encrypt_ecb(key: str, plaintext: str) -> str:
    """
    使用 AES-ECB 进行加密，并返回 Base64 编码后的密文
    """
    # 将 key 和 plaintext 转为字节形式
    key_bytes = key.encode('utf-8')
    data_bytes = plaintext.encode('utf-8')

    # 手动 PKCS7 填充
    data_bytes_padded = pkcs7_pad(data_bytes, 16)

    # 创建 AES 对象（ECB模式）
    cipher = AES.new(key_bytes, AES.MODE_ECB)

    # 加密并 Base64 编码
    encrypted = cipher.encrypt(data_bytes_padded)
    encrypted_b64 = base64.b64encode(encrypted).decode('utf-8')
    return encrypted_b64


def aes_decrypt_ecb(key: str, encrypted_b64: str) -> str:
    """
    使用 AES-ECB 进行解密，并返回原始明文
    """
    # 将 key 和加密文本转为字节形式
    key_bytes = key.encode('utf-8')
    encrypted_bytes = base64.b64decode(encrypted_b64)

    # 创建 AES 对象（ECB模式）
    cipher = AES.new(key_bytes, AES.MODE_ECB)

    # 解密并去除 PKCS7 填充
    decrypted_padded = cipher.decrypt(encrypted_bytes)
    decrypted = pkcs7_unpad(decrypted_padded)
    return decrypted.decode('utf-8')


if __name__ == "__main__":
    # 测试用例
    key = "RuMhbTayLJuSz123"  # 16 字节秘钥
    plaintext = "p4XJ6v6c8XFf3mb6"

    # 加密
    encrypted_text = aes_encrypt_ecb(key, plaintext)
    print("加密后的结果：", encrypted_text)

    # 解密
    decrypted_text = aes_decrypt_ecb(key, encrypted_text)
    print("解密后的结果：", decrypted_text)
