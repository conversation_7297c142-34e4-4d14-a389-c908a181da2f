from loguru import logger
from telegram import Update
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes
import json
import logging
import requests
import pyautogui
from io import BytesIO
import asyncio

from gpt_answer import gpt_answer_async, gpt_answer

models = [
    'doubao-seed-1.6',
    'gemini-2.5-flash',
    'gpt-4.1-2025-04-14',
]


# models = ['gemini-2.5-pro-preview-05-06', 'grok-vision-beta']


async def get_answer(binary_data, model) -> str:
    try:
        answer = await gpt_answer_async(binary_data, model)
    except:
        answer = 'API调用失败'
    return answer


def capture_screen(left=0, top=0, width=None, height=None):
    if width is None:
        width = pyautogui.size().width
    if height is None:
        height = pyautogui.size().height

    try:
        screenshot = pyautogui.screenshot(region=(left, top, width, height))
        image_stream = BytesIO()
        screenshot.save(image_stream, format='PNG')
        image_binary = image_stream.getvalue()
        image_stream.close()
        logger.info(f"成功截取屏幕区域: left={left}, top={top}, width={width}, height={height}")
        return image_binary
    except Exception as e:
        return None


async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理用户发送的文本消息"""
    user_message = update.message.text
    chat_id = update.message.chat_id
    logger.info(f"收到来自 {chat_id} 的消息: {user_message}")

    if user_message == '11':
        binary_data = capture_screen(left=500, top=250, width=1000, height=500)
        binary_data2 = capture_screen(left=350, top=150, width=1200, height=600)
        if not binary_data:
            await update.message.reply_text("截图失败，请重试。")
            logger.error("截图失败")
            return

        await update.message.reply_photo(photo=BytesIO(binary_data), caption="题目截图")
        await update.message.reply_photo(photo=BytesIO(binary_data2), caption="全屏截图")
        logger.info(f"已向 {chat_id} 发送截图")

        # 并发调用各模型获取答案
        # tasks = {
        #     asyncio.create_task(get_answer(binary_data, model)): model
        #     for model in models
        # }
        tasks = [asyncio.create_task(get_answer(binary_data, model), name=f'{model}')
                 for model in models]
        while tasks:
            done, tasks = await asyncio.wait(tasks, return_when=asyncio.FIRST_COMPLETED)
            for task in done:
                answer = task.result()
                model_name = task.get_name()
                await update.message.reply_text(f"{model_name} 回答：\n\n {answer}")

        await update.message.reply_text(f"-----------------------------------")
        logger.info(f"已向 {chat_id} 发送所有模型的答案----------------------------------------------")

        # for task in asyncio.as_completed(tasks.keys()):
        #     try:
        #         answer = await task
        #     except Exception:
        #         answer = 'API调用失败'
        #     model_name = tasks[task]
        #     await update.message.reply_text(f"{model_name} 回答：\n{answer}")

        # answer = await get_answer(binary_data, 'gpt-4.1-2025-04-14')
        # logger.info(f"get_answer 返回: {answer}")
        # await update.message.reply_text(answer)


async def error_handler(update: object, context: ContextTypes.DEFAULT_TYPE) -> None:
    """记录发生的错误"""
    logger.error(f"Update {update} caused error {context.error}")


def main() -> None:
    """启动 bot"""
    application = Application.builder().token("7925305485:AAGEqSyTGS54WI9n0eIwIibx75VaSz71XPI").build()

    # 添加消息处理器
    # MessageHandler会接收所有文本消息，并通过filters.TEXT进行过滤
    # 我们在 handle_message 函数内部判断消息内容是否为 '截图'
    message_handler = MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message)
    application.add_handler(message_handler)

    # 添加错误处理器
    application.add_error_handler(error_handler)

    # 启动 Bot 直到用户按下 Ctrl-C
    logger.info("Bot 已启动，按 Ctrl-C 停止。")
    application.run_polling()


if __name__ == '__main__':
    main()
