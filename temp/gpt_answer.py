import asyncio
import base64
from textwrap import dedent

from loguru import logger
from openai import AsyncOpenAI, OpenAI

client = OpenAI(
    api_key="sk-DCdBe9c1o2Q3mpVc232279Bb617846D6B7CdBb3e847f9682",
    base_url="https://api.oaibest.com/v1"
)
async_client = AsyncOpenAI(
    api_key="sk-hFkzyJWLf0i8Kf2XezlpX14POTC4TxnPxIWMHKYrXbQOawAq",
    base_url="https://api.oaibest.com/v1"
    # api_key="sk-TaZ7lJp0Q79y98Dp75E7F3E3808e45B9A0822a0dCc4dDe3f",
    # base_url="https://api.ocoolai.com/v1"
)


def encode_image(binary_data):
    return base64.b64encode(binary_data).decode("utf-8")


prompt = '''
    # 角色
    你是一名选择题做题专家
    # 背景
    主要是关于在中国知识产权的选择题
    # 任务
    图片中是一道不定项选择题，请先理解图片中的题目内容，然后分析、理解题目要求，最后给出答案。
    # 要求
    不要给出每个选项的过度分析，只需简单分析。
    # 你的答案：
    '''


def gpt_answer(binary_data, model):
    base64_image = encode_image(binary_data)
    response = client.chat.completions.create(
        model=model,
        messages=[  # type: ignore
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": dedent(prompt)},
                    {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{base64_image}"}}
                ]
            }
        ],
        temperature=0.1,
        top_p=0.7,
    )
    return response.choices[0].message.content


async def gpt_answer_async(binary_data, model):
    base64_image = encode_image(binary_data)
    response = await async_client.chat.completions.create(
        model=model,
        messages=[  # type: ignore
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": dedent(prompt)},
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{base64_image}"
                        }
                    }
                ]
            }
        ],
        temperature=0.1,
        top_p=0.7,
    )
    logger.info(response.choices[0].message.content)
    return response.choices[0].message.content
