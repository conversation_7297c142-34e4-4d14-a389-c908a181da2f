import threading
import time
import requests
import urllib3
from proxy import Proxy  # 需要 pip install Python_Proxy

urllib3.disable_warnings()

def run_proxy():
    # 在本地 127.0.0.1:1080 启动 Shadowsocks 隧道
    with Proxy(
        port=1080,
        protocol="ss",
        cipher="aes-128-gcm",
        password="94c2598d-a942-4386-817a-767c37974232",
        remote="main-central.earthsurface.cc:21153"
    ) as p:
        p.sleep_loop()

threading.Thread(target=run_proxy, daemon=True).start()
time.sleep(1)

proxies = {
    "http":  "socks5h://127.0.0.1:1080",
    "https": "socks5h://127.0.0.1:1080",
}

response = requests.get(
    "https://findbiz.nat.gov.tw/fts/query/QueryCmpyDetail/queryCmpyDetail.do",
    headers={
        "Referer":"https://findbiz.nat.gov.tw/fts/query/QueryList/queryList.do",
        "User-Agent":"Mozilla/5.0"
    },
    params={
        "objectId":"SEM4NDA3MTg1OQ==",
        "banNo":"84071859",
        "disj":"13FC27A616D13E5334C3E39C58AF47DC",
        "fhl":"zh_TW"
    },
    verify=False,
    proxies=proxies,
    timeout=10
)
print(response.text)