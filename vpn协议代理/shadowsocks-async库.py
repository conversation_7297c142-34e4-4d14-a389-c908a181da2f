import asyncio
import threading
import time
import requests
import urllib3
from shadowsocks_async import Server  # 需要 pip install shadowsocks-async

urllib3.disable_warnings()

# 1. SS 配置（与官方 shadowsocks 一致）
CONFIG = {
    "server":        "main-central.earthsurface.cc",
    "server_port":   21153,
    "password":      "94c2598d-a942-4386-817a-767c37974232",
    "method":        "aes-128-gcm",
    "local_address": "127.0.0.1",
    "local_port":    1080,
    "timeout":       60,
    "fast_open":     False,
}

async def start_ss():
    srv = Server(CONFIG)     # 启动 AsyncIO SS Relay
    await srv.serve()        # 内部启动事件循环

# 后台线程启动代理
threading.Thread(target=lambda: asyncio.run(start_ss()), daemon=True).start()
time.sleep(1)  # 等待本地 SOCKS5 就绪

# 2. requests 指向本地代理
proxies = {
    "http":  "socks5h://127.0.0.1:1080",
    "https": "socks5h://127.0.0.1:1080",
}

# 3. 发起请求
response = requests.get(
    "https://findbiz.nat.gov.tw/fts/query/QueryCmpyDetail/queryCmpyDetail.do",
    headers={
        "Referer": "https://findbiz.nat.gov.tw/fts/query/QueryList/queryList.do",
        "User-Agent": "Mozilla/5.0"
    },
    params={
        "objectId":"SEM4NDA3MTg1OQ==",
        "banNo":"84071859",
        "disj":"********************************",
        "fhl":"zh_TW"
    },
    verify=False,
    proxies=proxies,
    timeout=10
)
print(response.text)