import asyncio
import threading
import time
import requests
import urllib3
from aioshadowsocks import server as ss_server  # pip install aioshadowsocks

urllib3.disable_warnings()

CONFIG = {
    "server":       "main-central.earthsurface.cc",
    "server_port":  21153,
    "password":     "94c2598d-a942-4386-817a-767c37974232",
    "method":       "aes-128-gcm",
    "local_address":"127.0.0.1",
    "local_port":   1080,
    "timeout":      60,
    "fast_open":    False
}

async def start():
    srv = ss_server.Server(CONFIG)  # 创建 asyncio SS 服务实例
    await srv.serve()               # 启动事件循环

# 后台线程运行代理
threading.Thread(target=lambda: asyncio.run(start()), daemon=True).start()
time.sleep(1)

proxies = {
    "http":  "socks5h://127.0.0.1:1080",
    "https": "socks5h://127.0.0.1:1080",
}

response = requests.get(
    "https://findbiz.nat.gov.tw/fts/query/QueryCmpyDetail/queryCmpyDetail.do",
    headers={
        "Referer":"https://findbiz.nat.gov.tw/fts/query/QueryList/queryList.do",
        "User-Agent":"Mozilla/5.0"
    },
    params={
        "objectId":"SEM4NDA3MTg1OQ==",
        "banNo":"84071859",
        "disj":"********************************",
        "fhl":"zh_TW"
    },
    verify=False,
    proxies=proxies,
    timeout=10
)
print(response.text)