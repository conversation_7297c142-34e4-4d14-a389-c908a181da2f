import asyncio
import aiohttp
from python_asyncio并发编程.util import async_timed
from aiohttp import Client<PERSON>ession


@async_timed()
async def fetch_status(session: ClientSession, url: str, delay: int) ->str:
    ten_millis = aiohttp.ClientTimeout(total=0.5)
    try:
        # async with session.get(url, timeout=ten_millis) as result:
        await asyncio.sleep(delay=delay)
        async with session.get(url) as result:
            t = await result.text()
            return t
    except asyncio.TimeoutError as e:
        print("Timeout")


@async_timed()
async def main():
    async with aiohttp.ClientSession() as session:
        fetchers = [
            fetch_status(session, f"http://127.0.0.1:5000/{3}", delay=3),
            fetch_status(session, f"http://127.0.0.1:5000/{2}", delay=2),
            fetch_status(session, f"http://127.0.0.1:5000/{1}", delay=1)
        ]
        for task in asyncio.as_completed(fetchers):
            result = await task
            print(result)


if __name__ == '__main__':
    asyncio.run(main())
