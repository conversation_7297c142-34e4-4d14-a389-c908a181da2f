// Popup script for Switch Proxy Plugin
document.addEventListener('DOMContentLoaded', function() {
    // DOM elements
    const connectionStatus = document.getElementById('connection-status');
    const connectionIndicator = document.getElementById('connection-indicator');
    const currentProxyDiv = document.getElementById('current-proxy');
    const refreshBtn = document.getElementById('refresh-btn');
    const clearProxyBtn = document.getElementById('clear-proxy-btn');
    const reconnectBtn = document.getElementById('reconnect-btn');
    
    // Initialize popup
    updateStatus();
    
    // Event listeners
    refreshBtn.addEventListener('click', updateStatus);
    clearProxyBtn.addEventListener('click', clearProxy);
    reconnectBtn.addEventListener('click', reconnectWebSocket);
    
    // Update status display
    function updateStatus() {
        chrome.runtime.sendMessage({action: 'getStatus'}, function(response) {
            if (response) {
                updateConnectionStatus(response.connectionStatus, response.reconnectAttempts);
                updateProxyDisplay(response.currentProxy);
            }
        });
    }
    
    // Update connection status display
    function updateConnectionStatus(status, reconnectAttempts = 0) {
        connectionIndicator.className = 'indicator';

        switch (status) {
            case 'connected':
                connectionStatus.textContent = '已连接';
                connectionStatus.style.color = '#4CAF50';
                connectionIndicator.classList.add('connected');
                break;
            case 'disconnected':
                if (reconnectAttempts > 0) {
                    connectionStatus.textContent = `重连中... (${reconnectAttempts}/10)`;
                    connectionStatus.style.color = '#ff9800';
                    connectionIndicator.classList.add('error');
                } else {
                    connectionStatus.textContent = '未连接';
                    connectionStatus.style.color = '#f44336';
                    connectionIndicator.classList.add('disconnected');
                }
                break;
            case 'error':
                connectionStatus.textContent = '连接错误';
                connectionStatus.style.color = '#ff9800';
                connectionIndicator.classList.add('error');
                break;
            default:
                connectionStatus.textContent = '未知状态';
                connectionStatus.style.color = '#6c757d';
        }
    }
    
    // Update proxy display
    function updateProxyDisplay(proxy) {
        if (proxy) {
            currentProxyDiv.innerHTML = `
                <div class="proxy-details">
                    <div class="proxy-item">
                        <span class="proxy-label">协议:</span> ${proxy.scheme || 'http'}
                    </div>
                    <div class="proxy-item">
                        <span class="proxy-label">主机:</span> ${proxy.host}
                    </div>
                    <div class="proxy-item">
                        <span class="proxy-label">端口:</span> ${proxy.port}
                    </div>
                </div>
            `;
        } else {
            currentProxyDiv.innerHTML = '<div class="no-proxy">未设置代理</div>';
        }
    }
    
    // Clear proxy
    function clearProxy() {
        chrome.runtime.sendMessage({action: 'clearProxy'}, function(response) {
            if (response && response.success) {
                showNotification('代理已清除', 'success');
                setTimeout(updateStatus, 500);
            } else {
                showNotification('清除代理失败', 'error');
            }
        });
    }
    
    // Reconnect WebSocket
    function reconnectWebSocket() {
        chrome.runtime.sendMessage({action: 'reconnect'}, function(response) {
            if (response && response.success) {
                showNotification('正在重新连接...', 'info');
                setTimeout(updateStatus, 1000);
            } else {
                showNotification('重连失败', 'error');
            }
        });
    }
    
    // Show notification
    function showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            padding: 10px 15px;
            border-radius: 4px;
            color: white;
            font-size: 12px;
            z-index: 1000;
            transition: all 0.3s ease;
        `;
        
        // Set background color based on type
        switch (type) {
            case 'success':
                notification.style.background = '#4CAF50';
                break;
            case 'error':
                notification.style.background = '#f44336';
                break;
            case 'info':
                notification.style.background = '#2196F3';
                break;
            default:
                notification.style.background = '#6c757d';
        }
        
        // Add to document
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    
    // Auto-refresh status every 5 seconds
    setInterval(updateStatus, 5000);
});
