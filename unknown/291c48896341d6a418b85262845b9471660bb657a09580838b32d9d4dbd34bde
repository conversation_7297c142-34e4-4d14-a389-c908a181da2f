import asyncio
import aiohttp
from aiohttp import ClientSession
from python_asyncio并发编程.util import async_timed
from loguru import logger


@async_timed()
async def fetch_status(session: ClientSession, url: str, delay: int) -> str:
    async with session.get(url) as result:
        t = await result.text()
        return t


@async_timed()
async def main():
    async with aiohttp.ClientSession() as session:
        fetchers = [
            asyncio.create_task(fetch_status(session, f'http://127.0.0.1:5000/{1}', 1)),
            asyncio.create_task(fetch_status(session, f'http://127.0.0.1:5000/{3}', 3)),
            asyncio.create_task(fetch_status(session, f'http://127.0.0.1:50003', 2)),
        ]

        done, pending = await asyncio.wait(fetchers)

        print(f'Done task count: {len(done)}')
        print(f'Pending task count: {len(pending)}')

        for done_task in done:
            if done_task.exception() is None:
                print(done_task.result())
            else:
                logger.error('Failed to fetch {}', done_task.exception())


if __name__ == '__main__':
    asyncio.run(main())
