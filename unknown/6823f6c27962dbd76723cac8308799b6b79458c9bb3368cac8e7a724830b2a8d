import asyncio
import socket
from types import TracebackType
from typing import Type, Optional


class ConnectedSocket:

    def __init__(self, server_socket):
        self._connection = None
        self._server_socket = server_socket

    async def __aenter__(self):
        print("进入上下文管理器，等待连接")
        loop = asyncio.get_event_loop()
        connection, address = await loop.sock_accept(self._server_socket)
        self._connection = connection
        print("接受一个连接")
        return self._connection

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        print("退出上下文管理器")
        self._connection.close()
        print("连接关闭")


async def main():
    loop = asyncio.get_event_loop()

    server_socket = socket.socket()
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    server_address = ('127.0.0.1', 8000)
    server_socket.setblocking(False)
    server_socket.bind(server_address)
    server_socket.listen()

    async with ConnectedSocket(server_socket) as connection:
        data = await loop.sock_recv(connection, 1024)
        print(data)


if __name__ == '__main__':
    asyncio.run(main())
