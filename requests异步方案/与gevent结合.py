import time
import gevent
import gevent.monkey

gevent.monkey.patch_all()

import requests


def fetch(url):
    response = requests.get(url, timeout=1)
    print(f"Fetch successful {response.status_code}")
    return response.status_code


if __name__ == '__main__':
    urls = ['https://www.baidu.com' for _ in range(30)]
    a = time.time()
    jobs = [gevent.spawn(fetch, url) for url in urls]
    gevent.joinall(jobs)
    print(time.time() - a)
