import asyncio
import time
import concurrent.futures
import requests


def fetch(method, url, **kwargs):
    return requests.request(method, url, **kwargs)


async def main():
    url = "http://www.baidu.com"
    tasks = [loop.run_in_executor(executor, fetch, 'GET', url) for _ in range(200)]
    for task in asyncio.as_completed(tasks):
        print(await task)


t = time.time()
loop = asyncio.get_event_loop()
executor = concurrent.futures.ThreadPoolExecutor(500)
loop.run_until_complete(main())
print(time.time() - t)
