def generator1():
    total = 0
    while True:
        x = yield
        print('加', x)
        if not x:
            break
        total += x
    return total


def generator2():
    while True:
        total = yield from generator1()
        print('加和总数是：', total)


# def main():
#     # g1 = generator1()
#     # print('g1', g1.send(None))
#     # print('g1', g1.send(2))
#     # print('g1', g1.send(3))
#     # print('g1', g1.send(None))
#     g2 = generator2()
#     print(g2.send(None))
#     print(g2.send(2))
#     print(g2.send(3))
#     print(g2.send(None))


# main()
def a():
    for i in generator1():
        yield i


# todo 为什么这样写不能传入2
b = a()
print(b.send(None))
print(b.send(2))
print(b.send(3))
print(b.send(None))
