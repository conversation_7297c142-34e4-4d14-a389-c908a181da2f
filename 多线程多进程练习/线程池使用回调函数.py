from concurrent.futures import ThreadPoolExecutor
import os, time, random
import threading
import requests


def task(url):
    print(f'{threading.current_thread().getName()}:{os.getpid()} fetching {url}')
    response = requests.get(url)
    return {'url': url, 'response': response.text}


def parse(future):
    result = future.result()
    print(f'{threading.current_thread().getName()} parse {result["url"]}')
    print(result['response'][:20])


if __name__ == '__main__':
    a = time.time()
    pool = ThreadPoolExecutor()
    urls = [
        'https://www.baidu.com',
        'https://www.baidu.com',
        'https://www.baidu.com',
        'https://www.baidu.com',
        'https://www.baidu.com',
        'https://www.baidu.com',
    ]
    for url in urls:
        pool.submit(task, url).add_done_callback(parse)

    pool.shutdown()
    print('time: ', time.time() - a)
    print('主: ', os.getpid())
