import time
import asyncio


@asyncio.coroutine
def taskio1():
    print('开始运行IO任务1....')
    yield from asyncio.sleep(2)
    print('IO任务1已经完成，耗时2s')
    return taskio1.__name__


@asyncio.coroutine
def taskio2():
    print('开始运行IO任务2....')
    yield from asyncio.sleep(3)
    print('IO任务2已完成，耗时3s')
    return taskio2.__name__


@asyncio.coroutine
def main():
    tasks = [taskio1(), taskio2()]
    done, pending = yield from asyncio.wait(tasks)
    for r in done:
        print('协程无序返回值：' + r.result())


if __name__ == '__main__':
    start = time.time()
    loop = asyncio.get_event_loop()
    try:
        loop.run_until_complete(main())
    finally:
        loop.close()
    print('所有IO任务总耗时%.5f秒' % float(time.time() - start))
