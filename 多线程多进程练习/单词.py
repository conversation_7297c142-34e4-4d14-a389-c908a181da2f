from bs4 import BeautifulSoup
import re
from docx import Document

html = open(r"C:\Users\<USER>\Downloads\四级核心词汇690.html", 'r', encoding='utf-8').read()
soup = BeautifulSoup(html, 'lxml')
spans = soup.find_all(name='span', class_='T2')
list1 = []
for span in spans:
    list1.extend(span.text.split("  "))

list2 = []
for i, j in enumerate(list1):
    b = re.sub(r'\s', ' ', j)
    b = re.findall(r'[a-z]+', b)
    # print(b)
    try:
        list2.append(b[0])
    except:
        pass
# print(list2)
# print(len(list2))

list3 = []
for i in range(0, len(list2), 5):
    list3.append(list2[i:i + 5])
print(list3)

document = Document()
for i in list3:
    temp = '    '.join(i)
    document.add_paragraph(temp)
document.save(r'C:\Users\<USER>\Downloads\1.docx')
