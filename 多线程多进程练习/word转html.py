from pydocx import PyDocX
from win32com import client
import mammoth

word = client.Dispatch("Word.Application")
doc = word.Documents.Open(r"C:\Users\<USER>\Downloads\大学英语四级核心词汇690个附高频词汇表.doc")
doc.SaveAs(r"C:\Users\<USER>\Downloads\大学英语四级核心词汇690个附高频词汇表.docx", 16)
doc.Close()
word.Quit()

# html = PyDocX.to_html(r"C:\Users\<USER>\Downloads\四级核心词汇690.doc")
# f = open(r"C:\Users\<USER>\Downloads\四级核心词汇690.html", 'w', encoding='utf-8')
# f.write(html)
# f.close()
