import multiprocessing
import os, time, random


# def long_time_last(name):
#     print('Run task %s (%s)...' % (name, os.getpid()))
#     start = time.time()
#     time.sleep(random.random() * 3)
#     end = time.time()
#     print('Task %s runs %0.2f seconds.' % (name, (end - start)))
#
#
# if __name__ == '__main__':
#     print('Parent process %s.' % os.getpid())
#     p = multiprocessing.Pool(4)
#     for i in range(5):
#         p.apply_async(long_time_last, args=(i,))
#     print('Waiting for all subprocesses done...')
#     p.close()
#     p.join()
#     print('All subprocesses done.')


def write(q):
    print('Process to write·····')
    for i in ['A', 'B', 'C']:
        q.put(i)
        print(f'put {i} to q')
        time.sleep(random.random())


def read(q):
    print('Process to read·····')
    while True:
        data = q.get(True)
        print(f'read {data} from q')


if __name__ == '__main__':
    Q = multiprocessing.Queue()
    w = multiprocessing.Process(target=write, args=(Q,))
    r = multiprocessing.Process(target=read, args=(Q,))
    w.start()
    r.start()

    w.join()
    r.terminate()
