from concurrent.futures import ThreadPoolExecutor
import threading
import time
import os
import random


def task(n):
    print(f'{threading.current_thread().getName()}:{os.getpid()} is running')
    time.sleep(random.random())
    return n * n


if __name__ == '__main__':
    a = time.time()
    # with Thread<PERSON>oolExecutor() as pool:
    #     futures = [pool.submit(task, i) for i in range(10)]
    # print('Waiting: ', time.time() - a)
    # print([future.result() for future in futures])

    # ! 同步执行情况
    # pool = ThreadPoolExecutor()
    # for i in range(10):
    #     res = pool.submit(task, i).result()
    #     print(res)
    #
    # print('Waiting: ', time.time() - a)

    # ! map函数 返回有序的结果
    with ThreadPoolExecutor() as pool:
        result = pool.map(task, range(20))

    print('Waiting: ', time.time() - a)
    print(list(result))
