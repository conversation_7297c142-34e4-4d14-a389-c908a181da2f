from concurrent.futures import <PERSON><PERSON>oolExecutor
import os
import random
import time


def task(n):
    print(f'{os.getpid()} is running')
    time.sleep(1)
    return n * n


if __name__ == '__main__':
    a = time.time()
    # with ProcessPoolExecutor() as pool:
    #     futures = [pool.submit(task, i) for i in range(10)]
    pool = ProcessPoolExecutor()
    futures = [pool.submit(task, i) for i in range(10)]
    pool.shutdown()  # ! 完成所有任务后关闭pool
    print('time: ', time.time() - a)
    print([future.result() for future in futures])
