import asyncio
import threading


# # @asyncio.coroutine
# async def hello():
#     print("Hello world!")
#     # 异步调用asyncio.sleep(1):
#     # r = yield from asyncio.sleep(1)
#     await asyncio.sleep(1)
#     print("Hello again!")
#
#
# # 获取EventLoop:
# loop = asyncio.get_event_loop()
# # 执行coroutine
# loop.run_until_complete(hello())
# loop.close()


async def hello(n):
    print('%d Hello world!(%s)' % (n, threading.currentThread()))
    await asyncio.sleep(1)
    print('%d Hello again!(%s)' % (n, threading.currentThread()))


loop = asyncio.get_event_loop()
tasks = [hello(1), hello(2)]
loop.run_until_complete(asyncio.wait(tasks))
loop.close()
