from loguru import logger
import sys

logger.add("interface_log_{time}.log", rotation="500MB", encoding="utf-8",
           enqueue=True, compression="zip", retention="10 days")
# logger.add(sink='log.log',
#            format="{time} - {level} - {message}", level="INFO", colorize=True)
# logger.add(sys.stdout, colorize=True, format="<green>{time}</green> <level>{message}</level>")

logger.info("jiejie")
logger.debug("shushu")
logger.warning("jiejie")
logger.error("shushu")
logger.critical("jiejie")

logger.info('I love {} {feature}', 'jiejie', feature='forever')
