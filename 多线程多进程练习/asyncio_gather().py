import time
import asyncio


async def taskio1():
    print('开始运行IO任务1....')
    await asyncio.sleep(3)
    print('IO任务1已完成，耗时3s')
    return taskio1.__name__


async def taskio2():
    print('开始运行IO任务2....')
    await asyncio.sleep(2)
    print('IO任务2已完成，耗时2s')
    return taskio2.__name__


async def main():
    results = await asyncio.gather(taskio1(), taskio2())
    print(results)


if __name__ == '__main__':
    start = time.time()
    loop = asyncio.get_event_loop()
    try:
        loop.run_until_complete(main())
    finally:
        loop.close()
    print('所有IO任务总耗时%.5f秒' % float(time.time() - start))
