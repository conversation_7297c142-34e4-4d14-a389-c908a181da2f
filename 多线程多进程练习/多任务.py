import time
import threading

num = 0

lock_ = threading.Lock()


def sing():
    global num
    for i in range(1000000):
        # print("我喜欢洁洁")
        lock_.acquire()
        num += 1
        lock_.release()
        # time.sleep(0.4)


def dance():
    global num
    for i in range(1000000):
        lock_.acquire()
        num += 1
        lock_.release()
        # print('我喜欢姝姝')
        # time.sleep(0.4)


# class A(threading.Thread):
#     def run(self):
#         for i in range(3):
#             print('我喜欢姝姝')
#             time.sleep(0.4)


def run():
    # sing()
    # dance()
    a = threading.Thread(target=sing)
    b = threading.Thread(target=dance)
    a.start()
    b.start()
    print(threading.enumerate())
    print('num:', num)
    time.sleep(3)
    print(threading.enumerate())
    print('num', num)


if __name__ == '__main__':
    run()
# a = A()
# a.start()
# print(threading.enumerate())
