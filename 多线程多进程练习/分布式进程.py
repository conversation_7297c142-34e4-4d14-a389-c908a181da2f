# todo https://blog.csdn.net/qq_19707521/article/details/107690206
from multiprocessing.managers import BaseManager
from multiprocessing import freeze_support, Queue

task_number = 10

task_queue = Queue(task_number)
result_queue = Queue(task_number)


def get_task():
    return task_queue


def get_result():
    return result_queue


class QueueManager(BaseManager):
    pass


def win_run():
    QueueManager.register('get_task_queue', callable=get_task)
    QueueManager.register('get_result_queue', callable=get_result)

    manager = QueueManager(address=('127.0.0.1', 8001), authkey='jie'.encode())
    manager.start()

    try:
        task = manager.get_task_queue()
        result = manager.get_result_queue()

        for url in ["ImageURl_" + str(i) for i in range(10)]:
            print('url is %s' % url)
            task.put(url)

        print('try get result')
        for i in range(10):
            print('result is %s' % result.get(timeout=10))

    except  Exception as e:
        print('Manager error')

    finally:
        manager.shutdown()


if __name__ == '__main__':
    freeze_support()
    win_run()
