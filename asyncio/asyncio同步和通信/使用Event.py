import asyncio


async def wait_for_event(event):
    print("Waiting for event...")
    await event.wait()
    print("Event received!")


async def set_event(event):
    print("Setting event...")
    await asyncio.sleep(2)
    event.set()


async def main():
    event = asyncio.Event()
    task1 = asyncio.create_task(wait_for_event(event))
    task2 = asyncio.create_task(set_event(event))
    await asyncio.gather(task1, task2)


asyncio.run(main())
