import asyncio


async def access_resource(semaphore):
    async with semaphore:
        print("Accessing resource...")
        await asyncio.sleep(2)
        print("Resource access complete!")


async def main():
    semaphore = asyncio.Semaphore(2)
    tasks = []
    for _ in range(3):
        task = asyncio.create_task(access_resource(semaphore))
        tasks.append(task)
    await asyncio.gather(*tasks)


asyncio.run(main())
