import asyncio


async def acquire_lock(lock):
    print("Trying to acquire lock...")
    async with lock:
        print("Lock acquired!")
        await asyncio.sleep(2)
        print("Lock released!")


async def main():
    lock = asyncio.Lock()
    tasks = []
    for _ in range(3):
        task = asyncio.create_task(acquire_lock(lock))
        tasks.append(task)
    await asyncio.gather(*tasks)


asyncio.run(main())
