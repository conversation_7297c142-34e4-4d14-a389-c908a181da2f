import asyncio


async def wait_for_condition(condition):
    print("Waiting for condition...")
    async with condition:
        await condition.wait()
        print("Condition satisfied!")


async def set_condition(condition):
    print("Setting condition...")
    await asyncio.sleep(2)
    async with condition:
        condition.notify_all()


async def main():
    condition = asyncio.Condition()
    task1 = asyncio.create_task(wait_for_condition(condition))
    task2 = asyncio.create_task(set_condition(condition))
    await asyncio.gather(task1, task2)


asyncio.run(main())
