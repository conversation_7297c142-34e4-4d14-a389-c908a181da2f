import asyncio


async def produce(queue):
    for i in range(5):
        print(f"Producing item {i}")
        await queue.put(i)
        await asyncio.sleep(1)


async def consume(queue):
    while True:
        item = await queue.get()
        print(f"Consuming item {item}")
        queue.task_done()
        if item is None:
            break
        await asyncio.sleep(1)


async def main():
    queue = asyncio.Queue()
    producer_task = asyncio.create_task(produce(queue))
    consumer_task = asyncio.create_task(consume(queue))
    await producer_task
    await queue.join()
    consumer_task.cancel()


asyncio.run(main())
