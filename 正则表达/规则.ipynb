#%%
#> 分组 (?P<name> )
import re


def func(matched):
    value = matched.group('name')
    return value * 2


content = 'dw22cef'
print(re.sub(r'(?P<name>\d+)', func, content))
#%%
#! 捕获分组
import re
a = '123abc456'
pattern = '([0-9]*)([a-z]*)([0-9]*)'
print(re.search(pattern, a).group(1))
#%%
#! 不捕获
import re
a = '123abc456'
pattern = '(?:[0-9]*)(?:[a-z]*)(?:[0-9]*)'
print(re.search(pattern, a).group())
#%%
