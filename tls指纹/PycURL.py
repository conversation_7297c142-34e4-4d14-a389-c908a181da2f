import pycurl
import json

# 打印看下pycurl的版本是否和文章中的一致
print(pycurl.version)
result = 0


def my_func(data):
    global result
    d = json.loads(data)['data']
    for i in d:
        result += int(i['value'])
    print(result)


headers = [
    'Host: www.python-spider.com',
    'origin: https://www.python-spider.com',
    'referer: https://www.python-spider.com/challenge/29',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
]
cookies = {
    "no-alert": "true",
    "sessionid": "gqavfp78ag7809t14iq38c4l9scequ20",
    "__yr_token__": "b301cDBgTb0oKHBp6Oks9KGYjV1oUfSIyYlIvUFh6QExoSSVLT1d6ZH8CKH0kDVUeXEc9Ql4"
                    "/QRBTTBtoHVlJH3tzaB8bG0MfT3h2CywVMk47Y091Ug8vGnwPWAZYF3ZrXAZPABUMG3kJWQNBYRY="
}

curl = pycurl.Curl()
curl.setopt(
    curl.SSL_CIPHER_LIST,
    'TLS_AES_128_GCM_SHA256,TLS_AES_256_GCM_SHA384,TLS_CHACHA20_POLY1305_SHA256,ECDHE-ECDSA-AES128-GCM-SHA256,ECDHE-RSA-AES128-GCM-SHA256,'
    'ECDHE-ECDSA-AES256-GCM-SHA384,ECDHE-RSA-AES256-GCM-SHA384,ECDHE-ECDSA-CHACHA20-POLY1305,ECDHE-RSA-CHACHA20-POLY1305,ECDHE-RSA-AES128-SHA,'
    'ECDHE-RSA-AES256-SHA,AES128-GCM-SHA256,AES256-GCM-SHA384,AES128-SHA,AES256-SHA'
)
curl.setopt(curl.SSLVERSION, curl.SSLVERSION_TLSv1_2)

curl.setopt(pycurl.HTTPHEADER, headers)
curl.setopt(pycurl.COOKIE, "; ".join([f"{k}={v}" for k, v in cookies.items()]))
curl.setopt(pycurl.WRITEFUNCTION, my_func)  # todo 处理数据返回的回调事件

curl.setopt(curl.HTTP_VERSION, curl.CURL_HTTP_VERSION_2_0)  # todo 设置 HTTP/2 版本
curl.setopt(curl.SSL_ENABLE_NPN, 0)  # todo 用于协商客户端和服务器之间的通信协议（如 HTTP/1.1, HTTP/2）
curl.setopt(curl.SSL_ENABLE_ALPS, 1)  # todo 用于提前传输应用层协议的设置，如 HTTP/2 和 HTTP/3的配置
curl.setopt(pycurl.HTTP2_PSEUDO_HEADERS_ORDER, "masp")  # todo 用于设置 HTTP/2 请求头字段的顺序

# curl.setopt(curl.SSL_FALSESTART, 0) #todo 允许在握手完成前就开始发送加密数据
curl.setopt(curl.SSL_CERT_COMPRESSION, "brotli")  # todo 允许对在 TLS 握手过程中交换的证书进行压缩 "brotli" 压缩算法


# url = 'https://www.python-spider.com/api/challenge29'
url = 'https://www.python-spider.com/api/challenge22'
for i in range(1, 101):
    data = "page={}".format(i)
    curl.setopt(pycurl.POSTFIELDS, data)
    # curl.setopt(curl.PROXY, 'https://127.0.0.1:xxxx')
    curl.setopt(pycurl.URL, url)
    curl.perform()

curl.close()

"""
import sys
import os

base = os.path.join("/usr/local", "lib", "libcurl-impersonate-chrome.so")
with open(base, "rb") as inp, open("/usr/lib/libcurl-impersonate-chrome.so.4","wb") as out:
    data = inp.read()
    out.write(data)
"""