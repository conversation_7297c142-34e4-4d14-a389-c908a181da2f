import tls_client

session = tls_client.Session(

    ja3_string="771,4865-4866-4867-49195-49199-49196-49200-52393-52392-49171-49172-156-157-47-53,0-23-65281-10-11-35-16-5-13-18-51-45-43-27-17513,29-23-24,0",

    h2_settings={

        "HEADER_TABLE_SIZE": 65536,

        "MAX_CONCURRENT_STREAMS": 1000,

        "INITIAL_WINDOW_SIZE": 6291456,

        "MAX_HEADER_LIST_SIZE": 262144

    },

    h2_settings_order=[

        "HEADER_TABLE_SIZE",

        "MAX_CONCURRENT_STREAMS",

        "INITIAL_WINDOW_SIZE",

        "MAX_HEADER_LIST_SIZE"

    ],

    supported_signature_algorithms=[

        "ECDSAWithP256AndSHA256",

        "PSSWithSHA256",

        "PKCS1WithSHA256",

        "ECDSAWithP384AndSHA384",

        "PSSWithSHA384",

        "PKCS1WithSHA384",

        "PSSWithSHA512",

        "PKCS1WithSHA512",

    ],

    supported_versions=["GREASE", "1.3", "1.2"],

    key_share_curves=["GREASE", "X25519"],

    cert_compression_algo="brotli",

    pseudo_header_order=[

        ":method",

        ":authority",

        ":scheme",

        ":path"

    ],

    connection_flow=15663105,

    header_order=[

        "accept",

        "user-agent",

        "accept-encoding",

        "accept-language"

    ]

)

res = session.post(

    "https://www.example.com/",

    headers={

        "key1": "value1",

    },

    json={

        "key1": "key2"

    }

)
