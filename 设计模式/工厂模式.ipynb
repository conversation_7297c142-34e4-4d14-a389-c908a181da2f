#%%
#* 简单工厂模式
from abc import ABCMeta, abstractmethod


class Animal(metaclass=ABCMeta):
    @abstractmethod
    def do_say(self):
        pass


class Dog(Animal):
    def do_say(self):
        print('wang wang')


class Cat(Animal):
    def do_say(self):
        print('miao miao')


class Factory(object):
    def create_sounds(self, object_type):
        return eval(object_type)().do_say()


if __name__ == '__main__':
    factory = Factory()
    object_type1 = input("Which animal should make_sound Dog or Cat?")
    factory.create_sounds(object_type1)
#%%
#* 工厂方法模式
from abc import ABCMeta, abstractmethod


class Section(metaclass=ABCMeta):
    @abstractmethod
    def describe(self):
        pass


class PersonalSection(Section):
    def describe(self):
        print('Personal Section')


class AlbumSection(Section):
    def describe(self):
        print('Album Section')


class PatentSection(Section):
    def describe(self):
        print('patent Section')


class PublicationSection(Section):
    def describe(self):
        print('Publication Section')


class Profile(metaclass=ABCMeta):
    def __init__(self):
        self.sections = []
        self.create_sections()

    @abstractmethod
    def create_sections(self):
        pass

    def add_section(self, section):
        self.sections.append(section)

    def get_sections(self):
        return self.sections


class FaceBook(Profile):
    def create_sections(self):
        self.add_section(AlbumSection())
        self.add_section(PersonalSection())


class LinkedIn(Profile):
    def create_sections(self):
        self.add_section(PersonalSection())
        self.add_section(PublicationSection())
        self.add_section(PatentSection())


if __name__ == '__main__':
    profile_type = input("Which Profile you'd like to create?[LinkedIn or FaceBook]")
    profile = eval(profile_type)()
    print('creating profile...', type(profile).__name__)
    print('profile has sections --', profile.get_sections())
#%%
#* 抽象工厂模式
from abc import ABCMeta, abstractmethod


class VegPizza(metaclass=ABCMeta):
    @abstractmethod
    def prepare(self):
        pass
    

class NonVegPizza(metaclass=ABCMeta):
    @abstractmethod
    def serve(self, vegPizza):
        pass


class DeluxVeggiePizza(VegPizza):
    def prepare(self):
        print('prepare DeluxVeggiePizza')


class ChickenPizza(NonVegPizza):
    def serve(self, vegPizza):
        print(type(self).__name__ + 'is served with chicken on ' + type(vegPizza).__name__)


class MexicanVegPizza(VegPizza):
    def prepare(self):
        print('prepare MexicoVegPizza')


class HamPizza(NonVegPizza):
    def serve(self, vegPizza):
        print(type(self).__name__ + 'is served with ham on' + type(vegPizza).__name__)


class PizzaFactory(metaclass=ABCMeta):
    @abstractmethod
    def createVegPizza(self):
        pass

    @abstractmethod
    def createNonVegPizza(self):
        pass


class IndianPizzaFactory(metaclass=ABCMeta):
    def createVegPizza(self):
        return DeluxVeggiePizza()

    def createNonVegPizza(self):
        return ChickenPizza()


class USPizzaFactory(metaclass=ABCMeta):
    def createVegPizza(self):
        return MexicanVegPizza()

    def createNonVegPizza(self):
        return HamPizza()


class PizzaStore:
    def __init__(self):
        pass

    def makePizzas(self):
        for _ in [USPizzaFactory()]:
            self.factory = _
            self.NonVegPizza = self.factory.createNonVegPizza()
            self.VegPizza = self.factory.createVegPizza()
            self.VegPizza.prepare()
            self.NonVegPizza.serve(self.NonVegPizza)


pizza = PizzaStore()
pizza.makePizzas()
#%%
