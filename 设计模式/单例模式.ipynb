#%%
#* 方法一 重写 __new__ 方法

class SingleModel(object):
    _instance = None

    def __new__(cls, *args, **kwargs):
        if not SingleModel._instance:
            cls._instance = super().__new__(cls)
        return cls._instance


a = SingleModel()
b = SingleModel()
print('a', a)
print('b', b)
#%%
#* 方法二 闭包定义装饰器

def single(cls):
    _instance = {}

    def func(*args, **kwargs):
        if cls not in _instance:
            _instance[cls] = cls(*args, **kwargs)
        return _instance[cls]

    return func


@single
class SingleModel:

    def __init__(cls, name):
        cls.name = name


a = SingleModel('jiejie')
b = SingleModel('shushu')
print(id(a))
print(id(b))
print(a == b)
#%%
#* 适用于多线程的单例模式

import threading
import time
import random


class Singleton:
    _singleton_lock = threading.Lock()

    def __init__(cls):
        pass

    def __new__(cls, *args, **kwargs):
        if not hasattr(Singleton, '_instance'):
            with cls._singleton_lock:
                if not hasattr(Singleton, '_instance'):
                    Singleton._instance = super().__new__(cls)

        return Singleton._instance


def task(n):
    time.sleep(random.randint(1, 4))
    aa = Singleton()
    print('aa: ', aa, n)


if __name__ == '__main__':
    for i in range(5):
        run = threading.Thread(target=task, args=(i,))
        run.start()#%%
#* 方法一 重写 __new__ 方法

class SingleModel(object):
    _instance = None

    def __new__(cls, *args, **kwargs):
        if not SingleModel._instance:
            cls._instance = super().__new__(cls)
        return cls._instance


a = SingleModel()
b = SingleModel()
print('a', a)
print('b', b)
#%%
#* 方法二 闭包定义装饰器

def single(cls):
    _instance = {}

    def func(*args, **kwargs):
        if cls not in _instance:
            _instance[cls] = cls(*args, **kwargs)
        return _instance[cls]

    return func


@single
class SingleModel:

    def __init__(cls, name):
        cls.name = name


a = SingleModel('jiejie')
b = SingleModel('shushu')
print(id(a))
print(id(b))
print(a == b)
#%%
#* 适用于多线程的单例模式

import threading
import time
import random


class Singleton:
    _singleton_lock = threading.Lock()

    def __init__(cls):
        pass

    def __new__(cls, *args, **kwargs):
        if not hasattr(Singleton, '_instance'):
            with cls._singleton_lock:
                if not hasattr(Singleton, '_instance'):
                    Singleton._instance = super().__new__(cls)

        return Singleton._instance


def task(n):
    time.sleep(random.randint(1, 4))
    aa = Singleton()
    print('aa: ', aa, n)


if __name__ == '__main__':
    for i in range(5):
        run = threading.Thread(target=task, args=(i,))
        run.start()

#%%
# 元类实现
class Singleton(type):
    def __init__(cls, *args, **kwargs):
        cls.single_object = None
        super().__init__(*args, **kwargs)

    def __call__(cls, *args, **kwargs):
        if cls.single_object is None:
            cls.single_object = super().__call__(*args, **kwargs)

        return cls.single_object


class A(metaclass=Singleton):
    pass


if __name__ == '__main__':
    a = A()
    b = A()
    print(id(a))
    print(id(b))
