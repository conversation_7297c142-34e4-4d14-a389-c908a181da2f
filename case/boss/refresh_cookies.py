import asyncio
import random
import re
from pathlib import Path

import requests
from DrissionPage import Chromium, ChromiumOptions
from loguru import logger

from process_manage import start_proxy_server, stop_proxy_server
from proxy_switch import ProxySwitchClient
from gt_challege import verify_nine
from position_list import fetch_job_list


def update_websocket_url(host: str = "localhost", port: int = 8765):
    try:
        background_js_path = Path.cwd().parent.parent.parent / "apps/dingliangyi/switch_proxy_plugin/background.js"
        with open(background_js_path, 'r', encoding='utf-8') as f:
            content = f.read()

        new_url = f"ws://{host}:{port}"
        pattern = r"const\s+WS_SERVER_URL\s*=\s*['\"]ws://[^'\"]*['\"];"
        replacement = f"const WS_SERVER_URL = '{new_url}';"

        new_content, count = re.subn(pattern, replacement, content)

        if count == 0:
            logger.error("未找到匹配的WebSocket URL配置行")
            return False

        with open(background_js_path, 'w', encoding='utf-8') as f:
            f.write(new_content)

        logger.info(f"成功更新WebSocket URL: {new_url} (替换了 {count} 处)")
        return True
    except Exception as e:
        logger.error(f"更新WebSocket URL时出错: {e}")
        return False


def set_proxy(client: ProxySwitchClient = None):
    async def async_set_proxy():
        try:
            await client.connect()
            success, message = await client.switch_proxy(host, port, "http")
            logger.info(f"Proxy switch result: {success}, message: {message}")
        finally:
            await client.disconnect()

    IP = get_long_proxy()['http']
    logger.info(f"Using proxy: {IP}")
    host, port = IP.split('//')[1].split(':')[0], int(IP.split(':')[2])
    asyncio.run(async_set_proxy())
    return IP


def get_long_proxy():
    res = requests.get('http://*************:8015/long-proxy')
    proxy = random.choice(res.text.split('\r\n'))
    return {
        'http': f'http://{proxy}',
        'https': f'http://{proxy}'
    }


def init_co(port=9222):
    co = ChromiumOptions().set_local_port(port)
    co.ignore_certificate_errors()
    co.set_load_mode('none')  # eager none
    co.set_argument('--no-sandbox')
    co.set_argument("--disable-gpu")
    # co.set_argument('--disable-features=DisableLoadExtensionCommandLineSwitch')
    # co.add_extension('/Users/<USER>/Downloads/pygs-work-parent/apps/dingliangyi/switch_proxy_plugin')
    # co.set_browser_path('/Applications/Microsoft Edge.app')
    # co.incognito(True)
    # co.headless(True)
    # co.set_argument('--auto-open-devtools-for-tabs')  # 自动打开开发者工具
    return co


def get_tab(browser: Chromium, websocket_client):
    tab = browser.latest_tab
    tab.set.cookies.clear()
    # tab.run_cdp('Network.setCacheDisabled', cacheDisabled=True)
    # tab.run_cdp('Runtime.enable')
    # tab.run_cdp('Log.enable')
    # tab.set.load_mode('none')
    IP = set_proxy(websocket_client)  # 设置代理
    tab.set.user_agent(tab.user_agent.replace('Headless', ''))  # 去掉Headless
    tab.listen.start([
        'search/joblist.json',
        'captcha/gettype'
    ])
    return tab, IP


def boss(browser: Chromium, websocket_client):
    tab, IP = get_tab(browser, websocket_client)
    tab.get('https://www.zhipin.com/web/geek/jobs?query=%E7%88%AC%E8%99%AB', timeout=5)

    packets = tab.listen.wait(2, fit_count=False, timeout=10)  # 等待数据包
    retry_count = 0
    max_retries = 3

    while not packets and retry_count < max_retries:
        retry_count += 1
        logger.info(f"No packets received, retrying... ({retry_count}/{max_retries})")
        tab.listen.stop()
        tab.listen.start()
        IP = set_proxy(websocket_client)  # 重新设置代理
        tab.get('https://www.zhipin.com/web/geek/jobs?query=%E7%88%AC%E8%99%AB', timeout=5)
        packets = tab.listen.wait(2, fit_count=False, timeout=10)  # 等待数据包

    # 检查是否成功获取到数据包
    if not packets:
        logger.error("Failed to receive packets after maximum retries")
        browser.quit()
        return False

    for packet in packets:
        if 'captcha/gettype' in packet.url:
            tab.listen.stop()  # 停止监听
            if verify_nine(IP, packet.response.body['zpData']['startCaptcha']):
                tab.listen.start()
                tab.get('https://www.zhipin.com/web/geek/jobs?query=%E7%88%AC%E8%99%AB', timeout=5)
                packets = tab.listen.wait(2, fit_count=False, timeout=10)
                # 再次检查验证后的数据包
                if not packets:
                    logger.error("Failed to receive packets after captcha verification")
                    browser.quit()
                    return False
                break
        logger.info(f"Packet received: {packet.url} {packet.response.status} {packet.response.raw_body} {packet.request.cookies}")

    tab.stop_loading()  # 主动停止加载

    # 确保有足够的数据包用于提取cookies
    if len(packets) < 2:
        logger.error("Insufficient packets received for cookie extraction")
        browser.quit()
        return False

    cookies = {
        packet_cookie['name']: packet_cookie['value']
        for packet_cookie in packets[1].request.cookies
    }

    fetch_job_list(cookies, IP, packets[1].url)
    logger.success('Browser session completed successfully.')
    return True


def process():
    while True:
        try:
            port = 8675
            browser = None
            process_info = None
            try:
                process_info = start_proxy_server(port=port)
                update_websocket_url(port=port)
                websocket_client = ProxySwitchClient(port)

                co = init_co(9444)
                browser = Chromium(co)
                boss(browser, websocket_client)
            finally:
                pass
                stop_proxy_server(process_info)
                browser.quit()
        except Exception as e:
            logger.error(f'任务失败: {e}，重试中...')


if __name__ == '__main__':
    process()
