import requests
from urllib3 import disable_warnings
import random
import re


def get_long_proxy():
    res = requests.get('http://*************:8015/long-proxy')
    proxy = random.choice(res.text.split('\r\n'))
    return {
        'http': f'http://{proxy}',
        'https': f'http://{proxy}'
    }


disable_warnings()  # 禁用SSL证书警告
headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9",
    "cache-control": "no-cache",
    "pragma": "no-cache",
    "priority": "u=1, i",
    "referer": "https://www.zhipin.com/web/geek/jobs?query=%E7%88%AC%E8%99%AB",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "traceid": "F-a2cd6cbKYRvIHu2r",
    "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "x-requested-with": "XMLHttpRequest"
}
url = "https://www.zhipin.com/wapi/zpgeek/search/joblist.json"
params = {
    "page": "1",
    "pageSize": "3",
    "city": "101010100",
    "position": "100101",
    # "expectInfo": "",
    # "query": "",
    # "multiSubway": "",
    # "multiBusinessDistrict": "",
    # "jobType": "",
    # "salary": "",
    # "experience": "",
    # "degree": "",
    # "industry": "",
    # "scale": "",
    # "stage": "",
    # "scene": "1",
    # "_": "1752056409512"
}

proxies = {
    'http': 'http://************:30636',
    'https': 'http://************:30636'
}


def fetch_job_list(cookies=None, IP=None, url_param=None):
    session = requests.Session()
    # session.proxies = proxies
    session.proxies = {
        'http': IP,
        'https': IP
    }
    response = session.get(url_param, headers=headers, verify=False, timeout=5, cookies=cookies)
    print(response.json())


if __name__ == '__main__':
    aa = {}
    ip = 'http://**************:12380'
    fetch_job_list(aa, ip)
