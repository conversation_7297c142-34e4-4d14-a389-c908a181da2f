import requests

headers = {
    "Origin": "http://www.lvwang.org.cn",
    "Referer": "http://www.lvwang.org.cn/search/",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0"
}
url = "http://www.lvwang.org.cn/search/enterprise-suggestions"
data = {"keywords": "海南福臻餐饮管理有限公司"}
response = requests.post(url, headers=headers, json=data, verify=False, timeout=5)
list_ = response.json()['data'][:1]
enterprise_id = list_[0]['enterprise_id']
detail_url = f"http://www.lvwang.org.cn/pages/eriCompanyDetails.html?id={enterprise_id}&module=0"
