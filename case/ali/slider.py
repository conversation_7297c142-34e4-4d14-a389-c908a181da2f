from DrissionPage import Chromium, ChromiumOptions
import random
import time


def init_co(port=9222):
    co = ChromiumOptions().set_local_port(port)
    co.ignore_certificate_errors()
    # co.set_load_mode('none')  # eager none
    co.set_argument('--no-sandbox')
    co.set_argument("--disable-gpu")
    # co.set_argument('--disable-features=DisableLoadExtensionCommandLineSwitch')
    # co.add_extension('/Users/<USER>/Downloads/pygs-work-parent/apps/dingliangyi/switch_proxy_plugin')
    # co.incognito(True)
    # co.headless(True)
    # co.set_argument('--auto-open-devtools-for-tabs')  # 自动打开开发者工具
    co.set_user_agent(
        user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36')
    return co


def get_tab(browser: Chromium):
    tab = browser.latest_tab
    tab.set.cookies.clear()
    # tab.run_cdp('Network.setCacheDisabled', cacheDisabled=True)
    # tab.run_cdp('Runtime.enable')
    # tab.run_cdp('Log.enable')
    # tab.set.load_mode('none')
    tab.set.user_agent(tab.user_agent.replace('Headless', ''))  # 去掉Headless
    return tab


browser = Chromium(init_co())


def slide():
    ss = 280
    page.wait.eles_loaded("span.nc-lang-cnt", timeout=3, raise_err=True)
    page.ele("x://span[contains(@id,'nc_1_n1z')]").hover()
    page.actions.hold("x://span[contains(@id,'nc_1_n1z')]")
    page.actions.move(ss, offset_y=random.randint(1, 3), duration=random.random())
    # time.sleep(random.random())
    # page.actions.release("x://span[contains(@id,'nc_1_n1z')]")


page = get_tab(browser)
page.get('http://www.lvwang.org.cn/pages/eriCompanyDetails.html?id=83224487&module=1')
slide()
# browser.quit()
