import requests
import time
from loguru import logger
import execjs
import re
import json
import ddddocr
import cv2
import numpy as np
from binascii import b2a_hex
import rsa
import base64
import uuid


def rsa_encrypt(text: str):
    key = "00C1E3934D1614465B33053E7F48EE4EC87B14B95EF88947713D25EECBFF7E74C7977D02DC1D9451F79DD5D1C10C29ACB6A9B4D6FB7D0A0279B6719E1772565F09AF627715919221AEF91899CAE08C0D686D748B20A3603BE2318CA6BC2B59706592A9219D0BF05C9F65023A21D2330807252AE0066D59CEEFA5F2748EA80BAB81"
    pub_key = rsa.PublicKey(e=int('10001', 16), n=int(key, 16))
    rsa_e = rsa.encrypt(text.encode(), pub_key)

    return b2a_hex(rsa_e).decode()


def get_uuid():
    js_code = """
    var uuid = function () {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            var r = Math.random() * 16 | 0;
            var v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    };
    """
    compile_js = execjs.compile(js_code)
    return compile_js.call("uuid")


headers = {
    "Accept": "*/*",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Cache-Control": "no-cache",
    "Connection": "keep-alive",
    "Pragma": "no-cache",
    "Referer": "https://gt4.geetest.com/",
    "Sec-Fetch-Dest": "script",
    "Sec-Fetch-Mode": "no-cors",
    "Sec-Fetch-Site": "same-site",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "sec-ch-ua": "^\\^Google",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "^\\^Windows^^"
}
# captcha_id = "54088bb07d2df3c46b79f80300b0abbe"
# captcha_id = "517df78b31ff1b8f841cd86fc0db9f3e"
captcha_id = "af29b3003fc94f2ba29e865b31ee86ee"


def get_load():
    url = "https://gcaptcha4.geetest.com/load"
    params = {
        "callback": f"geetest_{int(time.time() * 1000)}",
        "captcha_id": captcha_id,
        "challenge": get_uuid(),
        "client_type": "web",
        "risk_type": "slide",
        "lang": "zh-cn"
    }
    response = requests.get(url, headers=headers, params=params)
    load_json = json.loads(re.search(r'geetest_\d{13}\((.*)\)', response.text).group(1))

    logger.success(response.status_code)
    logger.info(load_json)

    return load_json


def get_distance(bg_url):
    bg_url = f"https://static.geetest.com/{bg_url}"
    background_bytes = requests.get(bg_url, headers=headers).content
    url = "https://www.detayun.cn/openapi/verify_code_identify/"
    header = {"Content-Type": "application/json"}
    pic = base64.b64encode(background_bytes)
    data = {
        "key": "WWghmUIPAgF4HS7ii6Ti",
        "verify_idf_id": "23",
        "img_base64": "data:image/jpeg;base64," + pic.decode(),
        "words": ""
    }
    response_ = requests.post(url=url, json=data, headers=header)
    slider_distance = int(response_.json()['data']['res_str'][2:-2])
    logger.success(f"滑块距离：{slider_distance}")

    image = cv2.imdecode(np.frombuffer(background_bytes, np.uint8), cv2.IMREAD_COLOR)
    cv2.line(image, (slider_distance, 0), (slider_distance, 200), (0, 255, 0), thickness=1)
    cv2.imwrite('test.png', image)

    return slider_distance


def get_w(load_json):
    # 获取gct4.5··· js文件的两个函数字符串
    response_ = requests.get('http://static.geetest.com' + load_json['data']['gct_path'], headers=headers)
    code = \
        re.findall(r'];(function Fmhz\(t\){var .*;break;}}})(function .*\(.*\){var .*\)]\(\)\);break;}}})',
                   response_.text)[
            0]

    distance = get_distance(load_json['data']['bg'])

    js_code = open('./w.js', 'r', encoding='utf-8').read()
    js_compile = execjs.compile(js_code)
    js_result = js_compile.call('get_w', distance - 13, load_json['data']['lot_number'], captcha_id,
                                load_json['data']['pow_detail'], code[0], code[1])

    logger.info(f'distance :{distance}')
    logger.info(js_result['params'])
    logger.info(js_result['key'])

    return js_result['w']


def verify(load_json, w):
    params = {
        "callback": f"geetest_{int(time.time() * 1000)}",
        "captcha_id": captcha_id,
        "client_type": "web",
        "lot_number": load_json['data']['lot_number'],
        "risk_type": "slide",
        "payload": load_json['data']['payload'],
        "process_token": load_json['data']['process_token'],
        "payload_protocol": "1",
        "pt": "1",
        "w": w
    }
    logger.info(params)

    url2 = "https://gcaptcha4.geetest.com/verify"
    response2 = requests.get(url2, headers=headers, params=params, verify=False)
    logger.success(response2.status_code)
    logger.info(response2.text)


if __name__ == '__main__':
    load = get_load()
    w = get_w(load)
    verify(load, w)
    # logger.info(get_distance(
    #     "captcha_v4/e70fbf1d77/slide/22397859ec/2022-04-21T09/bg/8d3615fcfad045b8b10bc9b9f665b2c6.png"))
