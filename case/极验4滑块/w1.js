const __ = require('lodash');
const NodeRSA = require('node-rsa');
const CryptoJS = require('crypto-js')

// Date.now = function () {
//     return 1681893196072
// }
//
// Date.parse = function () {
//     return 1681893196072
// }
//
// Date.prototype.getTime = function () {
//     return 1681893196072
// }
//
// Date.prototype.valueOf = function () {
//     return 1681893196072
// }
//
// Math.random = function () {
//     return 0.5
// }

function get__setLeft__passtime__userresponse(distance, slide_time) {
    let random_time = null
    if (distance < 100) {
        random_time = __.random(500, 1000, false)
    } else if (distance > 100 && distance < 200) {
        random_time = __.random(1000, 1700, false)
    } else {
        random_time = __.random(1500, 2100, false)
    }
    slide_time = slide_time || random_time
    return {
        "setLeft": distance,
        "passtime": slide_time,
        "userresponse": distance / 1.0059466666666665 + 2
    }
}

// console.log(get__setLeft__passtime__userresponse(150))

var s_encode = function () {
    function _(e) {
        var t,
            n,
            s,
            r = "",
            i = -1;

        if (e && e["length"]) {
            s = e["length"];

            while ((i += 1) < s) t = e["charCodeAt"](i), n = i + 1 < s ? e["charCodeAt"](i + 1) : 0, 55296 <= t && t <= 56319 && 56320 <= n && n <= 57343 && (t = 65536 + ((1023 & t) << 10) + (1023 & n), i += 1), t <= 127 ? r += String["fromCharCode"](t) : t <= 2047 ? r += String["fromCharCode"](192 | t >>> 6 & 31, 128 | 63 & t) : t <= 65535 ? r += String["fromCharCode"](224 | t >>> 12 & 15, 128 | t >>> 6 & 63, 128 | 63 & t) : t <= 2097151 && (r += String["fromCharCode"](240 | t >>> 18 & 7, 128 | t >>> 12 & 63, 128 | t >>> 6 & 63, 128 | 63 & t));
        }

        return r;
    }

    function B(e, t) {
        var n = (65535 & e) + (65535 & t);
        return (e >> 16) + (t >> 16) + (n >> 16) << 16 | 65535 & n;
    }

    function S(e, t) {
        return e << t | e >>> 32 - t;
    }

    function o(e, t) {
        for (var n, s = t ? "0123456789ABCDEF" : "0123456789abcdef", r = "", i = 0, o = e["length"]; i < o; i += 1) n = e["charCodeAt"](i), r += s["charAt"](n >>> 4 & 15) + s["charAt"](15 & n);

        return r;
    }

    function c(e) {
        var t,
            n = 32 * e["length"],
            s = "";

        for (t = 0; t < n; t += 8) s += String["fromCharCode"](e[t >> 5] >>> 24 - t % 32 & 255);

        return s;
    }

    function d(e) {
        var t,
            n = 32 * e["length"],
            s = "";

        for (t = 0; t < n; t += 8) s += String["fromCharCode"](e[t >> 5] >>> t % 32 & 255);

        return s;
    }

    function g(e) {
        var t,
            n = 8 * e["length"],
            s = Array(e["length"] >> 2),
            r = s["length"];

        for (t = 0; t < r; t += 1) s[t] = 0;

        for (t = 0; t < n; t += 8) s[t >> 5] |= (255 & e["charCodeAt"](t / 8)) << t % 32;

        return s;
    }

    function h(e) {
        var t,
            n = 8 * e["length"],
            s = Array(e["length"] >> 2),
            r = s["length"];

        for (t = 0; t < r; t += 1) s[t] = 0;

        for (t = 0; t < n; t += 8) s[t >> 5] |= (255 & e["charCodeAt"](t / 8)) << 24 - t % 32;

        return s;
    }

    function v(e, t) {
        var n,
            s,
            r,
            i,
            o,
            a,
            u,
            c,
            _ = t["length"],
            h = Array();

        for (i = (a = Array(Math["ceil"](e["length"] / 2)))["length"], n = 0; n < i; n += 1) a[n] = e["charCodeAt"](2 * n) << 8 | e["charCodeAt"](2 * n + 1);

        while (0 < a["length"]) {
            for (o = Array(), n = r = 0; n < a["length"]; n += 1) r = (r << 16) + a[n], r -= (s = Math["floor"](r / _)) * _, (0 < o["length"] || 0 < s) && (o[o["length"]] = s);

            h[h["length"]] = r, a = o;
        }

        for (u = "", n = h["length"] - 1; 0 <= n; n--) u += t["charAt"](h[n]);

        for (c = Math["ceil"](8 * e["length"] / (Math["log"](t["length"]) / Math["log"](2))), n = u["length"]; n < c; n += 1) u = t[0] + u;

        return u;
    }

    function b(e, t) {
        var n,
            s,
            r,
            i = "",
            o = e["length"];

        for (t = t || "=", n = 0; n < o; n += 3) for (r = e["charCodeAt"](n) << 16 | (n + 1 < o ? e["charCodeAt"](n + 1) << 8 : 0) | (n + 2 < o ? e["charCodeAt"](n + 2) : 0), s = 0; s < 4; s += 1) 8 * n + 6 * s > 8 * e["length"] ? i += t : i += "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"["charAt"](r >>> 6 * (3 - s) & 63);

        return i;
    }

    return {
        "VERSION": "1.0.6",
        "Base64": function () {
            var l = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",
                p = "=",
                f = !0;
            this["encode"] = function (e) {
                var t,
                    n,
                    s,
                    r = "",
                    i = e["length"];

                for (p = p || "=", e = f ? _(e) : e, t = 0; t < i; t += 3) for (s = e["charCodeAt"](t) << 16 | (t + 1 < i ? e["charCodeAt"](t + 1) << 8 : 0) | (t + 2 < i ? e["charCodeAt"](t + 2) : 0), n = 0; n < 4; n += 1) r += 8 * i < 8 * t + 6 * n ? p : l["charAt"](s >>> 6 * (3 - n) & 63);

                return r;
            }, this["decode"] = function (e) {
                var t,
                    n,
                    s,
                    r,
                    i,
                    o,
                    a,
                    u,
                    c = "",
                    _ = [];
                if (!e) return e;
                t = u = 0, e = e["replace"](new RegExp("\\" + p, "gi"), "");

                do {
                    n = (a = l["indexOf"](e["charAt"](t++)) << 18 | l["indexOf"](e["charAt"](t++)) << 12 | (i = l["indexOf"](e["charAt"](t++))) << 6 | (o = l["indexOf"](e["charAt"](t++)))) >> 16 & 255, s = a >> 8 & 255, r = 255 & a, _[u += 1] = 64 === i ? String["fromCharCode"](n) : 64 === o ? String["fromCharCode"](n, s) : String["fromCharCode"](n, s, r);
                } while (t < e["length"]);

                return c = _["join"](""), c = f ? function h(e) {
                    var t,
                        n,
                        s,
                        r,
                        i,
                        o,
                        a = [];

                    if (t = n = s = r = i = 0, e && e["length"]) {
                        o = e["length"], e += "";

                        while (t < o) n += 1, (s = e["charCodeAt"](t)) < 128 ? (a[n] = String["fromCharCode"](s), t += 1) : 191 < s && s < 224 ? (r = e["charCodeAt"](t + 1), a[n] = String["fromCharCode"]((31 & s) << 6 | 63 & r), t += 2) : (r = e["charCodeAt"](t + 1), i = e["charCodeAt"](t + 2), a[n] = String["fromCharCode"]((15 & s) << 12 | (63 & r) << 6 | 63 & i), t += 3);
                    }

                    return a["join"]("");
                }(c) : c;
            }, this["setPad"] = function (e) {
                return p = e || p, this;
            }, this["setTab"] = function (e) {
                return l = e || l, this;
            }, this["setUTF8"] = function (e) {
                return "boolean" == typeof e && (f = e), this;
            };
        },
        "CRC32": function (e) {
            var t,
                n,
                s,
                r = 0,
                i = 0;

            for (e = _(e), t = ["00000000 77073096 EE0E612C 990951BA 076DC419 706AF48F E963A535 9E6495A3 0EDB8832 ", "79DCB8A4 E0D5E91E 97D2D988 09B64C2B 7EB17CBD E7B82D07 90BF1D91 1DB71064 6AB020F2 F3B97148 ", "84BE41DE 1ADAD47D 6DDDE4EB F4D4B551 83D385C7 136C9856 646BA8C0 FD62F97A 8A65C9EC 14015C4F ", "63066CD9 FA0F3D63 8D080DF5 3B6E20C8 4C69105E D56041E4 A2677172 3C03E4D1 4B04D447 D20D85FD ", "A50AB56B 35B5A8FA 42B2986C DBBBC9D6 ACBCF940 32D86CE3 45DF5C75 DCD60DCF ABD13D59 26D930AC ", "51DE003A C8D75180 BFD06116 21B4F4B5 56B3C423 CFBA9599 B8BDA50F 2802B89E 5F058808 C60CD9B2 ", "B10BE924 2F6F7C87 58684C11 C1611DAB B6662D3D 76DC4190 01DB7106 98D220BC EFD5102A 71B18589 ", "06B6B51F 9FBFE4A5 E8B8D433 7807C9A2 0F00F934 9609A88E E10E9818 7F6A0DBB 086D3D2D 91646C97 ", "E6635C01 6B6B51F4 1C6C6162 856530D8 F262004E 6C0695ED 1B01A57B 8208F4C1 F50FC457 65B0D9C6 ", "12B7E950 8BBEB8EA FCB9887C 62DD1DDF 15DA2D49 8CD37CF3 FBD44C65 4DB26158 3AB551CE A3BC0074 ", "D4BB30E2 4ADFA541 3DD895D7 A4D1C46D D3D6F4FB 4369E96A 346ED9FC AD678846 DA60B8D0 44042D73 ", "33031DE5 AA0A4C5F DD0D7CC9 5005713C 270241AA BE0B1010 C90C2086 5768B525 206F85B3 B966D409 ", "CE61E49F 5EDEF90E 29D9C998 B0D09822 C7D7A8B4 59B33D17 2EB40D81 B7BD5C3B C0BA6CAD EDB88320 ", "9ABFB3B6 03B6E20C 74B1D29A EAD54739 9DD277AF 04DB2615 73DC1683 E3630B12 94643B84 0D6D6A3E ", "7A6A5AA8 E40ECF0B 9309FF9D 0A00AE27 7D079EB1 F00F9344 8708A3D2 1E01F268 6906C2FE F762575D ", "806567CB 196C3671 6E6B06E7 FED41B76 89D32BE0 10DA7A5A 67DD4ACC F9B9DF6F 8EBEEFF9 17B7BE43 ", "60B08ED5 D6D6A3E8 A1D1937E 38D8C2C4 4FDFF252 D1BB67F1 A6BC5767 3FB506DD 48B2364B D80D2BDA ", "AF0A1B4C 36034AF6 41047A60 DF60EFC3 A867DF55 316E8EEF 4669BE79 CB61B38C BC66831A 256FD2A0 ", "5268E236 CC0C7795 BB0B4703 220216B9 5505262F C5BA3BBE B2BD0B28 2BB45A92 5CB36A04 C2D7FFA7 ", "B5D0CF31 2CD99E8B 5BDEAE1D 9B64C2B0 EC63F226 756AA39C 026D930A 9C0906A9 EB0E363F 72076785 ", "05005713 95BF4A82 E2B87A14 7BB12BAE 0CB61B38 92D28E9B E5D5BE0D 7CDCEFB7 0BDBDF21 86D3D2D4 ", "F1D4E242 68DDB3F8 1FDA836E 81BE16CD F6B9265B 6FB077E1 18B74777 88085AE6 FF0F6A70 66063BCA ", "11010B5C 8F659EFF F862AE69 616BFFD3 166CCF45 A00AE278 D70DD2EE 4E048354 3903B3C2 A7672661 ", "D06016F7 4969474D 3E6E77DB AED16A4A D9D65ADC 40DF0B66 37D83BF0 A9BCAE53 DEBB9EC5 47B2CF7F ", "30B5FFE9 BDBDF21C CABAC28A 53B39330 24B4A3A6 BAD03605 CDD70693 54DE5729 23D967BF B3667A2E ", "C4614AB8 5D681B02 2A6F2B94 B40BBE37 C30C8EA1 5A05DF1B 2D02EF8D"]["join"](""), r ^= -1, n = 0, s = e["length"]; n < s; n += 1) i = 255 & (r ^ e["charCodeAt"](n)), r = r >>> 8 ^ "0x" + t["substring"](9 * i, 8);

            return (-1 ^ r) >>> 0;
        },
        "MD5": function (e) {
            var n = !(!e || "boolean" != typeof e["uppercase"]) && e["uppercase"],
                s = e && "string" == typeof e["pad"] ? e["pad"] : "=",
                a = !e || "boolean" != typeof e["utf8"] || e["utf8"];

            function r(e) {
                return d(u(g(e = a ? _(e) : e), 8 * e["length"]));
            }

            function i(e, t) {
                var n, s, r, i, o;

                for (e = a ? _(e) : e, t = a ? _(t) : t, 16 < (n = g(e))["length"] && (n = u(n, 8 * e["length"])), s = Array(16), r = Array(16), o = 0; o < 16; o += 1) s[o] = 909522486 ^ n[o], r[o] = 1549556828 ^ n[o];

                return i = u(s["concat"](g(t)), 512 + 8 * t["length"]), d(u(r["concat"](i), 640));
            }

            function u(e, t) {
                var n,
                    s,
                    r,
                    i,
                    o,
                    a = 1732584193,
                    u = -271733879,
                    c = -1732584194,
                    _ = 271733878;

                for (e[t >> 5] |= 128 << t % 32, e[14 + (t + 64 >>> 9 << 4)] = t, n = 0; n < e["length"]; n += 16) u = f(u = f(u = f(u = f(u = p(u = p(u = p(u = p(u = l(u = l(u = l(u = l(u = h(u = h(u = h(u = h(r = u, c = h(i = c, _ = h(o = _, a = h(s = a, u, c, _, e[n + 0], 7, -680876936), u, c, e[n + 1], 12, -389564586), a, u, e[n + 2], 17, 606105819), _, a, e[n + 3], 22, -1044525330), c = h(c, _ = h(_, a = h(a, u, c, _, e[n + 4], 7, -176418897), u, c, e[n + 5], 12, 1200080426), a, u, e[n + 6], 17, -1473231341), _, a, e[n + 7], 22, -45705983), c = h(c, _ = h(_, a = h(a, u, c, _, e[n + 8], 7, 1770035416), u, c, e[n + 9], 12, -1958414417), a, u, e[n + 10], 17, -42063), _, a, e[n + 11], 22, -1990404162), c = h(c, _ = h(_, a = h(a, u, c, _, e[n + 12], 7, 1804603682), u, c, e[n + 13], 12, -40341101), a, u, e[n + 14], 17, -1502002290), _, a, e[n + 15], 22, 1236535329), c = l(c, _ = l(_, a = l(a, u, c, _, e[n + 1], 5, -165796510), u, c, e[n + 6], 9, -1069501632), a, u, e[n + 11], 14, 643717713), _, a, e[n + 0], 20, -373897302), c = l(c, _ = l(_, a = l(a, u, c, _, e[n + 5], 5, -701558691), u, c, e[n + 10], 9, 38016083), a, u, e[n + 15], 14, -660478335), _, a, e[n + 4], 20, -405537848), c = l(c, _ = l(_, a = l(a, u, c, _, e[n + 9], 5, 568446438), u, c, e[n + 14], 9, -1019803690), a, u, e[n + 3], 14, -187363961), _, a, e[n + 8], 20, 1163531501), c = l(c, _ = l(_, a = l(a, u, c, _, e[n + 13], 5, -1444681467), u, c, e[n + 2], 9, -51403784), a, u, e[n + 7], 14, 1735328473), _, a, e[n + 12], 20, -1926607734), c = p(c, _ = p(_, a = p(a, u, c, _, e[n + 5], 4, -378558), u, c, e[n + 8], 11, -2022574463), a, u, e[n + 11], 16, 1839030562), _, a, e[n + 14], 23, -35309556), c = p(c, _ = p(_, a = p(a, u, c, _, e[n + 1], 4, -1530992060), u, c, e[n + 4], 11, 1272893353), a, u, e[n + 7], 16, -155497632), _, a, e[n + 10], 23, -1094730640), c = p(c, _ = p(_, a = p(a, u, c, _, e[n + 13], 4, 681279174), u, c, e[n + 0], 11, -358537222), a, u, e[n + 3], 16, -722521979), _, a, e[n + 6], 23, 76029189), c = p(c, _ = p(_, a = p(a, u, c, _, e[n + 9], 4, -640364487), u, c, e[n + 12], 11, -421815835), a, u, e[n + 15], 16, 530742520), _, a, e[n + 2], 23, -995338651), c = f(c, _ = f(_, a = f(a, u, c, _, e[n + 0], 6, -198630844), u, c, e[n + 7], 10, 1126891415), a, u, e[n + 14], 15, -1416354905), _, a, e[n + 5], 21, -57434055), c = f(c, _ = f(_, a = f(a, u, c, _, e[n + 12], 6, 1700485571), u, c, e[n + 3], 10, -1894986606), a, u, e[n + 10], 15, -1051523), _, a, e[n + 1], 21, -2054922799), c = f(c, _ = f(_, a = f(a, u, c, _, e[n + 8], 6, 1873313359), u, c, e[n + 15], 10, -30611744), a, u, e[n + 6], 15, -1560198380), _, a, e[n + 13], 21, 1309151649), c = f(c, _ = f(_, a = f(a, u, c, _, e[n + 4], 6, -145523070), u, c, e[n + 11], 10, -1120210379), a, u, e[n + 2], 15, 718787259), _, a, e[n + 9], 21, -343485551), a = B(a, s), u = B(u, r), c = B(c, i), _ = B(_, o);

                return Array(a, u, c, _);
            }

            function c(e, t, n, s, r, i) {
                return B(S(B(B(t, e), B(s, i)), r), n);
            }

            function h(e, t, n, s, r, i, o) {
                return c(t & n | ~t & s, e, t, r, i, o);
            }

            function l(e, t, n, s, r, i, o) {
                return c(t & s | n & ~s, e, t, r, i, o);
            }

            function p(e, t, n, s, r, i, o) {
                return c(t ^ n ^ s, e, t, r, i, o);
            }

            function f(e, t, n, s, r, i, o) {
                return c(n ^ (t | ~s), e, t, r, i, o);
            }

            this["hex"] = function (e) {
                return o(r(e), n);
            }, this["b64"] = function (e) {
                return b(r(e), s);
            }, this["any"] = function (e, t) {
                return v(r(e), t);
            }, this["raw"] = function (e) {
                return r(e);
            }, this["hex_hmac"] = function (e, t) {
                return o(i(e, t), n);
            }, this["b64_hmac"] = function (e, t) {
                return b(i(e, t), s);
            }, this["any_hmac"] = function (e, t, n) {
                return v(i(e, t), n);
            }, this["vm_test"] = function () {
                return "900150983cd24fb0d6963f7d28e17f72" === hex("abc")["toLowerCase"]();
            }, this["setUpperCase"] = function (e) {
                return "boolean" == typeof e && (n = e), this;
            }, this["setPad"] = function (e) {
                return s = e || s, this;
            }, this["setUTF8"] = function (e) {
                return "boolean" == typeof e && (a = e), this;
            };
        },
        "SHA1": function (e) {
            var t = !(!e || "boolean" != typeof e["uppercase"]) && e["uppercase"],
                n = e && "string" == typeof e["pad"] ? e["pad"] : "=",
                a = !e || "boolean" != typeof e["utf8"] || e["utf8"];

            function s(e) {
                return c(u(h(e = a ? _(e) : e), 8 * e["length"]));
            }

            function r(e, t) {
                var n, s, r, i, o;

                for (e = a ? _(e) : e, t = a ? _(t) : t, 16 < (n = h(e))["length"] && (n = u(n, 8 * e["length"])), s = Array(16), r = Array(16), i = 0; i < 16; i += 1) s[i] = 909522486 ^ n[i], r[i] = 1549556828 ^ n[i];

                return o = u(s["concat"](h(t)), 512 + 8 * t["length"]), c(u(r["concat"](o), 672));
            }

            function u(e, t) {
                var n,
                    s,
                    r,
                    i,
                    o,
                    a,
                    u,
                    c,
                    _,
                    h = Array(80),
                    l = 1732584193,
                    p = -271733879,
                    f = -1732584194,
                    d = 271733878,
                    g = -1009589776;

                for (e[t >> 5] |= 128 << 24 - t % 32, e[15 + (t + 64 >> 9 << 4)] = t, n = 0; n < e["length"]; n += 16) {
                    for (i = l, o = p, a = f, u = d, c = g, s = 0; s < 80; s += 1) h[s] = s < 16 ? e[n + s] : S(h[s - 3] ^ h[s - 8] ^ h[s - 14] ^ h[s - 16], 1), r = B(B(S(l, 5), m(s, p, f, d)), B(B(g, h[s]), (_ = s) < 20 ? 1518500249 : _ < 40 ? 1859775393 : _ < 60 ? -1894007588 : -899497514)), g = d, d = f, f = S(p, 30), p = l, l = r;

                    l = B(l, i), p = B(p, o), f = B(f, a), d = B(d, u), g = B(g, c);
                }

                return Array(l, p, f, d, g);
            }

            function m(e, t, n, s) {
                return e < 20 ? t & n | ~t & s : e < 40 ? t ^ n ^ s : e < 60 ? t & n | t & s | n & s : t ^ n ^ s;
            }

            this["hex"] = function (e) {
                return o(s(e), t);
            }, this["b64"] = function (e) {
                return b(s(e), n);
            }, this["any"] = function (e, t) {
                return v(s(e), t);
            }, this["raw"] = function (e) {
                return s(e);
            }, this["hex_hmac"] = function (e, t) {
                return o(r(e, t));
            }, this["b64_hmac"] = function (e, t) {
                return b(r(e, t), n);
            }, this["any_hmac"] = function (e, t, n) {
                return v(r(e, t), n);
            }, this["vm_test"] = function () {
                return "900150983cd24fb0d6963f7d28e17f72" === hex("abc")["toLowerCase"]();
            }, this["setUpperCase"] = function (e) {
                return "boolean" == typeof e && (t = e), this;
            }, this["setPad"] = function (e) {
                return n = e || n, this;
            }, this["setUTF8"] = function (e) {
                return "boolean" == typeof e && (a = e), this;
            };
        },
        "SHA256": function (e) {
            !(!e || "boolean" != typeof e["uppercase"]) && e["uppercase"];
            var T,
                n = e && "string" == typeof e["pad"] ? e["pad"] : "=",
                a = !e || "boolean" != typeof e["utf8"] || e["utf8"];

            function s(e, t) {
                return c(u(h(e = t ? _(e) : e), 8 * e["length"]));
            }

            function r(e, t) {
                var n;
                e = a ? _(e) : e, t = a ? _(t) : t;
                var s = 0,
                    r = h(e),
                    i = Array(16),
                    o = Array(16);

                for (16 < r["length"] && (r = u(r, 8 * e["length"])); s < 16; s += 1) i[s] = 909522486 ^ r[s], o[s] = 1549556828 ^ r[s];

                return n = u(i["concat"](h(t)), 512 + 8 * t["length"]), c(u(o["concat"](n), 768));
            }

            function C(e, t) {
                return e >>> t | e << 32 - t;
            }

            function E(e, t) {
                return e >>> t;
            }

            function u(e, t) {
                var n,
                    s,
                    r,
                    i,
                    o,
                    a,
                    u,
                    c,
                    _,
                    h,
                    l,
                    p,
                    f,
                    d,
                    g,
                    m,
                    v,
                    b,
                    w,
                    y,
                    x = [1779033703, -1150833019, 1013904242, -1521486534, 1359893119, -1694144372, 528734635, 1541459225],
                    k = new Array(64);

                for (e[t >> 5] |= 128 << 24 - t % 32, e[15 + (t + 64 >> 9 << 4)] = t, _ = 0; _ < e["length"]; _ += 16) {
                    for (n = x[0], s = x[1], r = x[2], i = x[3], o = x[4], a = x[5], u = x[6], c = x[7], h = 0; h < 64; h += 1) k[h] = h < 16 ? e[h + _] : B(B(B(C(y = k[h - 2], 17) ^ C(y, 19) ^ E(y, 10), k[h - 7]), C(w = k[h - 15], 7) ^ C(w, 18) ^ E(w, 3)), k[h - 16]), l = B(B(B(B(c, C(b = o, 6) ^ C(b, 11) ^ C(b, 25)), (v = o) & a ^ ~v & u), T[h]), k[h]), p = B(C(m = n, 2) ^ C(m, 13) ^ C(m, 22), (f = n) & (d = s) ^ f & (g = r) ^ d & g), c = u, u = a, a = o, o = B(i, l), i = r, r = s, s = n, n = B(l, p);

                    x[0] = B(n, x[0]), x[1] = B(s, x[1]), x[2] = B(r, x[2]), x[3] = B(i, x[3]), x[4] = B(o, x[4]), x[5] = B(a, x[5]), x[6] = B(u, x[6]), x[7] = B(c, x[7]);
                }

                return x;
            }

            this["hex"] = function (e) {
                return o(s(e, a));
            }, this["b64"] = function (e) {
                return b(s(e, a), n);
            }, this["any"] = function (e, t) {
                return v(s(e, a), t);
            }, this["raw"] = function (e) {
                return s(e, a);
            }, this["hex_hmac"] = function (e, t) {
                return o(r(e, t));
            }, this["b64_hmac"] = function (e, t) {
                return b(r(e, t), n);
            }, this["any_hmac"] = function (e, t, n) {
                return v(r(e, t), n);
            }, this["vm_test"] = function () {
                return "900150983cd24fb0d6963f7d28e17f72" === hex("abc")["toLowerCase"]();
            }, this["setUpperCase"] = function (e) {
                return "boolean" == typeof e && e, this;
            }, this["setPad"] = function (e) {
                return n = e || n, this;
            }, this["setUTF8"] = function (e) {
                return "boolean" == typeof e && (a = e), this;
            }, T = [1116352408, 1899447441, -1245643825, -373957723, 961987163, 1508970993, -1841331548, -1424204075, -670586216, 310598401, 607225278, 1426881987, 1925078388, -2132889090, -1680079193, -1046744716, -459576895, -272742522, 264347078, 604807628, 770255983, 1249150122, 1555081692, 1996064986, -1740746414, -1473132947, -1341970488, -1084653625, -958395405, -710438585, 113926993, 338241895, 666307205, 773529912, 1294757372, 1396182291, 1695183700, 1986661051, -2117940946, -1838011259, -1564481375, -1474664885, -1035236496, -949202525, -778901479, -694614492, -200395387, 275423344, 430227734, 506948616, 659060556, 883997877, 958139571, 1322822218, 1537002063, 1747873779, 1955562222, 2024104815, -2067236844, -1933114872, -1866530822, -1538233109, -1090935817, -965641998];
        },
        "SHA512": function (e) {
            !(!e || "boolean" != typeof e["uppercase"]) && e["uppercase"];
            var C,
                n = e && "string" == typeof e["pad"] ? e["pad"] : "=",
                a = !e || "boolean" != typeof e["utf8"] || e["utf8"];

            function s(e) {
                return c(u(h(e = a ? _(e) : e), 8 * e["length"]));
            }

            function r(e, t) {
                var n;
                e = a ? _(e) : e, t = a ? _(t) : t;
                var s = 0,
                    r = h(e),
                    i = Array(32),
                    o = Array(32);

                for (32 < r["length"] && (r = u(r, 8 * e["length"])); s < 32; s += 1) i[s] = 909522486 ^ r[s], o[s] = 1549556828 ^ r[s];

                return n = u(i["concat"](h(t)), 1024 + 8 * t["length"]), c(u(o["concat"](n), 1536));
            }

            function u(e, t) {
                var n,
                    s,
                    r,
                    i = new Array(80),
                    o = new Array(16),
                    a = [new E(1779033703, -205731576), new E(-1150833019, -2067093701), new E(1013904242, -23791573), new E(-1521486534, 1595750129), new E(1359893119, -1377402159), new E(-1694144372, 725511199), new E(528734635, -79577749), new E(1541459225, 327033209)],
                    u = new E(0, 0),
                    c = new E(0, 0),
                    _ = new E(0, 0),
                    h = new E(0, 0),
                    l = new E(0, 0),
                    p = new E(0, 0),
                    f = new E(0, 0),
                    d = new E(0, 0),
                    g = new E(0, 0),
                    m = new E(0, 0),
                    v = new E(0, 0),
                    b = new E(0, 0),
                    w = new E(0, 0),
                    y = new E(0, 0),
                    x = new E(0, 0),
                    k = new E(0, 0),
                    T = new E(0, 0);

                for (C === undefined && (C = [new E(1116352408, -685199838), new E(1899447441, 602891725), new E(-1245643825, -330482897), new E(-373957723, -2121671748), new E(961987163, -213338824), new E(1508970993, -1241133031), new E(-1841331548, -1357295717), new E(-1424204075, -630357736), new E(-670586216, -1560083902), new E(310598401, 1164996542), new E(607225278, 1323610764), new E(1426881987, -704662302), new E(1925078388, -226784913), new E(-2132889090, 991336113), new E(-1680079193, 633803317), new E(-1046744716, -815192428), new E(-459576895, -1628353838), new E(-272742522, 944711139), new E(264347078, -1953704523), new E(604807628, 2007800933), new E(770255983, 1495990901), new E(1249150122, 1856431235), new E(1555081692, -1119749164), new E(1996064986, -2096016459), new E(-1740746414, -295247957), new E(-1473132947, 766784016), new E(-1341970488, -1728372417), new E(-1084653625, -1091629340), new E(-958395405, 1034457026), new E(-710438585, -1828018395), new E(113926993, -536640913), new E(338241895, 168717936), new E(666307205, 1188179964), new E(773529912, 1546045734), new E(1294757372, 1522805485), new E(1396182291, -1651133473), new E(1695183700, -1951439906), new E(1986661051, 1014477480), new E(-2117940946, 1206759142), new E(-1838011259, 344077627), new E(-1564481375, 1290863460), new E(-1474664885, -1136513023), new E(-1035236496, -789014639), new E(-949202525, 106217008), new E(-778901479, -688958952), new E(-694614492, 1432725776), new E(-200395387, 1467031594), new E(275423344, 851169720), new E(430227734, -1194143544), new E(506948616, 1363258195), new E(659060556, -544281703), new E(883997877, -509917016), new E(958139571, -976659869), new E(1322822218, -482243893), new E(1537002063, 2003034995), new E(1747873779, -692930397), new E(1955562222, 1575990012), new E(2024104815, 1125592928), new E(-2067236844, -1578062990), new E(-1933114872, 442776044), new E(-1866530822, 593698344), new E(-1538233109, -561857047), new E(-1090935817, -1295615723), new E(-965641998, -479046869), new E(-903397682, -366583396), new E(-779700025, 566280711), new E(-354779690, -840897762), new E(-176337025, -294727304), new E(116418474, 1914138554), new E(174292421, -1563912026), new E(289380356, -1090974290), new E(460393269, 320620315), new E(685471733, 587496836), new E(852142971, 1086792851), new E(1017036298, 365543100), new E(1126000580, -1676669620), new E(1288033470, -885112138), new E(1501505948, -60457430), new E(1607167915, 987167468), new E(1816402316, 1246189591)]), s = 0; s < 80; s += 1) i[s] = new E(0, 0);

                for (e[t >> 5] |= 128 << 24 - (31 & t), e[31 + (t + 128 >> 10 << 5)] = t, r = e["length"], s = 0; s < r; s += 32) {
                    for (A(_, a[0]), A(h, a[1]), A(l, a[2]), A(p, a[3]), A(f, a[4]), A(d, a[5]), A(g, a[6]), A(m, a[7]), n = 0; n < 16; n += 1) i[n]["h"] = e[s + 2 * n], i[n]["l"] = e[s + 2 * n + 1];

                    for (n = 16; n < 80; n += 1) B(x, i[n - 2], 19), S(k, i[n - 2], 29), D(T, i[n - 2], 6), b["l"] = x["l"] ^ k["l"] ^ T["l"], b["h"] = x["h"] ^ k["h"] ^ T["h"], B(x, i[n - 15], 1), B(k, i[n - 15], 8), D(T, i[n - 15], 7), v["l"] = x["l"] ^ k["l"] ^ T["l"], v["h"] = x["h"] ^ k["h"] ^ T["h"], F(i[n], b, i[n - 7], v, i[n - 16]);

                    for (n = 0; n < 80; n += 1) w["l"] = f["l"] & d["l"] ^ ~f["l"] & g["l"], w["h"] = f["h"] & d["h"] ^ ~f["h"] & g["h"], B(x, f, 14), B(k, f, 18), S(T, f, 9), b["l"] = x["l"] ^ k["l"] ^ T["l"], b["h"] = x["h"] ^ k["h"] ^ T["h"], B(x, _, 28), S(k, _, 2), S(T, _, 7), v["l"] = x["l"] ^ k["l"] ^ T["l"], v["h"] = x["h"] ^ k["h"] ^ T["h"], y["l"] = _["l"] & h["l"] ^ _["l"] & l["l"] ^ h["l"] & l["l"], y["h"] = _["h"] & h["h"] ^ _["h"] & l["h"] ^ h["h"] & l["h"], M(u, m, b, w, C[n], i[n]), z(c, v, y), A(m, g), A(g, d), A(d, f), z(f, p, u), A(p, l), A(l, h), A(h, _), z(_, u, c);

                    z(a[0], a[0], _), z(a[1], a[1], h), z(a[2], a[2], l), z(a[3], a[3], p), z(a[4], a[4], f), z(a[5], a[5], d), z(a[6], a[6], g), z(a[7], a[7], m);
                }

                for (s = 0; s < 8; s += 1) o[2 * s] = a[s]["h"], o[2 * s + 1] = a[s]["l"];

                return o;
            }

            function E(e, t) {
                this["h"] = e, this["l"] = t;
            }

            function A(e, t) {
                e["h"] = t["h"], e["l"] = t["l"];
            }

            function B(e, t, n) {
                e["l"] = t["l"] >>> n | t["h"] << 32 - n, e["h"] = t["h"] >>> n | t["l"] << 32 - n;
            }

            function S(e, t, n) {
                e["l"] = t["h"] >>> n | t["l"] << 32 - n, e["h"] = t["l"] >>> n | t["h"] << 32 - n;
            }

            function D(e, t, n) {
                e["l"] = t["l"] >>> n | t["h"] << 32 - n, e["h"] = t["h"] >>> n;
            }

            function z(e, t, n) {
                var s = (65535 & t["l"]) + (65535 & n["l"]),
                    r = (t["l"] >>> 16) + (n["l"] >>> 16) + (s >>> 16),
                    i = (65535 & t["h"]) + (65535 & n["h"]) + (r >>> 16),
                    o = (t["h"] >>> 16) + (n["h"] >>> 16) + (i >>> 16);
                e["l"] = 65535 & s | r << 16, e["h"] = 65535 & i | o << 16;
            }

            function F(e, t, n, s, r) {
                var i = (65535 & t["l"]) + (65535 & n["l"]) + (65535 & s["l"]) + (65535 & r["l"]),
                    o = (t["l"] >>> 16) + (n["l"] >>> 16) + (s["l"] >>> 16) + (r["l"] >>> 16) + (i >>> 16),
                    a = (65535 & t["h"]) + (65535 & n["h"]) + (65535 & s["h"]) + (65535 & r["h"]) + (o >>> 16),
                    u = (t["h"] >>> 16) + (n["h"] >>> 16) + (s["h"] >>> 16) + (r["h"] >>> 16) + (a >>> 16);
                e["l"] = 65535 & i | o << 16, e["h"] = 65535 & a | u << 16;
            }

            function M(e, t, n, s, r, i) {
                var o = (65535 & t["l"]) + (65535 & n["l"]) + (65535 & s["l"]) + (65535 & r["l"]) + (65535 & i["l"]),
                    a = (t["l"] >>> 16) + (n["l"] >>> 16) + (s["l"] >>> 16) + (r["l"] >>> 16) + (i["l"] >>> 16) + (o >>> 16),
                    u = (65535 & t["h"]) + (65535 & n["h"]) + (65535 & s["h"]) + (65535 & r["h"]) + (65535 & i["h"]) + (a >>> 16),
                    c = (t["h"] >>> 16) + (n["h"] >>> 16) + (s["h"] >>> 16) + (r["h"] >>> 16) + (i["h"] >>> 16) + (u >>> 16);
                e["l"] = 65535 & o | a << 16, e["h"] = 65535 & u | c << 16;
            }

            this["hex"] = function (e) {
                return o(s(e));
            }, this["b64"] = function (e) {
                return b(s(e), n);
            }, this["any"] = function (e, t) {
                return v(s(e), t);
            }, this["raw"] = function (e) {
                return s(e);
            }, this["hex_hmac"] = function (e, t) {
                return o(r(e, t));
            }, this["b64_hmac"] = function (e, t) {
                return b(r(e, t), n);
            }, this["any_hmac"] = function (e, t, n) {
                return v(r(e, t), n);
            }, this["vm_test"] = function () {
                return "900150983cd24fb0d6963f7d28e17f72" === hex("abc")["toLowerCase"]();
            }, this["setUpperCase"] = function (e) {
                return "boolean" == typeof e && e, this;
            }, this["setPad"] = function (e) {
                return n = e || n, this;
            }, this["setUTF8"] = function (e) {
                return "boolean" == typeof e && (a = e), this;
            };
        },
        "RMD160": function (e) {
            !(!e || "boolean" != typeof e["uppercase"]) && e["uppercase"];
            var n = e && "string" == typeof e["pad"] ? e["pa"] : "=",
                a = !e || "boolean" != typeof e["utf8"] || e["utf8"],
                k = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8, 3, 10, 14, 4, 9, 15, 8, 1, 2, 7, 0, 6, 13, 11, 5, 12, 1, 9, 11, 10, 0, 8, 12, 4, 13, 3, 7, 15, 14, 5, 6, 2, 4, 0, 5, 9, 7, 12, 2, 10, 14, 1, 3, 8, 11, 6, 15, 13],
                T = [5, 14, 7, 0, 9, 2, 11, 4, 13, 6, 15, 8, 1, 10, 3, 12, 6, 11, 3, 7, 0, 13, 5, 10, 14, 15, 8, 12, 4, 9, 1, 2, 15, 5, 1, 3, 7, 14, 6, 9, 11, 8, 12, 2, 10, 0, 4, 13, 8, 6, 4, 1, 3, 11, 15, 0, 5, 12, 2, 13, 9, 7, 10, 14, 12, 15, 10, 4, 1, 5, 8, 7, 6, 2, 13, 14, 0, 3, 9, 11],
                C = [11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8, 7, 6, 8, 13, 11, 9, 7, 15, 7, 12, 15, 9, 11, 7, 13, 12, 11, 13, 6, 7, 14, 9, 13, 15, 14, 8, 13, 6, 5, 12, 7, 5, 11, 12, 14, 15, 14, 15, 9, 8, 9, 14, 5, 6, 8, 6, 5, 12, 9, 15, 5, 11, 6, 8, 13, 12, 5, 12, 13, 14, 11, 8, 5, 6],
                E = [8, 9, 9, 11, 13, 15, 15, 5, 7, 7, 8, 11, 14, 14, 12, 6, 9, 13, 15, 7, 12, 8, 9, 11, 7, 7, 12, 7, 6, 15, 13, 11, 9, 7, 15, 11, 8, 6, 6, 14, 12, 13, 5, 14, 13, 13, 7, 5, 15, 5, 8, 11, 14, 14, 6, 14, 6, 9, 12, 9, 12, 5, 15, 8, 8, 5, 12, 9, 12, 5, 14, 6, 8, 13, 6, 5, 15, 13, 11, 11];

            function s(e) {
                return u(c(g(e = a ? _(e) : e), 8 * e["length"]));
            }

            function r(e, t) {
                var n, s;
                e = a ? _(e) : e, t = a ? _(t) : t;
                var r = g(e),
                    i = Array(16),
                    o = Array(16);

                for (16 < r["length"] && (r = c(r, 8 * e["length"])), n = 0; n < 16; n += 1) i[n] = 909522486 ^ r[n], o[n] = 1549556828 ^ r[n];

                return s = c(i["concat"](g(t)), 512 + 8 * t["length"]), u(c(o["concat"](s), 672));
            }

            function u(e) {
                var t,
                    n = "",
                    s = 32 * e["length"];

                for (t = 0; t < s; t += 8) n += String["fromCharCode"](e[t >> 5] >>> t % 32 & 255);

                return n;
            }

            function c(e, t) {
                var n,
                    s,
                    r,
                    i,
                    o,
                    a,
                    u,
                    c,
                    _,
                    h,
                    l,
                    p,
                    f,
                    d,
                    g,
                    m,
                    v = 1732584193,
                    b = 4023233417,
                    w = 2562383102,
                    y = 271733878,
                    x = 3285377520;

                for (e[t >> 5] |= 128 << t % 32, e[14 + (t + 64 >>> 9 << 4)] = t, i = e["length"], r = 0; r < i; r += 16) {
                    for (o = h = v, a = l = b, u = p = w, c = f = y, _ = d = x, s = 0; s <= 79; s += 1) n = B(S(n = B(n = B(n = B(o, A(s, a, u, c)), e[r + k[s]]), 0 <= (m = s) && m <= 15 ? 0 : 16 <= m && m <= 31 ? 1518500249 : 32 <= m && m <= 47 ? 1859775393 : 48 <= m && m <= 63 ? 2400959708 : 64 <= m && m <= 79 ? 2840853838 : "rmd160_K1: j out of range"), C[s]), _), o = _, _ = c, c = S(u, 10), u = a, a = n, n = B(S(n = B(n = B(n = B(h, A(79 - s, l, p, f)), e[r + T[s]]), 0 <= (g = s) && g <= 15 ? 1352829926 : 16 <= g && g <= 31 ? 1548603684 : 32 <= g && g <= 47 ? 1836072691 : 48 <= g && g <= 63 ? 2053994217 : 64 <= g && g <= 79 ? 0 : "rmd160_K2: j out of range"), E[s]), d), h = d, d = f, f = S(p, 10), p = l, l = n;

                    n = B(b, B(u, f)), b = B(w, B(c, d)), w = B(y, B(_, h)), y = B(x, B(o, l)), x = B(v, B(a, p)), v = n;
                }

                return [v, b, w, y, x];
            }

            function A(e, t, n, s) {
                return 0 <= e && e <= 15 ? t ^ n ^ s : 16 <= e && e <= 31 ? t & n | ~t & s : 32 <= e && e <= 47 ? (t | ~n) ^ s : 48 <= e && e <= 63 ? t & s | n & ~s : 64 <= e && e <= 79 ? t ^ (n | ~s) : "rmd160_f: j out of range";
            }

            this["hex"] = function (e) {
                return o(s(e));
            }, this["b64"] = function (e) {
                return b(s(e), n);
            }, this["any"] = function (e, t) {
                return v(s(e), t);
            }, this["raw"] = function (e) {
                return s(e);
            }, this["hex_hmac"] = function (e, t) {
                return o(r(e, t));
            }, this["b64_hmac"] = function (e, t) {
                return b(r(e, t), n);
            }, this["any_hmac"] = function (e, t, n) {
                return v(r(e, t), n);
            }, this["vm_test"] = function () {
                return "900150983cd24fb0d6963f7d28e17f72" === hex("abc")["toLowerCase"]();
            }, this["setUpperCase"] = function (e) {
                return "boolean" == typeof e && e, this;
            }, this["setPad"] = function (e) {
                return void 0 !== e && (n = e), this;
            }, this["setUTF8"] = function (e) {
                return "boolean" == typeof e && (a = e), this;
            };
        },
        "BitParse": function () {
            this["hex"] = function (e) {
                var t = {
                    "0": "0000",
                    "1": "0001",
                    "2": "0010",
                    "3": "0011",
                    "4": "0100",
                    "5": "0101",
                    "6": "0110",
                    "7": "0111",
                    "8": "1000",
                    "9": "1001",
                    "a": "1010",
                    "b": "1011",
                    "c": "1100",
                    "d": "1101",
                    "e": "1110",
                    "f": "1111"
                };

                if (1 < e["length"]) {
                    var n = [];

                    for (var s in e) for (var r in t) e[s] === r && (n[s] = t[r]);

                    return n["join"]("");
                }

                return t[e];
            };
        }
    };
}();

function get__pow_msg__pow_sign(e, t, n, s, r, i, o) {
    var a = r % 4,
        u = parseInt(r / 4, 10),
        c = function g(e, t) {
            return new Array(t + 1)["join"](e);
        }("0", u),
        _ = s + "|" + r + "|" + n + "|" + i + "|" + t + "|" + e + "|" + o + "|";

    while (1) {
        var r = function () {
            function e() {
                return (65536 * (1 + Math["random"]()) | 0)["toString"](16)["substring"](1);
            }

            return function () {
                return e() + e() + e() + e();
            };
        }();

        var h = r(),
            l = _ + h,
            p = void 0;


        var m = {}
        m["default"] = s_encode

        switch (n) {
            case "md5":
                p = new m["default"]["MD5"]()["hex"](l);
                break;

            case "sha1":
                p = new m["default"]["SHA1"]()["hex"](l);
                break;

            case "sha256":
                p = new m["default"]["SHA256"]()["hex"](l);
        }

        if (0 == a) {
            if (0 === p["indexOf"](c)) return {
                "pow_msg": _ + h,
                "pow_sign": p
            };
        } else if (0 === p["indexOf"](c)) {
            var f = void 0,
                d = p[u];

            switch (a) {
                case 1:
                    f = 7;
                    break;

                case 2:
                    f = 3;
                    break;

                case 3:
                    f = 1;
            }

            if (d <= f) return {
                "pow_msg": _ + h,
                "pow_sign": p
            };
        }
    }
}

// console.log(get__pow_msg__pow_sign("20b60b6977944282ac7c29fd3ce1d01c", "54088bb07d2df3c46b79f80300b0abbe",
//     'md5', '1', 0, "2023-10-22T22:45:23.367714+08:00", ""
// ))

function get_device_id() {
    var canvas_data = '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'
    canvas_data = canvas_data.split('')
    canvas_data[__.random(0, canvas_data.length - 1, false)] = __.random(0, 9, false)
    canvas_data = canvas_data.join('')
    return new s_encode["MD5"]()["hex"](canvas_data)
}

// console.log(get_device_id())

function Fmhz_(t) {
    var e = 5381;
    var n = t["length"];
    var o = 0;

    while (n--) {
        e = (e << 5) + e + t["charCodeAt"](o++);
    }

    e &= ~(1 << 31);
    return e;
}

// function GErG(t){var dkV=AdJTB.EoY()[3][7];for(;dkV!==AdJTB.EoY()[3][6];){switch(dkV){case AdJTB.EoY()[0][7]:if(t[UBkT(84)]&&t[UBkT(34)]){t[e]=Fmhz(GErG[TioX(50)]()+Fmhz(Fmhz[UBkT(50)]()))+UBkT(78);}return Fmhz(Fmhz[TioX(50)]());break;}}}
function GErG() {
}

Object.defineProperty(GErG, 'toString', {
    value: function () {
        return "function GErG(t){var dkV=AdJTB.EoY()[3][7];for(;dkV!==AdJTB.EoY()[3][6];){switch(dkV){case AdJTB.EoY()[0][7]:if(t[UBkT(84)]&&t[UBkT(34)]){t[e]=Fmhz(GErG[TioX(50)]()+Fmhz(Fmhz[UBkT(50)]()))+UBkT(78);}return Fmhz(Fmhz[TioX(50)]());break;}}}"
    }
})

// function Fmhz(t){var cwl=AdJTB.EoY()[0][7];for(;cwl!==AdJTB.EoY()[3][6];){switch(cwl){case AdJTB.EoY()[3][7]:var e=5381;var n=t[TioX(17)];var o=0;while(n--){e=(e<<5)+e+t[UBkT(26)](o++);}e&=~(1<<31);return e;break;}}}
function Fmhz() {
}

Object.defineProperty(Fmhz, 'toString', {
    value: function () {
        return "function Fmhz(t){var cwl=AdJTB.EoY()[0][7];for(;cwl!==AdJTB.EoY()[3][6];){switch(cwl){case AdJTB.EoY()[3][7]:var e=5381;var n=t[TioX(17)];var o=0;while(n--){e=(e<<5)+e+t[UBkT(26)](o++);}e&=~(1<<31);return e;break;}}}"
    }
})

// console.log(Fmhz_(GErG.toString() + Fmhz_(Fmhz.toString())))

function get_params(lot_number = 'c178b5b48922434c8ddd2f4bc444726e', captcha_id = "54088bb07d2df3c46b79f80300b0abbe", pow_detail = {
    'version': '1',
    'bits': 0,
    'datetime': '2023-11-07T00:12:34.085507+08:00',
    'hashfunc': 'md5'
}, distance = __.random(50, 250, false)) {
    let s_p_u = get__setLeft__passtime__userresponse(distance)
    let pow_msg__pow_sign = get__pow_msg__pow_sign(lot_number, captcha_id,
        pow_detail["hashfunc"], pow_detail["version"], pow_detail["bits"], pow_detail["datetime"], "")
    return {
        'setLeft': s_p_u['setLeft'],
        'passtime': s_p_u['passtime'],
        'userresponse': s_p_u['userresponse'],
        'device_id': get_device_id(),
        'lot_number': lot_number,
        'pow_msg': pow_msg__pow_sign['pow_msg'],
        'pow_sign': pow_msg__pow_sign['pow_sign'],
        'geetest': 'captcha',
        'lang': 'zh',
        'ep': '123',
        'biht': Fmhz_(GErG.toString() + Fmhz_(Fmhz.toString())),
        'T0ep': 'ubaT',
        'em': {
            'cp': 0,
            'ek': '11',
            'nt': 0,
            'ph': 0,
            'sc': 0,
            'si': 0,
            'wd': 1
        }
    }
}

// console.log(JSON.stringify(get_params()))

function rsa_encrypt(text) {
    const publicKey = '00C1E3934D1614465B33053E7F48EE4EC87B14B95EF88947713D25EECBFF7E74C7977D02DC1D9451F79DD5D1C10C29ACB6A9B4D6FB7D0A0279B6719E1772565F09AF627715919221AEF91899CAE08C0D686D748B20A3603BE2318CA6BC2B59706592A9219D0BF05C9F65023A21D2330807252AE0066D59CEEFA5F2748EA80BAB81';
    const publicExponent = '10001';
    const key = new NodeRSA();
    key.importKey({
        n: Buffer.from(publicKey, 'hex'),
        e: parseInt(publicExponent, 16)
    }, 'components-public');
    // key.setOptions({encryptionScheme: 'pkcs1'});
    return key.encrypt(text, 'hex', 'utf-8')
}

// console.log(encryptedData)

function aes_encrypt(text, key, iv = '0000000000000000') {
    var iv = CryptoJS.enc.Utf8.parse(iv)
    var key = CryptoJS.enc.Utf8.parse(key)
    var text = CryptoJS.enc.Utf8.parse(text)

    return CryptoJS.AES.encrypt(text, key, {
        mode: CryptoJS.mode.CBC,
        iv: iv,
        padding: CryptoJS.pad.Pkcs7
    }).ciphertext.toString()
}

var text = '{"setLeft":112,"passtime":630,"userresponse":113.3379105585452,"device_id":"74bdc8b1cf8ae04f096d1fea34b0bc60","lot_number":"59a41d3ff9204eb3bf273ed013915ca6","pow_msg":"1|0|md5|2023-11-08T10:19:48.543266+08:00|54088bb07d2df3c46b79f80300b0abbe|59a41d3ff9204eb3bf273ed013915ca6||8f5d1362eb58a026","pow_sign":"ae9b04d845d2f1c71e44f5814ea2bac4","geetest":"captcha","lang":"zh","ep":"123","biht":"1426265548","T0ep":"ubaT","em":{"ph":0,"cp":0,"ek":"11","wd":1,"nt":0,"si":0,"sc":0}}'
var iv = "0000000000000000"
var key_ = "6c8855039f11c394"

// console.log(aes_encrypt(text,key_,iv))

function get_w(lot_number, captcha_id, pow_detail, distance) {
    pow_detail = JSON.parse(pow_detail)
    let params = get_params(lot_number, captcha_id, pow_detail, distance)
    let random_number = function () {
        function e() {
            return (65536 * (1 + Math["random"]()) | 0)["toString"](16)["substring"](1);
        }

        return function () {
            return e() + e() + e() + e();
        };
    }()()

    // return aes_encrypt(JSON.stringify(params), random_number) + rsa_encrypt(random_number)
    return {
        'a':aes_encrypt(JSON.stringify(params), random_number),
        'b':random_number,
        'params':params
    }
}

// console.log(get_w('6973b3e48e1a4ccc80e15b4a7758d420',"54088bb07d2df3c46b79f80300b0abbe",
//     '{"version": "1", "bits": 0, "datetime": "2023-11-08T13:25:12.957880+08:00", "hashfunc": "md5"}',
//     150))

// console.log(aes_encrypt('{"setLeft":212,"passtime":1088,"userresponse":212.74675927153197,"device_id":"74bdc8b1cf8ae04f096d1fea34b0bc60","lot_number":"ba69236bd9154a0d9642f916da13f904","pow_msg":"1|0|md5|2023-11-08T13:56:57.480961+08:00|54088bb07d2df3c46b79f80300b0abbe|ba69236bd9154a0d9642f916da13f904||64798a9d43077c84","pow_sign":"3bd93661ab03dd54c65c517c9723c42c","geetest":"captcha","lang":"zh","ep":"123","biht":"1426265548","T0ep":"ubaT","em":{"ph":0,"cp":0,"ek":"11","wd":1,"nt":0,"si":0,"sc":0}}', '9bf1c6e617d40047','0000000000000000'))
