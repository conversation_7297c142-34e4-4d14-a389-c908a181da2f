import ddddocr
import requests
import cv2
import numpy as np
from io import BytesIO


def get_distance(bg_url, slice_url):
    slide = ddddocr.DdddOcr(det=False, ocr=False, show_ad=False)
    background_bytes = requests.get(bg_url).content
    target_bytes = requests.get(slice_url).content
    res = slide.slide_match(target_bytes, background_bytes, simple_target=True)

    print(res)
    position = res['target']
    image = cv2.imdecode(np.frombuffer(background_bytes, np.uint8), cv2.IMREAD_COLOR)
    cv2.rectangle(image, (position[0] + 20, position[1] + 17), (position[2] - 15, position[3] - 18), (0, 255, 0),
                  thickness=1)
    cv2.imwrite('test.png', image)
    cv2.imshow('1', image)
    cv2.waitKey()
    cv2.destroyAllWindows()
    slider_distance = res.get("target")[0]
    # 这里需要在识别出来的距离上进行相应的调整，应该是跟图片大小有关导致的，需要自己去进行调整
    slider_distance = slider_distance + 20
    return slider_distance


bg_path = "captcha_v4/e70fbf1d77/slide/e7874d46ba/2022-04-21T09/bg/561fef74af7d45998391eeb63f1b5673.png"
slice_path = "captcha_v4/e70fbf1d77/slide/e7874d46ba/2022-04-21T09/slice/561fef74af7d45998391eeb63f1b5673.png"
# bg_path = "captcha_v4/e70fbf1d77/slide/0af8d91d43/2022-04-21T09/bg/33a8f24a9b234a599036569c9e54a76a.png"
# slice_path = "captcha_v4/e70fbf1d77/slide/0af8d91d43/2022-04-21T09/slice/33a8f24a9b234a599036569c9e54a76a.png"
bg_url = f"https://static.geetest.com/{bg_path}"
slice_url = f"https://static.geetest.com/{slice_path}"
print(bg_url)
print(slice_url)
slider_distance = get_distance(bg_url, slice_url)
print(slider_distance)
