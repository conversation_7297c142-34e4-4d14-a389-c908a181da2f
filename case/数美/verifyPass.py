# -*- coding: utf-8 -*-
import requests
from pathlib import Path
import json
import PIL
import cv2
import time
import execjs
import numpy as np

import random
from datetime import datetime


organization = 'd6tpAY1oV0Kv5jRSgxQr'   # 与使用数美验证码的平台有关系，官网就是这个


def generateTimeFormat():
    now = datetime.now()
    t = lambda n: "0" + str(n) if n < 10 else str(n)
    return str(now.year) + t(now.month) + t(now.day) + t(now.hour) + t(now.minute) + t(now.second)


def getCaptchaUuid():
    c = ""
    o = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678"
    for a in range(18):
        c += random.choice(o)
    return generateTimeFormat() + c


def get_track(distance):
    track_length = random.randint(4, 10)
    track = [[0, -2, 0]]
    m = distance % track_length
    e = int(distance / track_length)
    for i in range(track_length):
        x = (i + 1) * e + m + random.randint(20, 40)
        y = -2 + (random.randint(-1, 10))
        t = (i + 1) * 100 + random.randint(-3, 5)
        if i == track_length - 1:
            x = distance - random.randint(5, 10)
            track.append([x, y, t])
        else:
            track.append([x, y, t])
    return track


def get_distance(bg, tp, im_show=False, save_path=None):
    bg_img = cv2_open(bg)
    tp_gray = cv2_open(tp, flag=cv2.COLOR_BGR2GRAY)
    bg_shift = cv2.pyrMeanShiftFiltering(bg_img, 5, 50)
    tp_gray = cv2.Canny(tp_gray, 255, 255)
    bg_gray = cv2.Canny(bg_shift, 255, 255)
    result = cv2.matchTemplate(bg_gray, tp_gray, cv2.TM_CCOEFF_NORMED)
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
    distance = max_loc[0]
    if save_path or im_show:
        tp_height, tp_width = tp_gray.shape[:2]
        x, y = max_loc
        _x, _y = x + tp_width, y + tp_height
        bg_img = cv2_open(bg)
        cv2.rectangle(bg_img, (x, y), (_x, _y), (0, 0, 255), 2)
        if save_path:
            save_path = Path(save_path).resolve()
            save_path = save_path.parent / f"{save_path.stem}.{distance}{save_path.suffix}"
            save_path = save_path.__str__()
            cv2.imwrite(save_path, bg_img)
        if im_show:
            cv2.imshow('test', bg_img)
            cv2.waitKey(0)
            cv2.destroyAllWindows()

    return max_loc


def cv2_open(img, flag=None):
    if isinstance(img, bytes):
        img = cv2.imdecode(np.frombuffer(img, dtype=np.uint8), 1)
    elif isinstance(img, (str, Path)):
        img = cv2.imread(str(img))
    elif isinstance(img, np.ndarray):
        img = img
    elif isinstance(img, PIL.Image):
        img = cv2.cvtColor(np.asarray(img), cv2.COLOR_RGB2BGR)
    else:
        raise ValueError(f'输入的图片类型无法解析: {type(img)}')
    if flag is not None:
        img = cv2.cvtColor(img, flag)
    return img


def get_js_content(captchaUuid, ):
    '''获取version信息'''
    temp_headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        # 'Cookie': 'Hm_lvt_b96d87c9b1b6a130a14b9fc14dc746c1=1696350543,1696405833,1696425753,1698804153; __root_domain_v=.ishumei.com; _qddaz=QD.134195129975381; sajssdk_2015_cross_new_user=1; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2218b889e3ecc529-00ef48d4f271eb1-745d5777-921600-18b889e3ecd606%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E4%BB%98%E8%B4%B9%E5%B9%BF%E5%91%8A%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC%22%2C%22%24latest_referrer%22%3A%22https%3A%2F%2Fwww.bing.com%2F%22%2C%22%24latest_utm_source%22%3A%22bdss1%22%2C%22%24latest_utm_medium%22%3A%22%E5%93%81%E7%89%8C%22%2C%22%24latest_utm_campaign%22%3A%22%E5%93%81%E7%89%8C%E8%AF%8D%22%2C%22%24latest_utm_content%22%3A%22%E6%95%B0%E7%BE%8E%22%2C%22%24latest_utm_term%22%3A%22%7Bpa_mt_id%7D%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMThiODg5ZTNlY2M1MjktMDBlZjQ4ZDRmMjcxZWIxLTc0NWQ1Nzc3LTkyMTYwMC0xOGI4ODllM2VjZDYwNiJ9%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%2218b889e3ecc529-00ef48d4f271eb1-745d5777-921600-18b889e3ecd606%22%7D; __bid_n=18aad9ebc4580482da82fd; smidV2=20230919212615843b13368fd386535c4b93b8e443ab6c00e4317bf4c5febf0; PHPSESSID=iah1u4nc3gf435dl2jjv3if8ip; Qs_lvt_331993=1698804156; Qs_pv_331993=3759317299717763600; Hm_lpvt_b96d87c9b1b6a130a14b9fc14dc746c1=1698804172; .thumbcache_f240e8e1f73f62cbb1f2162d9ec6195e=W/V+H45O4Gbwes0GYn2c/BnVP6chBjeAd5cxKekC7to7Q22nWcR8eOF8q3Vjt1C9eVWrjqplmPK5OjTyxCrX1Q%3D%3D',
        'Pragma': 'no-cache',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-site',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/118.0.2088.76',
        'sec-ch-ua': '"Chromium";v="118", "Microsoft Edge";v="118", "Not=A?Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }

    response = requests.get('https://console.ishumei.com/new/', headers=temp_headers)

    version = response.text.split('https://castatic.fengkongcloud.cn/pr/v', 1)[1].split('/', 1)[0]

    '''获取sdkver'''
    temp_headers = {
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Pragma': 'no-cache',
        'Referer': 'https://console.ishumei.com/',
        'Sec-Fetch-Dest': 'script',
        'Sec-Fetch-Mode': 'no-cors',
        'Sec-Fetch-Site': 'cross-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/118.0.2088.76',
        'sec-ch-ua': '"Chromium";v="118", "Microsoft Edge";v="118", "Not=A?Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }

    response = requests.get(f'https://castatic.fengkongcloud.cn/pr/v{version}/smcp.min.js',
                            headers=temp_headers)

    sdkver = response.text.split("'SDKVER':'", 1)[1].split("'", 1)[0]

    '''获取小版本号'''
    temp_headers = {
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Origin': 'https://console.ishumei.com',
        'Pragma': 'no-cache',
        'Referer': 'https://console.ishumei.com/',
        'Sec-Fetch-Dest': 'script',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'cross-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/118.0.2088.76',
        'sec-ch-ua': '"Chromium";v="118", "Microsoft Edge";v="118", "Not=A?Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }

    temp_params = {
        'appId': 'default',
        'captchaUuid': captchaUuid,
        'sdkver': sdkver,
        'callback': f'sm_{round(time.time() * 1000)}',
        'model': 'slide',
        'organization': organization,
        'rversion': version,
        'channel': 'DEFAULT',
        'lang': 'zh-cn',
    }

    response = requests.get('https://captcha1.fengkongcloud.cn/ca/v1/conf', params=temp_params, headers=temp_headers)

    json_data = json.loads(response.text.split('(', 1)[1].rsplit(')', 1)[0])

    js_url = "https://castatic.fengkongcloud.cn"+json_data["detail"]["js"]

    js_content = requests.get(js_url, headers=temp_headers).text

    version_info = js_url.split('v', 1)[1].split('/', 1)[0].split('-')

    js_version_info = version_info+[sdkver]

    return js_content, js_version_info


def get_keys(captchaUuid):
    '''
    按照索引获取没有去重的key列表中对应值
    '''
    js_code, js_info = get_js_content(captchaUuid)

    with open('./execjs-获取key.js', 'r', encoding='utf-8') as fp:
        js_call = fp.read()

    keys = execjs.compile(js_call).call('getDynamicParams', js_code)
    return keys, js_info



if __name__ == '__main__':
    captchaUuid = getCaptchaUuid()

    key_list, js_version_info = get_keys(captchaUuid)

    # key_list = [['xt', '1d5a3363'], ['ee', '1822c58f'], ['ra', '3126130b'], ['jv', '16c70d95'], ['jn', 'ed3e6918'], ['ml', '2a29ac9a'], ['ee', '1822c58f'], ['ra', '3126130b'], ['jv', '16c70d95'], ['jn', 'ed3e6918'], ['ml', '2a29ac9a'], ['ml', '2a29ac9a'], ['ee', '1822c58f'], ['ra', '3126130b'], ['jv', '16c70d95'], ['jn', 'ed3e6918'], ['rj', '32f87e96'], ['xc', '6c8902c2'], ['qu', '528b03ef'], ['ma', '98c58424'], ['hd', '2436d078'], ['xy', '1e25b734'], ['us', '05ce67c1']]

    print(f'当前版本信息:\t\t"version":"{js_version_info[0]}", "小版本":"{js_version_info[1]}", "sdkver":"{js_version_info[2]}"')

    headers = {
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Origin': 'https://www.ishumei.com',
        'Pragma': 'no-cache',
        'Referer': 'https://www.ishumei.com/trial/captcha.html',
        'Sec-Fetch-Dest': 'script',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'cross-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/117.0.2045.47',
        'sec-ch-ua': '"Microsoft Edge";v="117", "Not;A=Brand";v="8", "Chromium";v="117"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }

    params = {
        'channel': 'DEFAULT',
        'rversion': js_version_info[0],
        'lang': 'zh-cn',
        'sdkver': js_version_info[2],
        'model': 'slide',
        'appId': 'default',
        'data': '{}',
        'organization': organization,
        'callback': f'sm_{round(time.time() * 1000)}',
        'captchaUuid': captchaUuid,
    }

    response = requests.get('https://captcha1.fengkongcloud.cn/ca/v1/register', params=params, headers=headers).text

    rid = response.split('"rid":"', 1)[1].split('"')[0]
    bg_url = "https://castatic.fengkongcloud.cn" + response.split('"bg":"', 1)[1].split('"')[0]
    tp_url = "https://castatic.fengkongcloud.cn" + response.split('"fg":"', 1)[1].split('"')[0]
    pos = get_distance(
        bg=requests.get(bg_url, headers=headers).content,
        tp=requests.get(tp_url, headers=headers).content,
        # im_show=True
    )[0] / 2

    track = get_track(pos)
    endTime = track[-1][2] + random.randint(0, 100)

    with open('./getInfo.js', 'r', encoding='utf-8') as fp:
        js_code = fp.read()
    ctx = execjs.compile(js_code)
    params = ctx.call('get_params', pos, track, endTime, key_list, js_version_info)
    # print(params)

    params['callback'] = f'sm_{round(time.time() * 1000)-1000}'
    params['rid'] = rid
    params['captchaUuid'] = captchaUuid
    params['organization'] = organization
    response = requests.get('https://captcha1.fengkongcloud.cn/ca/v2/fverify', params=params, headers=headers)
    print(response.text)

