/*
@File   :  get_dynamic_params_ast.js
@Time   :  2023-04-04
<AUTHOR>  公众号：K哥爬虫
@Desc   :  数美验证，动态读取 captcha-sdk.min.js，AST 提取动态参数
@Version:  1.0.1 / 1.0.3 / 1.0.4 通用
@Url    :  https://castatic.fengkongcloud.cn/pr/auto-build/v1.0.4-171/captcha-sdk.min.js
*/

const fs = require("fs");
const parse = require("@babel/parser").parse;
const generate = require("@babel/generator").default;
const traverse = require("@babel/traverse").default;
const types = require("@babel/types");

function getDynamicParams(oldCode){
    // 导入混淆代码并解析为 AST
    const astCode = parse(oldCode);
    // 获取整个 AST 节点的长度
    let astCodeLength = astCode.program.body.length
    // 获取数组位移函数的名称，如 _0x86bf
    let arrDisplacementFuncName = ""

    for (let i = 0; i < astCodeLength; i++) {
        let astCodeBody = astCode.program.body[i]
        if (astCodeBody.type == "FunctionDeclaration" && astCodeBody.params.length === 2) {
            arrDisplacementFuncName = astCodeBody.id.name
        }
    }
    // console.log("arrDisplacementFuncName: ", arrDisplacementFuncName)

    // 获取大数组以及数组位移代码
    let decryptFunction = "";
    for (let i = 0; i < astCodeLength; i++) {
        if (astCode.program.body[i].type == "FunctionDeclaration") {
            decryptFunction += generate(astCode.program.body[i]).code
        }
        if (astCode.program.body[i].type == "ExpressionStatement") {
            let expressions = astCode.program.body[i].expression.expressions
            for (let a = 0; a < expressions.length; a++) {
                if (expressions[a].type == "CallExpression" && expressions[a].callee.type == "FunctionExpression" && expressions[a].arguments.length == 2) {
                    let expressionStatement = types.expressionStatement(expressions[a])
                    decryptFunction += generate(expressionStatement).code
                }
            }
        }
    }
    eval(decryptFunction)

    // 获取所有引用了解密函数的变量名
    let allReplacedFunc = []
    let tmpReplacedFunc1 = [arrDisplacementFuncName]

    while (true) {
        let tmpReplacedFunc2 = []
        traverse(astCode, {
            VariableDeclarator(path) {
                if (path.node.init && path.node.init.type == "Identifier" && tmpReplacedFunc1.indexOf(path.node.init.name)) {
                    tmpReplacedFunc2.push(path.node.id.name)
                    path.remove()
                }
            }
        })
        if (!tmpReplacedFunc2.length) {
            break
        }
        allReplacedFunc = allReplacedFunc.concat(tmpReplacedFunc2)
        tmpReplacedFunc1 = tmpReplacedFunc2
    }
    // console.log(JSON.stringify(allReplacedFunc))


    // 解密函数还原
    traverse(astCode, {
        CallExpression(path) {
            let calleeName = path.node.callee.name;
            if (allReplacedFunc.indexOf(calleeName) > -1) {
                // 先替换函数名称 _0xbce966(0x936) ==> _0x86bf(0x936)
                let callee = types.identifier(arrDisplacementFuncName)
                let argument = path.node.arguments
                path.replaceInline(types.callExpression(callee, argument));
                // 直接计算结果并替换
                path.replaceInline(types.stringLiteral(eval(path.toString())));
            }
        }
    })

    // 大对象还原
    let allObject = {}
    traverse(astCode, {
        VariableDeclarator(path) {
            if (path.node.init && path.node.init.type == "ObjectExpression" && path.node.init.properties.length > 1) {
                let name = path.node.id.name
                let properties = path.node.init.properties
                allObject[name] = {}
                for (let i = 0; i < properties.length; i++) {
                    allObject[name][properties[i].key.value] = properties[i].value
                }
                path.remove()
            }
        }
    })

    // 函数替换，字符串替换：将类似 _0x3b79c6['YrYQW'] 变成 '#IpValue'
    traverse(astCode, {
        MemberExpression(path) {
            for (let key in allObject) {
                if (path.node.object && path.node.object.name == key && path.inList) {
                    path.replaceInline(allObject[key][path.node.property.value])
                }
                if (path.node.object && path.node.object.name == key && path.parent.property && path.parent.property.value == "split") {
                    path.replaceInline(allObject[key][path.node.property.value])
                }
                if (path.node.object && path.node.object.name == key && path.parent.type == "SwitchCase") {
                    path.replaceInline(allObject[key][path.node.property.value])
                }
            }
        }
    })

    // 获取动态post参数和加密的key
    let parametersList = [];
    traverse(astCode, {
        AssignmentExpression(path) {
            let left = path.node.left
            let right = path.node.right
            if (right.callee && right.callee.property && right.callee.property.value == "getEncryptContent") {
                parametersList.push([left.property.value, right.arguments[1].value])
            }
        },
        CallExpression(path) {
            let arguments = path.node.arguments
            if (arguments.length == 3 && arguments[2].callee && arguments[2].callee.property && arguments[2].callee.property.value == "getEncryptContent") {
                parametersList.push([arguments[1].value, arguments[2].arguments[1].value])
            }
        }
    })

    // 将 AST 节点转换成 JS 代码并写入到新文件里
    // const result = generate(astCode).code
    // fs.writeFile("./outPut.js", result, (err => {console.log(err)}))

    return parametersList
}
