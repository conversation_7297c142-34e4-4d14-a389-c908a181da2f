var CryptoJS = require("crypto-js")


function DESEncrypt(word, key) {
    word = typeof word === 'string' ? word : JSON.stringify(word)
    var key_ = CryptoJS.enc.Utf8.parse(key);
    var srcs = CryptoJS.enc.Utf8.parse(word);
    var encrypted = CryptoJS.DES.encrypt(srcs, key_, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.ZeroPadding
    });
    return encrypted.toString();
}
function get_params(mouseEndX, mouseData, slide_time, key_list, js_version_info) {
    json_str = `{"${key_list[19][0]}"` + '___"' + DESEncrypt("default", key_list[19][1]) + '",' +
    `"${key_list[20][0]}"` + '___"' + DESEncrypt("DEFAULT", key_list[20][1]) + '",' +
    `"${key_list[21][0]}"` + '___"' + DESEncrypt("zh-cn", key_list[21][1]) + '",' +
    `"${key_list[22][0]}"` + '___"' + DESEncrypt("10", key_list[22][1]) + '",' +
    '"rversion"' + '___' + `"${js_version_info[0]}"` + ',' +
    '"sdkver"' + '___' + `"${js_version_info[2]}"` + ',' +
    '"protocol"' + '___' + `"${js_version_info[1]}"` + ',' +
    '"ostype"' + '___"' + "web" + '",' +
    `"${key_list[5][0]}"` + '___"' + DESEncrypt(mouseEndX / 300, key_list[5][1]) + '",' +
    `"${key_list[1][0]}"` + '___"' + DESEncrypt(mouseData, key_list[1][1]) + '",' +
    `"${key_list[2][0]}"` + '___"' + DESEncrypt(slide_time, key_list[2][1]) + '",' +
    `"${key_list[3][0]}"` + '___"' + DESEncrypt(300, key_list[3][1]) + '",' +
    `"${key_list[4][0]}"` + '___"' + DESEncrypt(150, key_list[4][1]) + '",' +
    "\"act.os\"" + '___"' + "web_pc" + '",' +
    `"${key_list[16][0]}"` + '___"' + DESEncrypt('1', key_list[16][1]) + '",' +
    `"${key_list[17][0]}"` + '___"' + DESEncrypt("0", key_list[17][1]) + '",' +
    `"${key_list[18][0]}"` + '___"' + DESEncrypt("-1", key_list[18][1])+'"}'

    return JSON.parse(json_str.replace(/___/g, ':'))
}

// key_list = [['xt', '1d5a3363'], ['ee', '1822c58f'], ['ra', '3126130b'], ['jv', '16c70d95'], ['jn', 'ed3e6918'], ['ml', '2a29ac9a'], ['ee', '1822c58f'], ['ra', '3126130b'], ['jv', '16c70d95'], ['jn', 'ed3e6918'], ['ml', '2a29ac9a'], ['ml', '2a29ac9a'], ['ee', '1822c58f'], ['ra', '3126130b'], ['jv', '16c70d95'], ['jn', 'ed3e6918'], ['rj', '32f87e96'], ['xc', '6c8902c2'], ['qu', '528b03ef'], ['ma', '98c58424'], ['hd', '2436d078'], ['xy', '1e25b734'], ['us', '05ce67c1']]
// get_params(48.5, [[0, -2, 0], [43.5, 2, 97], [55.5, 3, 202], [58.5, 6, 304], [72.5, -1, 404], [41.5, 0, 498]], 588, key_list, ['1.0.4', '179', '1.1.3'])
