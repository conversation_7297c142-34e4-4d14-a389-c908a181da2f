import json
import re
import requests
import execjs
import urllib3
from loguru import logger

urllib3.disable_warnings()
headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 "
                  "Safari/537.36",
}


def get_cookies(url):
    cookies = {}

    resp_first = session.get(url=url, headers=headers, verify=False, timeout=3)
    cookies.update(resp_first.cookies)  # 第一次获取

    js_clearance = re.findall('cookie=(.*?);location', resp_first.text)[0]
    aa_result = execjs.eval(js_clearance).split(';')[0]  # 第一次生成

    cookies['__jsl_clearance_s'] = aa_result.split("=")[1]
    resp_second = session.get(url=url, headers=headers, cookies=cookies, verify=False, timeout=3)
    dd = re.findall(r';go\((.*?)\)</script>', resp_second.text)[0]
    params = json.loads(dd)
    with open('jiasule.js', 'r', encoding='utf-8') as f:
        jsl_js = f.read()
    js_clearance_s = execjs.compile(jsl_js).call('cookies', params)  # 第二次生成

    cookies.update(js_clearance_s)
    cookies.update(session.cookies.get_dict())
    resp_third = requests.get(url=url, headers=headers, cookies=cookies, proxies={
        'http': 'http://************:30636',
        'https': 'http://************:30636'
    }, verify=False)
    resp_third.encoding = 'utf-8'
    logger.info(resp_third.text)

    return cookies


if __name__ == '__main__':
    session = requests.session()
    session.proxies = {
        'http': 'http://************:30636',
        'https': 'http://************:30636'
    }

    cookies_ = get_cookies("https://amr.sz.gov.cn/outer/entSelect/gs.html")
    logger.info(cookies_)
