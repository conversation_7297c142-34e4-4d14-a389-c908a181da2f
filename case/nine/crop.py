from PIL import Image
import io


def crop_grid_image(image: bytes, tile_dim=112, line_w=4):
    # 打开原始图片
    img = Image.open(io.BytesIO(image))
    img_width, img_height = img.size
    img_height = img_height - 40

    tile_count = 0
    images: list[Image.Image] = []
    # 循环遍历 3x3 网格
    for row in range(3):
        for col in range(3):
            tile_count += 1

            # 计算当前小图的左上角坐标
            # x 坐标 = 列号 * (小图宽度 + 分割线宽度)
            left = col * (tile_dim + line_w)
            # y 坐标 = 行号 * (小图高度 + 分割线宽度)
            top = row * (tile_dim + line_w)

            # 计算当前小图的右下角坐标
            # x 坐标 = 左上角 x + 小图宽度
            right = left + tile_dim
            # y 坐标 = 左上角 y + 小图高度
            bottom = top + tile_dim

            # 定义裁剪区域 (left, top, right, bottom)
            # Pillow 的 crop 方法需要一个四元组
            box = (left, top, right, bottom)

            tile = img.crop(box)
            images.append(tile)

    img_temp = img.crop((0, img_height, 45, img_height + 40))
    images.append(img_temp)
    return images


if __name__ == '__main__':
    pass
