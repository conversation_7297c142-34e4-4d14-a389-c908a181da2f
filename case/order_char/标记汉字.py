import asyncio
import aiohttp
from pathlib import Path
from loguru import logger
import time
from hashlib import md5

OCR_URL = "http://127.0.0.1:8000/ocr/"
IMAGE_DIR = Path("/Users/<USER>/temp/jy3_语序点选/error")


def is_chinese_char(ch: str) -> bool:
    """判断单个字符 ch 是否在常用汉字的 Unicode 码点范围内。"""
    if len(ch) != 1:
        return False
    code = ord(ch)
    return (
            0x4E00 <= code <= 0x9FFF or  # 常用汉字
            0x3400 <= code <= 0x4DBF or  # 扩展 A
            0x20000 <= code <= 0x2A6DF  # 扩展 B
    )


async def ocr_image(session: aiohttp.ClientSession, image_path: Path):
    start = time.perf_counter()
    logger.info(f"{image_path.name} - 开始: {start:.3f}s")

    data = aiohttp.FormData()
    data.add_field("image",
                   image_path.read_bytes(),
                   filename=image_path.name,
                   content_type="image/png")

    async with session.post(OCR_URL, data=data) as resp:
        result = await resp.json()

    end = time.perf_counter()
    logger.info(f"{image_path.name} - 结束: {end:.3f}s，耗时: {(end - start):.3f}s")
    return image_path, result['result'], result['score']


async def bound_ocr(semaphore: asyncio.Semaphore, session, path):
    async with semaphore:
        return await ocr_image(session, path)


async def main():
    semaphore = asyncio.Semaphore(50)
    image_paths = [p for p in IMAGE_DIR.iterdir() if p.is_file() and p.name != '.DS_Store']
    async with aiohttp.ClientSession() as session:
        tasks = [bound_ocr(semaphore, session, p) for p in image_paths][:]
        results = await asyncio.gather(*tasks, return_exceptions=False)

    for src_path, result, score in results:
        if not is_chinese_char(result):
            continue
        # if src_path.name.split('_')[0] != result:
        #     continue

        image_bytes = src_path.read_bytes()
        md5sum = md5(image_bytes).hexdigest()
        new_name = f"{result}_{md5sum}.png"

        if 0.9 >= score > 0.8:
            RESULT_DIR = Path("/Users/<USER>/temp/jy3_语序点选/result_0.8")
        elif 0.8 >= score > 0.7:
            RESULT_DIR = Path("/Users/<USER>/temp/jy3_语序点选/result_0.7")
        # elif 0.7 >= score > 0.6:
        #     RESULT_DIR = Path("/Users/<USER>/temp/jy3_语序点选/result_0.6")
        # elif 0.6 >= score > 0.5:
        #     RESULT_DIR = Path("/Users/<USER>/temp/jy3_语序点选/result_0.5")
        else:
            RESULT_DIR = Path("/Users/<USER>/temp/jy3_语序点选/result_")
        dst_path = RESULT_DIR / new_name
        RESULT_DIR.mkdir(parents=True, exist_ok=True)
        src_path.rename(dst_path)
        print(f"{src_path.name} -> moved to {dst_path}")


if __name__ == "__main__":
    asyncio.run(main())
