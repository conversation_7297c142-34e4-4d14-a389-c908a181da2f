function random(min, max, floating = false) {
    if (floating) {
        return Math.random() * (max - min) + min;
    } else {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }
}

const __ = {
    random
}
window = global
var ue = {
    appName: "Netscape"
}


function rsa(u) {
    var F = function () {
        function n() {
            this["i"] = 0, this["j"] = 0, this["S"] = [];
        }

        n["prototype"]["init"] = function E(e) {
            var t, n, r;

            for (t = 0; t < 256; ++t) this["S"][t] = t;

            for (t = n = 0; t < 256; ++t) n = n + this["S"][t] + e[t % e["length"]] & 255, r = this["S"][t], this["S"][t] = this["S"][n], this["S"][n] = r;

            this["i"] = 0, this["j"] = 0;
        }, n["prototype"]["next"] = function C() {
            var e;
            return this["i"] = this["i"] + 1 & 255, this["j"] = this["j"] + this["S"][this["i"]] & 255, e = this["S"][this["i"]], this["S"][this["i"]] = this["S"][this["j"]], this["S"][this["j"]] = e, this["S"][e + this["S"][this["i"]] & 255];
        };
        var r,
            i,
            s,
            e,
            o = 256;

        if (null == i) {
            var t;

            if (i = [], s = 0, window["crypto"] && window["crypto"]["getRandomValues"]) {
                var _ = new Uint32Array(256);

                for (window["crypto"]["getRandomValues"](_), t = 0; t < _["length"]; ++t) i[s++] = 255 & _[t];
            }

            var a = 0,
                c = function (e) {
                    if (256 <= (a = a || 0) || o <= s) window["removeEventListener"] ? (a = 0, window["removeEventListener"]("mousemove", c, !1)) : window["detachEvent"] && (a = 0, window["detachEvent"]("onmousemove", c)); else try {
                        var t = e["x"] + e["y"];
                        i[s++] = 255 & t, a += 1;
                    } catch (n) {
                    }
                };

            window["addEventListener"] ? window["addEventListener"]("mousemove", c, !1) : window["attachEvent"] && window["attachEvent"]("onmousemove", c);
        }

        function l() {
            if (null == r) {
                r = function t() {
                    return new n();
                }();

                while (s < o) {
                    var e = Math["floor"](65536 * Math["random"]());
                    i[s++] = 255 & e;
                }

                for (r["init"](i), s = 0; s < i["length"]; ++s) i[s] = 0;

                s = 0;
            }

            return r["next"]();
        }

        function u() {
        }

        u["prototype"]["nextBytes"] = function S(e) {
            var t;

            for (t = 0; t < e["length"]; ++t) e[t] = l();
        };

        function w(e, t, n) {
            null != e && ("number" == typeof e ? this["fromNumber"](e, t, n) : null == t && "string" != typeof e ? this["fromString"](e, 256) : this["fromString"](e, t));
        }

        function x() {
            return new w(null);
        }

        e = "Microsoft Internet Explorer" == ue["appName"] ? (w["prototype"]["am"] = function T(e, t, n, r, i, s) {
            var o = 32767 & t,
                _ = t >> 15;

            while (0 <= --s) {
                var a = 32767 & this[e],
                    c = this[e++] >> 15,
                    l = _ * a + c * o;
                i = ((a = o * a + ((32767 & l) << 15) + n[r] + (1073741823 & i)) >>> 30) + (l >>> 15) + _ * c + (i >>> 30), n[r++] = 1073741823 & a;
            }

            return i;
        }, 30) : "Netscape" != ue["appName"] ? (w["prototype"]["am"] = function O(e, t, n, r, i, s) {
            while (0 <= --s) {
                var o = t * this[e++] + n[r] + i;
                i = Math["floor"](o / 67108864), n[r++] = 67108863 & o;
            }

            return i;
        }, 26) : (w["prototype"]["am"] = function A(e, t, n, r, i, s) {
            var o = 16383 & t,
                _ = t >> 14;

            while (0 <= --s) {
                var a = 16383 & this[e],
                    c = this[e++] >> 14,
                    l = _ * a + c * o;
                i = ((a = o * a + ((16383 & l) << 14) + n[r] + i) >> 28) + (l >> 14) + _ * c, n[r++] = 268435455 & a;
            }

            return i;
        }, 28), w["prototype"]["DB"] = e, w["prototype"]["DM"] = (1 << e) - 1, w["prototype"]["DV"] = 1 << e;
        w["prototype"]["FV"] = Math["pow"](2, 52), w["prototype"]["F1"] = 52 - e, w["prototype"]["F2"] = 2 * e - 52;
        var h,
            p,
            d = "0123456789abcdefghijklmnopqrstuvwxyz",
            g = [];

        for (h = "0"["charCodeAt"](0), p = 0; p <= 9; ++p) g[h++] = p;

        for (h = "a"["charCodeAt"](0), p = 10; p < 36; ++p) g[h++] = p;

        for (h = "A"["charCodeAt"](0), p = 10; p < 36; ++p) g[h++] = p;

        function f(e) {
            return d["charAt"](e);
        }

        function v(e) {
            var t = x();
            return t["fromInt"](e), t;
        }

        function y(e) {
            var t,
                n = 1;
            return 0 != (t = e >>> 16) && (e = t, n += 16), 0 != (t = e >> 8) && (e = t, n += 8), 0 != (t = e >> 4) && (e = t, n += 4), 0 != (t = e >> 2) && (e = t, n += 2), 0 != (t = e >> 1) && (e = t, n += 1), n;
        }

        function m(e) {
            this["m"] = e;
        }

        function b(e) {
            this["m"] = e, this["mp"] = e["invDigit"](), this["mpl"] = 32767 & this["mp"], this["mph"] = this["mp"] >> 15, this["um"] = (1 << e["DB"] - 15) - 1, this["mt2"] = 2 * e["t"];
        }

        function k() {
            this["n"] = null, this["e"] = 0, this["d"] = null, this["p"] = null, this["q"] = null, this["dmp1"] = null, this["dmq1"] = null, this["coeff"] = null;
            this["setPublic"]("00C1E3934D1614465B33053E7F48EE4EC87B14B95EF88947713D25EECBFF7E74C7977D02DC1D9451F79DD5D1C10C29ACB6A9B4D6FB7D0A0279B6719E1772565F09AF627715919221AEF91899CAE08C0D686D748B20A3603BE2318CA6BC2B59706592A9219D0BF05C9F65023A21D2330807252AE0066D59CEEFA5F2748EA80BAB81", "10001");
        }

        return m["prototype"]["convert"] = function D(e) {
            return e["s"] < 0 || 0 <= e["compareTo"](this["m"]) ? e["mod"](this["m"]) : e;
        }, m["prototype"]["revert"] = function M(e) {
            return e;
        }, m["prototype"]["reduce"] = function B(e) {
            e["divRemTo"](this["m"], null, e);
        }, m["prototype"]["mulTo"] = function R(e, t, n) {
            e["multiplyTo"](t, n), this["reduce"](n);
        }, m["prototype"]["sqrTo"] = function j(e, t) {
            e["squareTo"](t), this["reduce"](t);
        }, b["prototype"]["convert"] = function N(e) {
            var t = x();
            return e["abs"]()["dlShiftTo"](this["m"]["t"], t), t["divRemTo"](this["m"], null, t), e["s"] < 0 && 0 < t["compareTo"](w["ZERO"]) && this["m"]["subTo"](t, t), t;
        }, b["prototype"]["revert"] = function L(e) {
            var t = x();
            return e["copyTo"](t), this["reduce"](t), t;
        }, b["prototype"]["reduce"] = function z(e) {
            while (e["t"] <= this["mt2"]) e[e["t"]++] = 0;

            for (var t = 0; t < this["m"]["t"]; ++t) {
                var n = 32767 & e[t],
                    r = n * this["mpl"] + ((n * this["mph"] + (e[t] >> 15) * this["mpl"] & this["um"]) << 15) & e["DM"];
                e[n = t + this["m"]["t"]] += this["m"]["am"](0, r, e, t, 0, this["m"]["t"]);

                while (e[n] >= e["DV"]) e[n] -= e["DV"], e[++n]++;
            }

            e["clamp"](), e["drShiftTo"](this["m"]["t"], e), 0 <= e["compareTo"](this["m"]) && e["subTo"](this["m"], e);
        }, b["prototype"]["mulTo"] = function P(e, t, n) {
            e["multiplyTo"](t, n), this["reduce"](n);
        }, b["prototype"]["sqrTo"] = function I(e, t) {
            e["squareTo"](t), this["reduce"](t);
        }, w["prototype"]["copyTo"] = function q(e) {
            for (var t = this["t"] - 1; 0 <= t; --t) e[t] = this[t];

            e["t"] = this["t"], e["s"] = this["s"];
        }, w["prototype"]["fromInt"] = function F(e) {
            this["t"] = 1, this["s"] = e < 0 ? -1 : 0, 0 < e ? this[0] = e : e < -1 ? this[0] = e + this["DV"] : this["t"] = 0;
        }, w["prototype"]["fromString"] = function H(e, t) {
            var n;
            if (16 == t) n = 4; else if (8 == t) n = 3; else if (256 == t) n = 8; else if (2 == t) n = 1; else if (32 == t) n = 5; else {
                if (4 != t) return void this["fromRadix"](e, t);
                n = 2;
            }
            this["t"] = 0, this["s"] = 0;
            var r,
                i,
                s = e["length"],
                o = !1,
                _ = 0;

            while (0 <= --s) {
                var a = 8 == n ? 255 & e[s] : (r = s, null == (i = g[e["charCodeAt"](r)]) ? -1 : i);
                a < 0 ? "-" == e["charAt"](s) && (o = !0) : (o = !1, 0 == _ ? this[this["t"]++] = a : _ + n > this["DB"] ? (this[this["t"] - 1] |= (a & (1 << this["DB"] - _) - 1) << _, this[this["t"]++] = a >> this["DB"] - _) : this[this["t"] - 1] |= a << _, (_ += n) >= this["DB"] && (_ -= this["DB"]));
            }

            8 == n && 0 != (128 & e[0]) && (this["s"] = -1, 0 < _ && (this[this["t"] - 1] |= (1 << this["DB"] - _) - 1 << _)), this["clamp"](), o && w["ZERO"]["subTo"](this, this);
        }, w["prototype"]["clamp"] = function X() {
            var e = this["s"] & this["DM"];

            while (0 < this["t"] && this[this["t"] - 1] == e) --this["t"];
        }, w["prototype"]["dlShiftTo"] = function $(e, t) {
            var n;

            for (n = this["t"] - 1; 0 <= n; --n) t[n + e] = this[n];

            for (n = e - 1; 0 <= n; --n) t[n] = 0;

            t["t"] = this["t"] + e, t["s"] = this["s"];
        }, w["prototype"]["drShiftTo"] = function V(e, t) {
            for (var n = e; n < this["t"]; ++n) t[n - e] = this[n];

            t["t"] = Math["max"](this["t"] - e, 0), t["s"] = this["s"];
        }, w["prototype"]["lShiftTo"] = function Y(e, t) {
            var n,
                r = e % this["DB"],
                i = this["DB"] - r,
                s = (1 << i) - 1,
                o = Math["floor"](e / this["DB"]),
                _ = this["s"] << r & this["DM"];

            for (n = this["t"] - 1; 0 <= n; --n) t[n + o + 1] = this[n] >> i | _, _ = (this[n] & s) << r;

            for (n = o - 1; 0 <= n; --n) t[n] = 0;

            t[o] = _, t["t"] = this["t"] + o + 1, t["s"] = this["s"], t["clamp"]();
        }, w["prototype"]["rShiftTo"] = function U(e, t) {
            t["s"] = this["s"];
            var n = Math["floor"](e / this["DB"]);
            if (n >= this["t"]) t["t"] = 0; else {
                var r = e % this["DB"],
                    i = this["DB"] - r,
                    s = (1 << r) - 1;
                t[0] = this[n] >> r;

                for (var o = n + 1; o < this["t"]; ++o) t[o - n - 1] |= (this[o] & s) << i, t[o - n] = this[o] >> r;

                0 < r && (t[this["t"] - n - 1] |= (this["s"] & s) << i), t["t"] = this["t"] - n, t["clamp"]();
            }
        }, w["prototype"]["subTo"] = function J(e, t) {
            var n = 0,
                r = 0,
                i = Math["min"](e["t"], this["t"]);

            while (n < i) r += this[n] - e[n], t[n++] = r & this["DM"], r >>= this["DB"];

            if (e["t"] < this["t"]) {
                r -= e["s"];

                while (n < this["t"]) r += this[n], t[n++] = r & this["DM"], r >>= this["DB"];

                r += this["s"];
            } else {
                r += this["s"];

                while (n < e["t"]) r -= e[n], t[n++] = r & this["DM"], r >>= this["DB"];

                r -= e["s"];
            }

            t["s"] = r < 0 ? -1 : 0, r < -1 ? t[n++] = this["DV"] + r : 0 < r && (t[n++] = r), t["t"] = n, t["clamp"]();
        }, w["prototype"]["multiplyTo"] = function G(e, t) {
            var n = this["abs"](),
                r = e["abs"](),
                i = n["t"];
            t["t"] = i + r["t"];

            while (0 <= --i) t[i] = 0;

            for (i = 0; i < r["t"]; ++i) t[i + n["t"]] = n["am"](0, r[i], t, i, 0, n["t"]);

            t["s"] = 0, t["clamp"](), this["s"] != e["s"] && w["ZERO"]["subTo"](t, t);
        }, w["prototype"]["squareTo"] = function W(e) {
            var t = this["abs"](),
                n = e["t"] = 2 * t["t"];

            while (0 <= --n) e[n] = 0;

            for (n = 0; n < t["t"] - 1; ++n) {
                var r = t["am"](n, t[n], e, 2 * n, 0, 1);
                (e[n + t["t"]] += t["am"](n + 1, 2 * t[n], e, 2 * n + 1, r, t["t"] - n - 1)) >= t["DV"] && (e[n + t["t"]] -= t["DV"], e[n + t["t"] + 1] = 1);
            }

            0 < e["t"] && (e[e["t"] - 1] += t["am"](n, t[n], e, 2 * n, 0, 1)), e["s"] = 0, e["clamp"]();
        }, w["prototype"]["divRemTo"] = function Z(e, t, n) {
            var r = e["abs"]();

            if (!(r["t"] <= 0)) {
                var i = this["abs"]();
                if (i["t"] < r["t"]) return null != t && t["fromInt"](0), void (null != n && this["copyTo"](n));
                null == n && (n = x());
                var s = x(),
                    o = this["s"],
                    _ = e["s"],
                    a = this["DB"] - y(r[r["t"] - 1]);
                0 < a ? (r["lShiftTo"](a, s), i["lShiftTo"](a, n)) : (r["copyTo"](s), i["copyTo"](n));
                var c = s["t"],
                    l = s[c - 1];

                if (0 != l) {
                    var u = l * (1 << this["F1"]) + (1 < c ? s[c - 2] >> this["F2"] : 0),
                        h = this["FV"] / u,
                        p = (1 << this["F1"]) / u,
                        d = 1 << this["F2"],
                        g = n["t"],
                        f = g - c,
                        v = null == t ? x() : t;
                    s["dlShiftTo"](f, v), 0 <= n["compareTo"](v) && (n[n["t"]++] = 1, n["subTo"](v, n)), w["ONE"]["dlShiftTo"](c, v), v["subTo"](s, s);

                    while (s["t"] < c) s[s["t"]++] = 0;

                    while (0 <= --f) {
                        var m = n[--g] == l ? this["DM"] : Math["floor"](n[g] * h + (n[g - 1] + d) * p);

                        if ((n[g] += s["am"](0, m, n, f, 0, c)) < m) {
                            s["dlShiftTo"](f, v), n["subTo"](v, n);

                            while (n[g] < --m) n["subTo"](v, n);
                        }
                    }

                    null != t && (n["drShiftTo"](c, t), o != _ && w["ZERO"]["subTo"](t, t)), n["t"] = c, n["clamp"](), 0 < a && n["rShiftTo"](a, n), o < 0 && w["ZERO"]["subTo"](n, n);
                }
            }
        }, w["prototype"]["invDigit"] = function Q() {
            if (this["t"] < 1) return 0;
            var e = this[0];
            if (0 == (1 & e)) return 0;
            var t = 3 & e;
            return 0 < (t = (t = (t = (t = t * (2 - (15 & e) * t) & 15) * (2 - (255 & e) * t) & 255) * (2 - ((65535 & e) * t & 65535)) & 65535) * (2 - e * t % this["DV"]) % this["DV"]) ? this["DV"] - t : -t;
        }, w["prototype"]["isEven"] = function K() {
            return 0 == (0 < this["t"] ? 1 & this[0] : this["s"]);
        }, w["prototype"]["exp"] = function ee(e, t) {
            if (4294967295 < e || e < 1) return w["ONE"];
            var n = x(),
                r = x(),
                i = t["convert"](this),
                s = y(e) - 1;
            i["copyTo"](n);

            while (0 <= --s) if (t["sqrTo"](n, r), 0 < (e & 1 << s)) t["mulTo"](r, i, n); else {
                var o = n;
                n = r, r = o;
            }

            return t["revert"](n);
        }, w["prototype"]["toString"] = function te(e) {
            if (this["s"] < 0) return "-" + this["negate"]()["toString"](e);
            var t;
            if (16 == e) t = 4; else if (8 == e) t = 3; else if (2 == e) t = 1; else if (32 == e) t = 5; else {
                if (4 != e) return this["toRadix"](e);
                t = 2;
            }

            var n,
                r = (1 << t) - 1,
                i = !1,
                s = "",
                o = this["t"],
                _ = this["DB"] - o * this["DB"] % t;

            if (0 < o--) {
                _ < this["DB"] && 0 < (n = this[o] >> _) && (i = !0, s = f(n));

                while (0 <= o) _ < t ? (n = (this[o] & (1 << _) - 1) << t - _, n |= this[--o] >> (_ += this["DB"] - t)) : (n = this[o] >> (_ -= t) & r, _ <= 0 && (_ += this["DB"], --o)), 0 < n && (i = !0), i && (s += f(n));
            }

            return i ? s : "0";
        }, w["prototype"]["negate"] = function ne() {
            var e = x();
            return w["ZERO"]["subTo"](this, e), e;
        }, w["prototype"]["abs"] = function re() {
            return this["s"] < 0 ? this["negate"]() : this;
        }, w["prototype"]["compareTo"] = function ie(e) {
            var t = this["s"] - e["s"];
            if (0 != t) return t;
            var n = this["t"];
            if (0 != (t = n - e["t"])) return this["s"] < 0 ? -t : t;

            while (0 <= --n) if (0 != (t = this[n] - e[n])) return t;

            return 0;
        }, w["prototype"]["bitLength"] = function se() {
            return this["t"] <= 0 ? 0 : this["DB"] * (this["t"] - 1) + y(this[this["t"] - 1] ^ this["s"] & this["DM"]);
        }, w["prototype"]["mod"] = function $_ER(e) {
            var t = x();
            return this["abs"]()["divRemTo"](e, null, t), this["s"] < 0 && 0 < t["compareTo"](w["ZERO"]) && e["subTo"](t, t), t;
        }, w["prototype"]["modPowInt"] = function $_Fd(e, t) {
            var n;
            return n = e < 256 || t["isEven"]() ? new m(t) : new b(t), this["exp"](e, n);
        }, w["ZERO"] = v(0), w["ONE"] = v(1), k["prototype"]["doPublic"] = function $_GB(e) {
            return e["modPowInt"](this["e"], this["n"]);
        }, k["prototype"]["setPublic"] = function $_HH(e, t) {
            null != e && null != t && 0 < e["length"] && 0 < t["length"] ? (this["n"] = function n(e, t) {
                return new w(e, t);
            }(e, 16), this["e"] = parseInt(t, 16)) : console && console["error"] && console["error"]("Invalid RSA public key");
        }, k["prototype"]["encrypt"] = function $_I_(e) {
            var t = function _(e, t) {
                if (t < e["length"] + 11) return console && console["error"] && console["error"]("Message too long for RSA"), null;
                var n = [],
                    r = e["length"] - 1;

                while (0 <= r && 0 < t) {
                    var i = e["charCodeAt"](r--);
                    i < 128 ? n[--t] = i : 127 < i && i < 2048 ? (n[--t] = 63 & i | 128, n[--t] = i >> 6 | 192) : (n[--t] = 63 & i | 128, n[--t] = i >> 6 & 63 | 128, n[--t] = i >> 12 | 224);
                }

                n[--t] = 0;
                var s = new u(),
                    o = [];

                while (2 < t) {
                    o[0] = 0;

                    while (0 == o[0]) s["nextBytes"](o);

                    n[--t] = o[0];
                }

                return n[--t] = 2, n[--t] = 0, new w(n);
            }(e, this["n"]["bitLength"]() + 7 >> 3);

            if (null == t) return null;
            var n = this["doPublic"](t);
            if (null == n) return null;
            var r = n["toString"](16);
            return 0 == (1 & r["length"]) ? r : "0" + r;
        }, k;
    }();

    return new F()["encrypt"](u)
}

function aes(text, u) {
    var H = function () {
        var e,
            n = Object["create"] || function () {
                function n() {
                }

                return function (e) {
                    var t;
                    return n["prototype"] = e, t = new n(), n["prototype"] = null, t;
                };
            }(),
            t = {},
            r = t["lib"] = {},
            i = r["Base"] = {
                "extend": function (e) {
                    var t = n(this);
                    return e && t["mixIn"](e), t["hasOwnProperty"]("init") && this["init"] !== t["init"] || (t["init"] = function () {
                        t["$super"]["init"]["apply"](this, arguments);
                    }), (t["init"]["prototype"] = t)["$super"] = this, t;
                },
                "create": function () {
                    var e = this["extend"]();
                    return e["init"]["apply"](e, arguments), e;
                },
                "init": function () {
                },
                "mixIn": function (e) {
                    for (var t in e) e["hasOwnProperty"](t) && (this[t] = e[t]);

                    e["hasOwnProperty"]("toString") && (this["toString"] = e["toString"]);
                }
            },
            l = r["WordArray"] = i["extend"]({
                "init": function (e, t) {
                    e = this["words"] = e || [], t != undefined ? this["sigBytes"] = t : this["sigBytes"] = 4 * e["length"];
                },
                "concat": function (e) {
                    var t = this["words"],
                        n = e["words"],
                        r = this["sigBytes"],
                        i = e["sigBytes"];
                    if (this["clamp"](), r % 4) for (var s = 0; s < i; s++) {
                        var o = n[s >>> 2] >>> 24 - s % 4 * 8 & 255;
                        t[r + s >>> 2] |= o << 24 - (r + s) % 4 * 8;
                    } else for (s = 0; s < i; s += 4) t[r + s >>> 2] = n[s >>> 2];
                    return this["sigBytes"] += i, this;
                },
                "clamp": function () {
                    var e = this["words"],
                        t = this["sigBytes"];
                    e[t >>> 2] &= 4294967295 << 32 - t % 4 * 8, e["length"] = Math["ceil"](t / 4);
                }
            }),
            s = t["enc"] = {},
            u = s["Latin1"] = {
                "parse": function (e) {
                    for (var t = e["length"], n = [], r = 0; r < t; r++) n[r >>> 2] |= (255 & e["charCodeAt"](r)) << 24 - r % 4 * 8;

                    return new l["init"](n, t);
                }
            },
            o = s["Utf8"] = {
                "parse": function (e) {
                    return u["parse"](unescape(encodeURIComponent(e)));
                }
            },
            _ = r["BufferedBlockAlgorithm"] = i["extend"]({
                "reset": function () {
                    this["$_DCc"] = new l["init"](), this["$_DDH"] = 0;
                },
                "$_DEK": function (e) {
                    "string" == typeof e && (e = o["parse"](e)), this["$_DCc"]["concat"](e), this["$_DDH"] += e["sigBytes"];
                },
                "$_DFW": function (e) {
                    var t = this["$_DCc"],
                        n = t["words"],
                        r = t["sigBytes"],
                        i = this["blockSize"],
                        s = r / (4 * i),
                        o = (s = e ? Math["ceil"](s) : Math["max"]((0 | s) - this["$_DGZ"], 0)) * i,
                        _ = Math["min"](4 * o, r);

                    if (o) {
                        for (var a = 0; a < o; a += i) this["$_DHf"](n, a);

                        var c = n["splice"](0, o);
                        t["sigBytes"] -= _;
                    }

                    return new l["init"](c, _);
                },
                "$_DGZ": 0
            }),
            a = t["algo"] = {},
            c = r["Cipher"] = _["extend"]({
                "cfg": i["extend"](),
                "createEncryptor": function (e, t) {
                    return this["create"](this["$_DIt"], e, t);
                },
                "init": function (e, t, n) {
                    this["cfg"] = this["cfg"]["extend"](n), this["$_DJe"] = e, this["$_EAh"] = t, this["reset"]();
                },
                "reset": function () {
                    _["reset"]["call"](this), this["$_EBL"]();
                },
                "process": function (e) {
                    return this["$_DEK"](e), this["$_DFW"]();
                },
                "finalize": function (e) {
                    return e && this["$_DEK"](e), this["$_ECI"]();
                },
                "keySize": 4,
                "ivSize": 4,
                "$_DIt": 1,
                "$_EDu": 2,
                "$_EEt": function (c) {
                    return {
                        "encrypt": function (e, t, n) {
                            t = u["parse"](t), n && n["iv"] || ((n = n || {})["iv"] = u["parse"]("0000000000000000"));

                            for (var r = m["encrypt"](c, e, t, n), i = r["ciphertext"]["words"], s = r["ciphertext"]["sigBytes"], o = [], _ = 0; _ < s; _++) {
                                var a = i[_ >>> 2] >>> 24 - _ % 4 * 8 & 255;
                                o["push"](a);
                            }

                            return o;
                        }
                    };
                }
            }),
            h = t["mode"] = {},
            p = r["BlockCipherMode"] = i["extend"]({
                "createEncryptor": function (e, t) {
                    return this["Encryptor"]["create"](e, t);
                },
                "init": function (e, t) {
                    this["$_EFb"] = e, this["$_EGp"] = t;
                }
            }),
            d = h["CBC"] = ((e = p["extend"]())["Encryptor"] = e["extend"]({
                "processBlock": function (e, t) {
                    var n = this["$_EFb"],
                        r = n["blockSize"];
                    (function o(e, t, n) {
                        var r = this["$_EGp"];

                        if (r) {
                            var i = r;
                            this["$_EGp"] = undefined;
                        } else var i = this["$_EHW"];

                        for (var s = 0; s < n; s++) e[t + s] ^= i[s];
                    })["call"](this, e, t, r), n["encryptBlock"](e, t), this["$_EHW"] = e["slice"](t, t + r);
                }
            }), e),
            g = (t["pad"] = {})["Pkcs7"] = {
                "pad": function (e, t) {
                    for (var n = 4 * t, r = n - e["sigBytes"] % n, i = r << 24 | r << 16 | r << 8 | r, s = [], o = 0; o < r; o += 4) s["push"](i);

                    var _ = l["create"](s, r);

                    e["concat"](_);
                }
            },
            f = r["BlockCipher"] = c["extend"]({
                "cfg": c["cfg"]["extend"]({
                    "mode": d,
                    "padding": g
                }),
                "reset": function () {
                    c["reset"]["call"](this);
                    var e = this["cfg"],
                        t = e["iv"],
                        n = e["mode"];
                    if (this["$_DJe"] == this["$_DIt"]) var r = n["createEncryptor"];
                    this["$_EIw"] && this["$_EIw"]["$_EJf"] == r ? this["$_EIw"]["init"](this, t && t["words"]) : (this["$_EIw"] = r["call"](n, this, t && t["words"]), this["$_EIw"]["$_EJf"] = r);
                },
                "$_DHf": function (e, t) {
                    this["$_EIw"]["processBlock"](e, t);
                },
                "$_ECI": function () {
                    var e = this["cfg"]["padding"];

                    if (this["$_DJe"] == this["$_DIt"]) {
                        e["pad"](this["$_DCc"], this["blockSize"]);
                        var t = this["$_DFW"](!0);
                    }

                    return t;
                },
                "blockSize": 4
            }),
            v = r["CipherParams"] = i["extend"]({
                "init": function (e) {
                    this["mixIn"](e);
                }
            }),
            m = r["SerializableCipher"] = i["extend"]({
                "cfg": i["extend"](),
                "encrypt": function (e, t, n, r) {
                    r = this["cfg"]["extend"](r);
                    var i = e["createEncryptor"](n, r),
                        s = i["finalize"](t),
                        o = i["cfg"];
                    return v["create"]({
                        "ciphertext": s,
                        "key": n,
                        "iv": o["iv"],
                        "algorithm": e,
                        "mode": o["mode"],
                        "padding": o["padding"],
                        "blockSize": e["blockSize"],
                        "formatter": r["format"]
                    });
                }
            }),
            w = [],
            x = [],
            y = [],
            b = [],
            k = [],
            E = [],
            C = [],
            S = [],
            T = [],
            O = [];

        !function () {
            for (var e = [], t = 0; t < 256; t++) e[t] = t < 128 ? t << 1 : t << 1 ^ 283;

            var n = 0,
                r = 0;

            for (t = 0; t < 256; t++) {
                var i = r ^ r << 1 ^ r << 2 ^ r << 3 ^ r << 4;
                i = i >>> 8 ^ 255 & i ^ 99, w[n] = i;
                var s = e[x[i] = n],
                    o = e[s],
                    _ = e[o],
                    a = 257 * e[i] ^ 16843008 * i;
                y[n] = a << 24 | a >>> 8, b[n] = a << 16 | a >>> 16, k[n] = a << 8 | a >>> 24, E[n] = a;
                a = 16843009 * _ ^ 65537 * o ^ 257 * s ^ 16843008 * n;
                C[i] = a << 24 | a >>> 8, S[i] = a << 16 | a >>> 16, T[i] = a << 8 | a >>> 24, O[i] = a, n ? (n = s ^ e[e[e[_ ^ s]]], r ^= e[e[r]]) : n = r = 1;
            }
        }();
        var A = [0, 1, 2, 4, 8, 16, 32, 64, 128, 27, 54],
            D = a["AES"] = f["extend"]({
                "$_EBL": function () {
                    if (!this["$_FAT"] || this["$_FBV"] !== this["$_EAh"]) {
                        for (var e = this["$_FBV"] = this["$_EAh"], t = e["words"], n = e["sigBytes"] / 4, r = 4 * (1 + (this["$_FAT"] = 6 + n)), i = this["$_FCi"] = [], s = 0; s < r; s++) if (s < n) i[s] = t[s]; else {
                            var o = i[s - 1];
                            s % n ? 6 < n && s % n == 4 && (o = w[o >>> 24] << 24 | w[o >>> 16 & 255] << 16 | w[o >>> 8 & 255] << 8 | w[255 & o]) : (o = w[(o = o << 8 | o >>> 24) >>> 24] << 24 | w[o >>> 16 & 255] << 16 | w[o >>> 8 & 255] << 8 | w[255 & o], o ^= A[s / n | 0] << 24), i[s] = i[s - n] ^ o;
                        }

                        for (var _ = this["$_FDk"] = [], a = 0; a < r; a++) {
                            s = r - a;
                            if (a % 4) o = i[s]; else o = i[s - 4];
                            _[a] = a < 4 || s <= 4 ? o : C[w[o >>> 24]] ^ S[w[o >>> 16 & 255]] ^ T[w[o >>> 8 & 255]] ^ O[w[255 & o]];
                        }
                    }
                },
                "encryptBlock": function (e, t) {
                    this["$_FEg"](e, t, this["$_FCi"], y, b, k, E, w);
                },
                "$_FEg": function (e, t, n, r, i, s, o, _) {
                    for (var a = this["$_FAT"], c = e[t] ^ n[0], l = e[t + 1] ^ n[1], u = e[t + 2] ^ n[2], h = e[t + 3] ^ n[3], p = 4, d = 1; d < a; d++) {
                        var g = r[c >>> 24] ^ i[l >>> 16 & 255] ^ s[u >>> 8 & 255] ^ o[255 & h] ^ n[p++],
                            f = r[l >>> 24] ^ i[u >>> 16 & 255] ^ s[h >>> 8 & 255] ^ o[255 & c] ^ n[p++],
                            v = r[u >>> 24] ^ i[h >>> 16 & 255] ^ s[c >>> 8 & 255] ^ o[255 & l] ^ n[p++],
                            m = r[h >>> 24] ^ i[c >>> 16 & 255] ^ s[l >>> 8 & 255] ^ o[255 & u] ^ n[p++];
                        c = g, l = f, u = v, h = m;
                    }

                    g = (_[c >>> 24] << 24 | _[l >>> 16 & 255] << 16 | _[u >>> 8 & 255] << 8 | _[255 & h]) ^ n[p++], f = (_[l >>> 24] << 24 | _[u >>> 16 & 255] << 16 | _[h >>> 8 & 255] << 8 | _[255 & c]) ^ n[p++], v = (_[u >>> 24] << 24 | _[h >>> 16 & 255] << 16 | _[c >>> 8 & 255] << 8 | _[255 & l]) ^ n[p++], m = (_[h >>> 24] << 24 | _[c >>> 16 & 255] << 16 | _[l >>> 8 & 255] << 8 | _[255 & u]) ^ n[p++];
                    e[t] = g, e[t + 1] = f, e[t + 2] = v, e[t + 3] = m;
                },
                "keySize": 8
            });
        return t["AES"] = f["$_EEt"](D), t["AES"];
    }();
    var he = {
        "$_BCGs": {
            "$_BCHg": "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789()",
            "$_BCIX": ".",
            "$_BCJP": 7274496,
            "$_BDAR": 9483264,
            "$_BDBT": 19220,
            "$_BDCI": 235,
            "$_BDDE": 24
        },
        "$_BCHg": "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789()",
        "$_BCIX": ".",
        "$_BCJP": 7274496,
        "$_BDAR": 9483264,
        "$_BDBT": 19220,
        "$_BDCI": 235,
        "$_BDDE": 24,
        "$_BDEA": function (e) {
            for (var t = [], n = 0, r = e["length"]; n < r; n += 1) t["push"](e["charCodeAt"](n));

            return t;
        },
        "$_BDFH": function (e) {
            for (var t = "", n = 0, r = e["length"]; n < r; n += 1) t += String["fromCharCode"](e[n]);

            return t;
        },
        "$_BDGf": function (e) {
            var t = this["$_BCHg"];
            return e < 0 || e >= t["length"] ? "." : t["charAt"](e);
        },
        "$_BDHh": function (e) {
            return this["$_BCHg"]["indexOf"](e);
        },
        "$_BDII": function (e, t) {
            return e >> t & 1;
        },
        "$_BDJJ": function (e, i) {
            var s = this;
            i || (i = s);

            for (var t = function (e, t) {
                for (var n = 0, r = i["$_BDDE"] - 1; 0 <= r; r -= 1) 1 === s["$_BDII"](t, r) && (n = (n << 1) + s["$_BDII"](e, r));

                return n;
            }, n = "", r = "", o = e["length"], _ = 0; _ < o; _ += 3) {
                var a;
                if (_ + 2 < o) a = (e[_] << 16) + (e[_ + 1] << 8) + e[_ + 2], n += s["$_BDGf"](t(a, i["$_BCJP"])) + s["$_BDGf"](t(a, i["$_BDAR"])) + s["$_BDGf"](t(a, i["$_BDBT"])) + s["$_BDGf"](t(a, i["$_BDCI"])); else {
                    var c = o % 3;
                    2 == c ? (a = (e[_] << 16) + (e[_ + 1] << 8), n += s["$_BDGf"](t(a, i["$_BCJP"])) + s["$_BDGf"](t(a, i["$_BDAR"])) + s["$_BDGf"](t(a, i["$_BDBT"])), r = i["$_BCIX"]) : 1 == c && (a = e[_] << 16, n += s["$_BDGf"](t(a, i["$_BCJP"])) + s["$_BDGf"](t(a, i["$_BDAR"])), r = i["$_BCIX"] + i["$_BCIX"]);
                }
            }

            return {
                "res": n,
                "end": r
            };
        },
        "$_BEAU": function (e) {
            var t = this["$_BDJJ"](this["$_BDEA"](e));
            return t["res"] + t["end"];
        },
        "$_BEBT": function (e) {
            var t = this["$_BDJJ"](e);
            return t["res"] + t["end"];
        },
        "$_BECE": function (e, s) {
            var o = this;
            s || (s = o);

            for (var t = function (e, t) {
                if (e < 0) return 0;

                for (var n = 5, r = 0, i = s["$_BDDE"] - 1; 0 <= i; i -= 1) 1 === o["$_BDII"](t, i) && (r += o["$_BDII"](e, n) << i, n -= 1);

                return r;
            }, n = e["length"], r = "", i = 0; i < n; i += 4) {
                var _ = t(o["$_BDHh"](e["charAt"](i)), s["$_BCJP"]) + t(o["$_BDHh"](e["charAt"](i + 1)), s["$_BDAR"]) + t(o["$_BDHh"](e["charAt"](i + 2)), s["$_BDBT"]) + t(o["$_BDHh"](e["charAt"](i + 3)), s["$_BDCI"]),
                    a = _ >> 16 & 255;

                if (r += String["fromCharCode"](a), e["charAt"](i + 2) !== s["$_BCIX"]) {
                    var c = _ >> 8 & 255;

                    if (r += String["fromCharCode"](c), e["charAt"](i + 3) !== s["$_BCIX"]) {
                        var l = 255 & _;
                        r += String["fromCharCode"](l);
                    }
                }
            }

            return r;
        },
        "$_BEDt": function (e) {
            var t = 4 - e["length"] % 4;
            if (t < 4) for (var n = 0; n < t; n += 1) e += this["$_BCIX"];
            return this["$_BECE"](e);
        },
        "$_BEEv": function (e) {
            return this["$_BEDt"](e);
        }
    }

    h = H['encrypt'](text, u)
    return he["$_BEBT"](h)
}

var J = function () {
    function e() {
        return (65536 * (1 + Math["random"]()) | 0)["toString"](16)["substring"](1);
    }

    return function () {
        return e() + e() + e() + e();
    };
}();

function get_params1(gt, challenge, gettype) {
    return {
        "gt": gt,
        "challenge": challenge,
        "offline": false,
        "product": "float",
        "width": "300px",
        "protocol": "https://",
        "type": "fullpage",
        "static_servers": gettype["static_servers"],
        "beeline": gettype["beeline"],
        "voice": gettype["voice"],
        "click": gettype["click"],
        "fullpage": gettype["fullpage"],
        "slide": gettype["slide"],
        "geetest": gettype["geetest"],
        "aspect_radio": gettype['aspect_radio'],
        "cc": 14, // 12
        "ww": true,
        "i": "-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1"
    }
}

function get_w1(gt, challenge, gettype) {
    let key = J()
    let u = rsa(key)
    let params = get_params1(gt, challenge, gettype)
    let p = aes(JSON.stringify(params), key)
    return {
        'params': params,
        'w': p + u,
        'key': key
    }
}

/**
 * {
 *     "gt": "c78cc92860d6664d3e95a46084d4eced",
 *     "challenge": "8db4a2d792fcab3a6f4d3f86bba76415",
 *     "offline": false,
 *     "product": "float",
 *     "width": "326px",
 *     "protocol": "https://",
 *     "type": "fullpage",
 *     "static_servers": [
 *         "static.geetest.com/",
 *         "static.geevisit.com/"
 *     ],
 *     "beeline": "/static/js/beeline.1.0.1.js",
 *     "voice": "/static/js/voice.1.2.6.js",
 *     "click": "/static/js/click.3.1.2.js",
 *     "fullpage": "/static/js/fullpage.9.2.0-cf92nc.js",
 *     "slide": "/static/js/slide.7.9.3.js",
 *     "geetest": "/static/js/geetest.6.0.9.js",
 *     "aspect_radio": {
 *         "slide": 103,
 *         "click": 128,
 *         "voice": 128,
 *         "beeline": 50
 *     },
 *     "cc": 14,
 *     "ww": true,
 *     "i": "-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1!!-1"
 * }
 */