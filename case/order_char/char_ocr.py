import os
from io import BytesIO
from itertools import permutations

import cv2
import ddddocr
import kenlm
import requests
import uvicorn
from PIL import Image
from fastapi import FastAPI, File, UploadFile
from ultralytics import YOLO

app = FastAPI()
yo8_model = YOLO('../dianxuan/best.onnx', task='detect')
det = ddddocr.DdddOcr(det=True, show_ad=False)
ocr = ddddocr.DdddOcr(det=False, show_ad=False)
ocr2 = ddddocr.DdddOcr(det=False, show_ad=False, beta=True)


class WordOrder(object):
    def __init__(self, model_path):
        self.model = kenlm.LanguageModel(model_path)

    def n_gram(self, word):
        word_list = list(word)
        # n-gram
        candidate_list = list(permutations(word_list, r=len(word_list)))
        a_c_s = -100
        a_c = ""
        b_c_s = 1000
        for candidate in candidate_list:
            candidate = ' '.join(candidate)
            a = self.model.score(candidate)
            b = self.model.perplexity(candidate)
            if a > a_c_s:
                a_c = candidate
                a_c_s = a
            if b_c_s > b:
                b_c_s = b
        return a_c.replace(" ", '')

    def predict(self, text):
        return self.n_gram(text)


def get_coordinates_yolo(image: bytes):
    image = Image.open(BytesIO(image))
    result = yo8_model.predict(source=image, show=False, save=False, imgsz=320, device='cpu')[0]
    xyxy = result.boxes.xyxy.tolist()
    return xyxy


def crop_image(image: bytes, coordinates, save_to_output: bool = False):
    image = Image.open(BytesIO(image))
    cropped_images = []

    # 如果需要保存，创建output文件夹
    if save_to_output:
        output_dir = "output"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

    for i, bbox in enumerate(coordinates):
        x1, y1, x2, y2 = bbox
        temp_image = image.crop((x1 - 5, y1 - 5, x2 + 5, y2 + 5))
        cropped_images.append(temp_image)

        # 保存裁剪的图片
        if save_to_output:
            save_path = os.path.join(output_dir, f"cropped_{i}.png")
            temp_image.save(save_path)

    return cropped_images


def image_to_bytes(img: Image.Image, format='PNG') -> bytes:
    img_bytes = BytesIO()
    img.save(img_bytes, format=format)
    img_bytes.seek(0)
    return img_bytes.getvalue()


def dddd_ocr_conf(result):
    recognized_text = ""
    confidence_scores = []

    for i in result['probability']:
        max_prob = max(i)
        max_idx = i.index(max_prob)
        if not result['charsets'][max_idx]:
            continue
        recognized_text += result['charsets'][max_idx]
        confidence_scores.append(max_prob)

    return recognized_text, sum(confidence_scores) / len(confidence_scores)


def dddd_ocr(img: Image.Image):
    img_bytes_data = image_to_bytes(img)
    result = ocr.classification(img_bytes_data, probability=True)
    result2 = ocr2.classification(img_bytes_data, probability=True)
    char, conf = dddd_ocr_conf(result)
    char2, conf2 = dddd_ocr_conf(result2)
    if conf > conf2:
        return {'result': char, 'score': conf}
    else:
        return {'result': char2, 'score': conf2}


def yolo_ocr(img: list[Image.Image]):
    ocr_url = "http://127.0.0.1:8000/char_ocr/"
    files = [('images', ('image.png', image_to_bytes(i), 'image/png')) for i in img]
    res = requests.post(ocr_url, files=files)
    return res.json()


def rapid_ocr(img: Image.Image):
    ocr_url = "http://127.0.0.1:9000/ocr/"
    files = {'image': ('image.png', image_to_bytes(img), 'image/png')}
    res = requests.post(ocr_url, files=files)
    return res.json()


char_order = WordOrder('/Users/<USER>/learn/pycharm/captcha/models/zh_giga.no_cna_cmn.prune01244.klm')


def marked_order(path: str, char_pos: dict, string_order: str):
    # 加载原图并标记顺序
    image = cv2.imread(path)
    for order, char in enumerate(string_order, 1):
        x1, y1, x2, y2 = char_pos[char]
        center_x = int((x1 + x2) / 2)
        center_y = int((y1 + y2) / 2)

        # 绘制矩形框
        cv2.rectangle(image, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
        # 绘制顺序数字
        cv2.putText(image, str(order), (center_x - 10, center_y + 10),
                    cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)

    # 保存标记后的图片
    output_path = 'marked_order.jpg'
    cv2.imwrite(output_path, image)
    print(f"标记图片已保存到: {output_path}")


@app.post("/order_char/")
async def main(image: UploadFile = File(...)):
    """主函数，处理上传的图片并进行字符识别和排序"""
    image_content = await image.read()
    pos = get_coordinates_yolo(image_content)
    cropped_images_ = crop_image(image_content, pos, False)
    aa = yolo_ocr(cropped_images_)
    char_pos = {}
    string = ''
    for idx, aaa in enumerate(aa):
        char_, conf_ = list(aaa.items())[0]
        if conf_ < 0.7:
            char_dd = dddd_ocr(cropped_images_[idx])
            rapid_ocr_res = rapid_ocr(cropped_images_[idx])
            if rapid_ocr_res['score'] > conf_ and rapid_ocr_res['score'] > char_dd['score']:
                char_, conf_ = rapid_ocr_res['result'], rapid_ocr_res['score']
            else:
                char_, conf_ = char_dd['result'], char_dd['score']
        char_pos[char_] = pos[idx]
        string += char_
        print(f"字符: {char_}, 置信度: {conf_}")
    string_order = char_order.predict(string)
    mid_xy = []
    for char in string_order:
        xyxy = char_pos[char]
        mid_xy.append((round(xyxy[0] + xyxy[2]) / 2, round((xyxy[1] + xyxy[3]) / 2)))
    return mid_xy


if __name__ == '__main__':
    uvicorn.run(app, host="127.0.0.1", port=6666)
