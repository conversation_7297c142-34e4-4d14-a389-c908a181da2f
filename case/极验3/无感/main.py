import execjs
import random
import time
from loguru import logger
import urllib3
import requests
import json
import re

urllib3.disable_warnings()

headers = {
    "authority": "www.geetest.com",
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-language": "zh-CN,zh;q=0.9",
    "cache-control": "no-cache",
    "pragma": "no-cache",
    "referer": "https://www.geetest.com/demo/",
    "sec-ch-ua": "\"Google Chrome\";v=\"119\", \"Chromium\";v=\"119\", \"Not?A_Brand\";v=\"24\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "same-origin",
    "sec-fetch-user": "?1",
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36"
}


def register():
    url = "https://www.geetest.com/demo/gt/register-fullpage"
    params = {
        "t": f"{int(time.time() * 1000)}"
    }
    response = session.get(url, headers=headers, params=params, verify=False)

    logger.success(response.status_code)
    logger.info(response.json())

    return response.json()


def gettype(register_json):
    # url = "https://apiv6.geetest.com/gettype.php"
    url = 'https://api.geetest.com/gettype.php'
    params = {
        "gt": register_json['gt'],
        "callback": f"geetest_{int(time.time() * 1000)}"
    }
    response = session.get(url, headers=headers, params=params, verify=False)
    res = json.loads(re.search(r'geetest_\d{13}\((.*)\)', response.text).group(1))

    logger.success(response.status_code)
    logger.info(res)

    return res


def get1(register_json, gettype_json):
    url = "https://apiv6.geetest.com/get.php"

    js_str = open('w1.js', 'r', encoding='utf8').read()
    compile_js = execjs.compile(js_str)
    w_params = compile_js.call('get_w1', register_json['gt'], register_json['challenge'], gettype_json)
    params = {
        "gt": register_json['gt'],
        "challenge": register_json['challenge'],
        "lang": "zh-cn",
        "pt": "0",
        "client_type": "web",
        "w": w_params['w'],
        "callback": f"geetest_{int(time.time() * 1000)}"
    }

    response = session.get(url, headers=headers, params=params, verify=False)
    res = json.loads(re.search(r'geetest_\d{13}\((.*)\)', response.text).group(1))

    logger.success(response.status_code)
    logger.info(res)

    return res


def ajax1(register_json):
    url = "https://api.geevisit.com/ajax.php"
    params = {
        "gt": register_json['gt'],
        "challenge": register_json['challenge'],
        "lang": "zh-cn",
        "pt": "0",
        "client_type": "web",
        "w": "",
        "callback": f"geetest_{int(time.time() * 1000)}"
    }

    response = session.get(url, headers=headers, params=params, verify=False)
    res = json.loads(re.search(r'geetest_\d{13}\((.*)\)', response.text).group(1))

    logger.success(response.status_code)
    logger.info(res)

    return res


def get_gct_js_code(url):
    # headers.update({
    #     "Host": "static.geetest.com"
    # })
    js_code = requests.get(url, headers=headers, verify=False).text
    gct = re.findall(
        r'\[0];(function [a-zA-Z]{4}\([a-zA-Z]{1}\){var .*;break;}}})(function [a-zA-Z]{4}\([a-zA-Z]{1}\){var .*\(\);break;}}})return function\(',
        js_code)[0]

    return gct[0], gct[1]


def main():
    gt_challenge = register()
    g = gettype(gt_challenge)
    a = get1(gt_challenge, g)
    # ajax1_ = ajax1(gt_challenge)

    # captcha_type = ajax1_['data']['result']

    # get2_ = get2(gt_challenge, captcha_type)
    # ajax2(get2_, captcha_type)


if __name__ == '__main__':
    session = requests.session()
    main()
