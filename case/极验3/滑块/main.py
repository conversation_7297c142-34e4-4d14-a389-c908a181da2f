import random
import time
from loguru import logger
import urllib3
import requests
import json
import re
import cv2
import numpy as np
import execjs

# from .verify import verify, verify2
from case.极验3.滑块.verify import verify, verify2
# from .滑块还原 import convert
from case.极验3.滑块.滑块还原 import convert
# from .track import slide_track
from case.极验3.滑块.track import slide_track

urllib3.disable_warnings()
session = None

headers = {
    "Origin": "https://www.geetest.com",
    "Referer": "https://www.geetest.com/Register",
    "User-Agent": "Mozilla/5.0(WindowsNT10.0;Win64;x64)AppleWebKit/537.36(KHTML,"
                  "likeGecko)Chrome/83.0.4103.116Safari/537.36"
}


def register():
    # url = "https://www.geetest.com/demo/gt/register-slide"
    url = 'https://www.geetest.com/api/user/register/register-sms'
    params = {
        "t": str(int(time.time() * 1000))
    }

    # response = session.get(url, headers=headers, params=params, verify=False)
    response = requests.post(url, headers=headers, verify=False)

    # logger.success(response.status_code)
    # logger.info(response.json())

    return response.json()


def register2():
    headers = {
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 Edg/131.0.0.0",
    }
    url = "https://gsxt.amr.guizhou.gov.cn/registerValidate.jspx"
    params = {"t": str(int(time.time() * 1000))}
    response = requests.post(url, headers=headers, params=params)
    # response = requests.post(url, headers=headers)
    return response.json()


def gettype(register_json):
    # url = "https://apiv6.geetest.com/gettype.php"
    url = 'https://api.geetest.com/gettype.php'
    params = {
        "gt": register_json['gt'],
        "callback": f"geetest_{int(time.time() * 1000)}"
    }
    response = session.get(url, headers=headers, params=params, verify=False)
    res = json.loads(re.search(r'geetest_\d{13}\((.*)\)', response.text).group(1))

    # logger.success(response.status_code)
    # logger.info(res)

    return res


def get1(register_json):
    # url = "https://apiv6.geetest.com/get.php"
    url = "http://api.geetest.com/get.php"
    params = {
        "gt": register_json['gt'],
        "challenge": register_json['challenge'],

        # "lang": "zh-cn",
        # "pt": "0",
        # "client_type": "web",
        # "w": "",
        "product": "popup",
        "offline": "false",
        "protocol": "https://",
        "type": "slide",
        "path": "/static/js/geetest.6.0.9.js",

        "callback": f"geetest_{int(time.time() * 1000)}"
    }

    response = session.get(url, headers=headers, params=params, verify=False)
    res = json.loads(re.search(r'geetest_\d{13}\((.*)\)', response.text).group(1))

    # logger.success(response.status_code)
    # logger.info(res)

    return res


def ajax1(register_json):
    # url = "https://api.geetest.com/ajax.php"
    url = "http://api.geevisit.com/ajax.php"
    params = {
        "gt": register_json['gt'],
        "challenge": register_json['challenge'],
        "lang": "zh-cn",
        "pt": "0",
        # "client_type": "web",
        "w": "",
        "callback": f"geetest_{int(time.time() * 1000)}"
    }

    response = session.get(url, headers=headers, params=params, verify=False)
    res = json.loads(re.search(r'geetest_\d{13}\((.*)\)', response.text).group(1))

    # logger.success(response.status_code)
    # logger.info(res)

    return res


def get2(register_json, captcha_type):
    # url = "https://api.geetest.com/get.php"
    url = "http://api.geevisit.com/get.php"
    params = {
        "is_next": "true",
        "type": captcha_type,
        "gt": register_json['gt'],
        "challenge": register_json['challenge'],
        "lang": "zh-cn",
        "https": "true",
        "protocol": "https://",
        "offline": "false",
        "product": "float",
        "api_server": "api.geevisit.com",
        "isPC": "true",
        "autoReset": "true",
        "width": "100%",
        "callback": f"geetest_{int(time.time() * 1000)}"
    }

    response = session.get(url, headers=headers, params=params, verify=False)
    res = json.loads(re.search(r'geetest_\d{13}\((.*)\)', response.text).group(1))

    # logger.success(response.status_code)
    # logger.info(res)

    return res


def get_pic(bg_url, fullbg_url, slice_url):
    bg_content = convert(session.get(bg_url, headers=headers, verify=False).content, 'bg')
    fullbg_content = convert(session.get(fullbg_url, headers=headers).content, 'fullbg')
    slice_content = session.get(slice_url, headers=headers).content
    # with open(f'slice.png', 'wb') as f:
    #     f.write(bg_content)

    return bg_content, fullbg_content, slice_content


def get_gct_js_code(url):
    js_code = requests.get(url, headers=headers, verify=False).text
    gct = re.findall(
        r'\[0];(function [a-zA-Z]{4}\([a-zA-Z]{1}\){var .*;break;}}})(function [a-zA-Z]{4}\([a-zA-Z]{1}\){var .*\('
        r'\);break;}}})return function\(', js_code)[0]

    return gct[0], gct[1]


def ajax2(get2_json, captcha_type):
    if captcha_type == 'slide':
        bg_url = 'https://static.geetest.com/' + get2_json['bg']
        fullbg_url = 'https://static.geetest.com/' + get2_json['fullbg']
        slice_url = 'https://static.geetest.com/' + get2_json['slice']
        c = get2_json['c']
        s = get2_json['s']
        gt = get2_json['gt']
        challenge = get2_json['challenge']

        bg_content, fullbg_content, slice_content = get_pic(bg_url, fullbg_url, slice_url)
        distance = verify(bg_content, fullbg_content) - 5
        # logger.info(f'distance {distance}')

        gct_url = 'https://static.geetest.com' + get2_json['gct_path']
        code1, code2 = get_gct_js_code(gct_url)

        # tracks = generate_track(distance, random.randint(1000, 2000))
        # tracks, _ = get_slide_track(distance)

        tracks = slide_track.get(distance) or slide_track.get(distance - 1) or slide_track.get(
            distance + 1) or slide_track.get(distance + 2) or slide_track.get(distance - 2)

        js_str = open(r'E:\demo_program\python\pycharm\案例\极验3\滑块\w.js', 'r', encoding='utf8').read()
        compile_js = execjs.compile(js_str)
        w_params = compile_js.call('get_w', challenge, gt, tracks, c, s, code1, code2)

        # logger.info(w_params['w'])
        # logger.info(w_params['params'])

        time.sleep(1)
        # url = "https://api.geetest.com/ajax.php"
        url = "http://api.geevisit.com/ajax.php"
        params = {
            "gt": gt,
            "challenge": challenge,
            "lang": "zh-cn",
            "%24_BCX": "0",
            "client_type": "web",
            "w": w_params['w'],
            'pt': '0',
            "callback": f"geetest_{int(time.time() * 1000)}"
        }

        response = session.get(url, params=params, headers=headers)
        res = json.loads(re.search(r'geetest_\d{13}\((.*)\)', response.text).group(1))

        # logger.success(response.status_code)
        logger.info(res)

        return res


def main(gt_challenge):
    # gt_challenge = register()
    # gt_challenge = register2()

    global session
    session = requests.session()

    gettype(gt_challenge)
    a = get1(gt_challenge)
    # ajax1_ = ajax1(gt_challenge)

    # captcha_type = ajax1_['data']['result']
    captcha_type = 'slide'

    # get2_ = get2(gt_challenge, captcha_type)
    return ajax2(a, captcha_type), a['challenge']


def web(gt_challenge):
    global headers
    headers = {
        "Referer": "https://ss.cods.org.cn/",
        "Origin": "https://ss.cods.org.cn/",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                      "Chrome/121.0.0.0 Safari/537.36",
    }

    global session
    session = requests.session()

    gettype(gt_challenge)
    a = get1(gt_challenge)
    ajax1_ = ajax1(gt_challenge)

    captcha_type = ajax1_['data']['result']

    get2_ = get2(gt_challenge, captcha_type)
    return ajax2(get2_, captcha_type), get2_['challenge']


if __name__ == '__main__':
    for i in range(1):
        session = requests.session()
        main()
