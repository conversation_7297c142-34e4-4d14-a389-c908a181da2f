import requests
import time

from main import main

headers = {
    "Host": "gsxt.amr.guizhou.gov.cn",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "origin": "https://gsxt.amr.guizhou.gov.cn",
}

url = "https://gsxt.amr.guizhou.gov.cn/index.jspx"
session = requests.session()
response = session.get(url, headers=headers)
print(session.cookies.get_dict())

url = "https://gsxt.amr.guizhou.gov.cn/registerValidate.jspx"
params = {"t": str(int(time.time() * 1000))}
response2 = requests.get(url, headers=headers, params=params)
print(response2.json())

validate_, challenge = main(response2.json())

url = "https://gsxt.amr.guizhou.gov.cn/validateSecond.jspx"
data = {
    "searchText": "贵州城宇科技发展有限公司",
    "geetest_challenge": challenge,
    "geetest_validate": validate_['validate'],
    "geetest_seccode": f"{validate_['validate']}|jordan"
}
response3 = session.post(url, headers=headers, data=data)
print(response3.json())
