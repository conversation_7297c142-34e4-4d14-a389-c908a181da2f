import io
from PIL import Image

Ut = [
    39, 38, 48, 49, 41, 40, 46, 47, 35, 34, 50,
    51, 33, 32, 28, 29, 27, 26, 36, 37, 31, 30,
    44, 45, 43, 42, 12, 13, 23, 22, 14, 15, 21,
    20, 8, 9, 25, 24, 6, 7, 3, 2, 0, 1,
    11, 10, 4, 5, 19, 18, 16, 17
]


def convert(pic_bytes, type_):
    img = Image.open(io.BytesIO(pic_bytes))
    height = img.height
    width = img.width

    right_image = Image.new('RGB', (260, 160), 255)

    a = height / 2
    for i in range(52):
        c = Ut[i] % 26 * 12 + 1
        u = a if 25 < Ut[i] else 0
        l = img.crop((int(c), int(u), int(c) + 10, int(u) + int(a)))
        x = int(i % 26 * 10)
        y = int(a if 25 < i else 0)
        right_image.paste(l, (x, y))

    new_image = right_image.crop((0, 0, 260, 160))
    if type_ == 'bg':
        # new_image.save('./bg.png')
        # new_image.save('./gap.png')
        pass
    elif type_ == 'fullbg':
        # new_image.save('./fullbg.png')
        # new_image.save('./full.png')
        pass

    image_bytes_io = io.BytesIO()
    new_image.save(image_bytes_io, format='png')

    return image_bytes_io.getvalue()


if __name__ == '__main__':
    pic_bytes = open(r"C:\Users\<USER>\Downloads\bg.jpg", 'rb').read()
    convert(pic_bytes, 'bg')
    convert(open(r"C:\Users\<USER>\Downloads\fullbg.jpg", 'rb').read(), 'fullbg')
