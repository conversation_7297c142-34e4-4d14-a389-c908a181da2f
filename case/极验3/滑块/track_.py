import random
import numpy as np
import matplotlib.pyplot as plt
from math import pi, cos
import json
import math


def easeInExpo(x):
    return 0 if x == 0 else pow(2, 10 * x - 10)


def easeOutExpo(x):
    return 1 if x == 1 else 1 - pow(2, -10 * x)


def easeInOutSine(x):
    return -(cos(pi * x) - 1) / 2


def easeInOutQuad(x):
    return 2 * x * x if x < 0.5 else 1 - pow(-2 * x + 2, 3) / 2
    # return 2 * x * x if x < 0.5 else 1 - pow(-2 * x + 2, 2) / 2


def easeOutQuart(x):
    return 1 - pow(1 - x, 4)


def easeOutQuart_inverse(y):
    return 1 - pow(1 - y, 1 / 4)


def easeInQuad(x):
    return x * x


def easeInQuart(x):
    return x * x * x * x


def easeOutQuad_inverse(x):
    return


def easeInOutQuad_inverse(y):
    return pow(y / 2, 0.5) if y < 0.5 else 1 - pow((1 - y) / 4, 1 / 3)
    # return pow(y, 2)


def show_result(data):
    x_trail = []
    y_trail = []
    t_trail = []
    for trail in data:
        x = trail[0]
        y = trail[1]
        t = trail[2]
        x_trail.append(x)
        y_trail.append(y)
        t_trail.append(t)
    # print('diff: ', np.diff(x_trail))
    # print('len: ', len(x_trail))
    plt.plot(t_trail, x_trail)

    plt.show()


def adjust_scope(distance):
    # length = distance + random.randint(30, 40)
    t = np.linspace(0, 1, distance)
    # t = [i for i in range(167)]
    # t = np.linspace(-0.5, 1, length)
    # s = [easeOutExpo(x) for x in t]
    # s2 = [easeOutQuart_inverse(x / 167) for x in t]
    s2 = [easeOutQuart(x) for x in t]
    t = [easeOutQuart_inverse(x) for x in s2]
    # s = [easeInOutQuad(x) for x in t]
    # s = [easeInOutSine(x) for x in t]

    # plt.plot(t, s)
    plt.plot(t, s2)
    plt.show()


def generate_s(distance):
    two = random.randint(0, 10)
    # distance -= two * 2
    distance -= two

    numbers = [1, 2, 3, 4]
    p = [0.1, 0.15, 0.15, 0.6]
    data = []
    index = np.random.choice(numbers, p=p)
    t = 0
    for i in range(distance):
        t += 1
        if t == index:
            data.append(0)
            t = 0
            index = np.random.choice(numbers, p=p)
        data.append(1)

    # exits = []
    # for i in range(two):
    #     idx = random.randint(10, len(data) - 30)
    #     if idx in exits:
    #         continue
    #     exits.append(idx)
    #     data.insert(idx, 2)

    # * 随机递增为2
    for i in range(two):
        idx = random.randint(10, len(data) - 30)
        data[idx] += 1

    # * 随机最后distance相同
    if random.randint(0, 1):
        data.append(0)

    # print(data)
    data_ = np.cumsum(np.array(data))

    return data_


def generate_track(distance, passtime):
    s = generate_s(distance)

    t = []
    # t = [round(easeInExpo(x / distance) * passtime) for x in s]
    # t = [round(easeInOutQuad_inverse(x / distance) * passtime) for x in s]
    # t = [round(easeInOutQuad(x / distance) * passtime) for x in s]
    # t = [round(easeInExpo(x / distance) * passtime) for x in s]
    # for i in range(0, 10):
    #     t.append(round(easeInOutQuad_inverse(s[i] / distance) * passtime))
    for i in range(0, len(s)):
        t.append(round(easeOutQuart_inverse(s[i] / distance) * passtime) + int(passtime * 0.15))

    t = list(np.diff(t))
    for i in range(0, len(t), 15):
        t[i] += random.randint(6, 12)
    t.insert(0, random.randint(20, 30))
    t = list(np.cumsum(np.array(t)))

    # * 防止最后时间相同
    if t[-1] == t[-2]:
        t[-1] += random.randint(100, 200)

    # * 生成y数据
    data_y = []
    ini = 0
    tt = 0
    index = random.randint(10, 25)
    for i in range(len(s)):
        tt += 1
        if tt == index:
            ini += 1
            tt = 0
            index = random.randint(10, 25)
        data_y.append(-ini)

    # t[2] -= random.randint(30, 50)

    # print(s)
    # print(np.diff(s))
    # print(len(s))
    # print(data_y)
    # print(t)
    # print(np.diff(t))

    # plt.plot(t, s)
    # plt.show()

    tracks = [[-random.randint(20, 40), -random.randint(20, 40), 0], [0, 0, 0]]
    for i in range(len(s)):
        tracks.append([int(s[i]), int(data_y[i]), int(t[i])])

    # print(json.dumps(tracks))
    show_result(tracks)

    return tracks


def get_slide_track(distance):
    def __ease_out_expo(sep):
        if sep == 1:
            return 1
        else:
            return 1 - pow(2, -10 * sep)

    if not isinstance(distance, int) or distance < 0:
        raise ValueError(f"distance类型必须是大于等于0的整数: distance: {distance}, type: {type(distance)}")

    slide_track = [
        [random.randint(-50, -10), random.randint(-50, -10), 0],
        [0, 0, 0],
    ]
    # 共记录count次滑块位置信息
    # count = random.randint(30, 40) + int(distance / 2)
    count = 30 + int(distance / 2)
    # 初始化滑动时间
    t = random.randint(50, 100)
    # 记录上一次滑动的距离
    _x = 0
    _y = 0
    for i in range(count):
        # 已滑动的横向距离
        x = round(__ease_out_expo(i / count) * distance)
        # 滑动过程消耗的时间
        t += random.randint(10, 20)
        if x == _x:
            continue
        y = random.randint(-5, 0)

        slide_track.append([x, y, t])
        if x == distance:
            break
        _x = x

    slide_track.append(slide_track[-1])
    return slide_track, slide_track[-1][2]


def get_slide_track2():
    pass


if __name__ == '__main__':
    # adjust_scope(167)
    # print(generate_s(167))
    passtime = random.randint(1000, 2000)
    generate_track(167, passtime)

    a, _ = get_slide_track(167)
    print(a)
    show_result(a)

    # b = bezierTrajectory()
    # c = b.trackArray([0, 0], [1500, 167], 167 + random.randint(30, 40))
    # show_result(c)
    # print(c)
