import json
import time
import requests
import base64
import numpy as np
import cv2
from loguru import logger
import ddddocr


def verify(bg_bytes, full_bytes):
    slide = ddddocr.DdddOcr(det=False, ocr=False, show_ad=False)
    result = slide.slide_comparison(bg_bytes, full_bytes)['target']
    result[0] -= 3

    # image = cv2.imread('./slice.png')
    # cv2.line(image, (result[0], 0), (result[0], 160), (0, 255, 0), thickness=1)
    # cv2.imwrite('result.png', image)

    return result[0]


def verify2(bg_bytes, slice_bytes):
    det = ddddocr.DdddOcr(det=False, ocr=False, show_ad=False)
    result = det.slide_match(slice_bytes, bg_bytes, simple_target=True)['target']
    result[0] += 3
    result[1] += 3
    result[2] -= 3
    result[3] -= 3

    return result[0]


if __name__ == '__main__':
    pass

    a = open('./bg.png', 'rb').read()
    b = open('./fullbg.png', 'rb').read()
    # c = open('./slice.png', 'rb').read()
    print(verify(a, b))
    # position2 = verify2(a, c)

    # image = cv2.imread('./bg.png')
    # cv2.line(image, (position1[0], 0), (position1[0], 160), (0, 255, 0), thickness=2)
    # cv2.rectangle(image, (position2[0], position2[1]), (position2[2], position2[3]), (0, 0, 255), thickness=2)
    # cv2.imwrite('result.png', image)
    #
    # logger.success(position1)
    # logger.success(position2)
