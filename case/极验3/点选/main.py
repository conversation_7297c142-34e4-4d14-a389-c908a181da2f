import time
from loguru import logger
import urllib3
import requests
import json
import re
from verify import YdmVerify
import cv2
import numpy as np
import execjs

urllib3.disable_warnings()

headers = {
    "authority": "www.geetest.com",
    "accept": "application/json, text/javascript, */*; q=0.01",
    "accept-language": "zh-CN,zh;q=0.9",
    "cache-control": "no-cache",
    "pragma": "no-cache",
    "referer": "https://www.geetest.com/demo/click-float.html",
    "sec-ch-ua": '"Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "Windows",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "x-requested-with": "XMLHttpRequest"
}

headers2 = {
    "Host": "api.geetest.com",
    "Pragma": "no-cache",
    "Cache-Control": "no-cache",
    "sec-ch-ua": "Google Chrome;v=119, Chromium;v=119, Not?A_Brand;v=24",
    "sec-ch-ua-mobile": "?0",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                  "Chrome/********* Safari/537.36",
    "sec-ch-ua-platform": "Windows",
    "Accept": "*/*",
    "Sec-Fetch-Site": "cross-site",
    "Sec-Fetch-Mode": "no-cors",
    "Sec-Fetch-Dest": "script",
    "Referer": "http://qyxy.scjgj.beijing.gov.cn/",
    "origin": 'http://qyxy.scjgj.beijing.gov.cn',
    "Accept-Language": "zh-CN,zh;q=0.9"
}


def register():
    """
    获取 challenge、gt
    """
    url = "https://www.geetest.com/demo/gt/register-click"
    params = {
        "t": str(int(time.time() * 1000))
    }
    response = requests.get(url, headers=headers, params=params, verify=False)

    logger.success(response.status_code)
    logger.info(response.json())

    return response.json()


def gettype(register_json):
    # headers.update({
    #     "authority": "apiv6.geetest.com",
    #     "accept": "*/*",
    #     "referer": "https://www.geetest.com/",
    #     "sec-fetch-dest": "script",
    #     "sec-fetch-mode": "no-cors",
    #     "sec-fetch-site": "same-site"
    # })

    # url = "https://apiv6.geetest.com/gettype.php"
    url = "https://api.geetest.com/gettype.php"
    params = {
        "gt": register_json['gt'],
        "callback": f"geetest_{int(time.time() * 1000)}"
    }
    response = requests.get(url, headers=headers, params=params)
    res = json.loads(re.search(r'geetest_\d{13}\((.*)\)', response.text).group(1))

    logger.success(response.status_code)
    logger.info(res)

    return res


def get1(register_json):
    # url = "https://apiv6.geetest.com/get.php"
    url = 'https://api.geetest.com/get.php'
    params = {
        "gt": register_json['gt'],
        "challenge": register_json['challenge'],
        "lang": "zh-cn",
        "pt": "0",
        "client_type": "web",
        "w": "",
        "callback": f"geetest_{int(time.time() * 1000)}"
    }

    response = requests.get(url, headers=headers, params=params, verify=False)
    res = json.loads(re.search(r'geetest_\d{13}\((.*)\)', response.text).group(1))

    logger.success(response.status_code)
    logger.info(res)

    return res


def ajax1(register_json):
    url = "https://api.geevisit.com/ajax.php"
    headers.update({
        "Sec-Fetch-Site": "cross-site"
    })
    params = {
        "gt": register_json['gt'],
        "challenge": register_json['challenge'],
        "lang": "zh-cn",
        "pt": "0",
        "client_type": "web",
        "w": "",
        "callback": f"geetest_{int(time.time() * 1000)}"
    }
    response = requests.get(url, headers=headers, params=params, verify=False)
    res = json.loads(re.search(r'geetest_\d{13}\((.*)\)', response.text).group(1))

    logger.success(response.status_code)
    logger.info(res)

    return res


# def get_click_js_code(gettype_json):
#     js_url = 'https://static.geetest.com' + gettype_json['data']['click']
#     headers2 = {
#         "authority": "static.geetest.com",
#         "origin": "https://www.geetest.com",
#         "referer": "https://www.geetest.com/",
#         "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
#     }
#
#     js_code1 = requests.get(js_url, headers=headers2, verify=False).text
#     logger.info(js_code1)
#
#     return js_code1


def get2(register_json, ajax1_json):
    url = "https://api.geevisit.com/get.php"
    params = {
        "is_next": "true",
        "type": ajax1_json['data']['result'],
        "gt": register_json['gt'],
        "challenge": register_json['challenge'],
        "lang": "zh-cn",
        "https": "true",
        "protocol": "https://",
        "offline": "false",
        "product": "float",
        "api_server": "api.geevisit.com",
        "isPC": "true",
        "autoReset": "true",
        "width": "100%",
        "callback": f"geetest_{int(time.time() * 1000)}"
    }
    response = requests.get(url, headers=headers, params=params, verify=False)
    res = json.loads(re.search(r'geetest_\d{13}\((.*)\)', response.text).group(1))

    logger.success(response.status_code)
    logger.info(res)

    return res


def get_pic(url, register_json):
    params = {
        "challenge": register_json['challenge']
    }

    content = requests.get(url, headers={
        "Host": "static.geetest.com",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                      "Chrome/********* Safari/537.36",
        "referer": "http://qyxy.scjgj.beijing.gov.cn/"
    }, params=params).content
    # logger.info(content)

    return content


def verify(image: bytes):
    y = YdmVerify()
    verify_result = y.click_verify(image)

    img = cv2.imdecode(np.frombuffer(image, np.uint8), cv2.IMREAD_COLOR)
    for index, i in enumerate(verify_result):
        cv2.circle(img, (i[0], i[1]), 25, (0, 255, 0), thickness=2)
        cv2.putText(img, str(index + 1), (i[0] - 5, i[1] + 5), fontFace=cv2.FONT_HERSHEY_SCRIPT_SIMPLEX,
                    fontScale=1, color=(0, 0, 0), thickness=2)
    cv2.imwrite('test.png', img)

    position = []
    for i in verify_result:
        x = round((i[0] / 344) * 10000)
        y = round((i[1] / 344) * 10000)
        position.append(f"{x}_{y}")

    return ','.join(position), verify_result


def get_gct_js_code(url):
    js_code = requests.get(url, headers={
        "Host": "static.geetest.com",
        "origin": "http://qyxy.scjgj.beijing.gov.cn",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                      "Chrome/********* Safari/537.36",
        "referer": "http://qyxy.scjgj.beijing.gov.cn/"
    }, verify=False).text
    gct = re.findall(
        r'\[0];(function [a-zA-Z]{4}\([a-zA-Z]{1}\){var .*;break;}}})(function [a-zA-Z]{4}\([a-zA-Z]{1}\){var .*\(\);break;}}})return function\(',
        js_code)[0]

    return gct[0], gct[1]


def get_w(position_str, get2_json, register_json, position, code1, code2):
    c_ = get2_json['data']['c']
    s = get2_json['data']['s']
    pic_url = get2_json['data']['pic']

    gt = register_json['gt']
    challenge = register_json['challenge']

    js_code = open(r'w.js', 'r', encoding='utf-8').read()
    js_compile = execjs.compile(js_code)
    w_params = js_compile.call('get_w', position_str, gt, challenge, pic_url, c_, s, position, code1, code2)

    logger.info(w_params['params'])

    return w_params['w']


def ajax2(get2_json, register_json):
    pic_type = get2_json['data']['pic_type']

    pic = 'https://static.geetest.com' + get2_json['data']['pic']
    pic_content = get_pic(pic, register_json)

    # gct_path = 'https://static.geetest.com' + get2_json['data']['gct_path']
    # gct_code = requests.get(gct_path, headers=headers, verify=False)
    # logger.info(gct_code.text)

    gct_url = 'https://static.geetest.com' + get2_json['data']['gct_path']
    code1, code2 = get_gct_js_code(gct_url)

    if pic_type == 'word':
        position_str, position = verify(pic_content)
        w = get_w(position_str, get2_json, register_json, position, code1, code2)

        time.sleep(1)
        url = 'https://api.geevisit.com/ajax.php'
        params = {
            "gt": register_json['gt'],
            "challenge": register_json['challenge'],
            "lang": "zh-cn",
            "pt": "0",
            "client_type": "web",
            "w": w,
            "callback": f"geetest_{int(time.time() * 1000)}"
        }
        response = requests.get(url, headers=headers, verify=False, params=params)

        logger.success(response.status_code)
        logger.info(json.loads(re.search(r'geetest_\d{13}\((.*)\)', response.text).group(1)))

        return json.loads(re.search(r'geetest_\d{13}\((.*)\)', response.text).group(1))
    else:
        logger.warning('其他验证码类型')


def main():
    a = register()
    b = gettype(a)

    get1(a)
    c = ajax1(a)

    # get_click_js_code(b)

    d = get2(a, c)
    return ajax2(d, a), a['challenge']


def main_bj(a):
    # a = register()
    b = gettype(a)

    get1(a)
    c = ajax1(a)

    # get_click_js_code(b)

    d = get2(a, c)
    return ajax2(d, a)['data']['validate'], a['challenge']


if __name__ == '__main__':
    main()
