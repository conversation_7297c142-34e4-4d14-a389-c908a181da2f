import json
import time
import requests
import base64
import numpy as np
import cv2
from loguru import logger


class YdmVerify(object):
    _custom_url = "http://api.jfbym.com/api/YmServer/customApi"
    _token = "0dlGZ5ljUggnUQObBPw4TcrkDaG6dHOzIWkOSm3BJ88"
    _headers = {
        'Content-Type': 'application/json'
    }

    def click_verify(self, image, label_image=None, extra='click', verify_type="30103"):
        payload = {
            "image": base64.b64encode(image).decode(),
            # "label_image": base64.b64encode(label_image).decode(),
            "token": self._token,
            "type": verify_type
        }
        if extra:
            payload['extra'] = extra
        resp = requests.post(self._custom_url, headers=self._headers, data=json.dumps(payload))
        # print(resp.text)
        b = resp.json()['data']['data'].split('|')
        c = []
        for i in b:
            c.append(list(map(lambda x: int(x), i.split(','))))

        return c


if __name__ == '__main__':
    y = YdmVerify()
    image = open(r"C:\Users\<USER>\Downloads\77531c9602bc4b7da50594a301268e3d.jpg", 'rb').read()
    verify_result = y.click_verify(image)

    img = cv2.imdecode(np.frombuffer(image, np.uint8), cv2.IMREAD_COLOR)
    for index, i in enumerate(verify_result):
        cv2.circle(img, (i[0], i[1]), 25, (0, 255, 0), thickness=2)
        cv2.putText(img, str(index + 1), (i[0] - 5, i[1] + 5), fontFace=cv2.FONT_HERSHEY_SCRIPT_SIMPLEX,
                    fontScale=1, color=(0, 0, 0), thickness=2)
    cv2.imwrite('test.png', img)

    a = []
    for i in verify_result:
        x = round((i[0] / 344) * 10000)
        y = round((i[1] / 344) * 10000)
        a.append(f"{x}_{y}")

    logger.info(",".join(a))
    logger.info(verify_result)
