from ultralytics import YOLO
from loguru import logger
import time
import numpy as np
from io import BytesIO
from PIL import Image
from siamese import SiamOnnx
from fastapi import FastAPI, UploadFile
import uvicorn
from typing import List
import cv2

# from threading import Lock

app = FastAPI()


@app.get('/')
def hello_word():
    return "极验3点选识别"


class ClickPredict:

    def __init__(self, y8_model_path, siam_model_path, imgsz=320):
        self.imgsz = imgsz
        # 加载yolov8模型
        self.yo8_model = YOLO(y8_model_path, task='detect')
        # 加载孪生模型
        self.siam_model = SiamOnnx(siam_model_path, providers=['CPUExecutionProvider'])
        # self.lock = Lock()

    @staticmethod
    def y8_detect_xy(result):
        """
        输出坐标信息
        :param result:
        :return:
        """
        cls_xy = list()
        cls_dict = result.names
        cls_all = result.boxes.cls.tolist()
        # logger.info(f"==yolov8识别结果目标类 {len(cls_all)} 个：{cls_all}")
        xyxy_all = result.boxes.xyxy.tolist()
        for i in range(len(cls_all)):
            label_name = cls_dict[int(cls_all[i])]
            box_xyxy = xyxy_all[i]
            box_mid_xy = [(box_xyxy[0] + box_xyxy[2]) / 2, (box_xyxy[1] + box_xyxy[3]) / 2]
            # print(f"目标点{i} ：标签名字：{label_name}, 中心坐标：{box_mid_xy}， xyxy坐标： {box_xyxy}")
            cls_xy.append({
                "label_id": i, "label_name": label_name,
                "box_mid_xy": box_mid_xy, "xyxy": box_xyxy
            })
        # logger.info(f"==yolov8识别结果is {cls_xy}==")
        return cls_xy

    def y8_model_predict(self, img_path, save=True):
        # s_time = time.time()
        # with self.lock:
        results = self.yo8_model.predict(source=img_path, show=False, save=False, imgsz=self.imgsz, device='cpu')
        # logger.info(
        #     f"==yolov8model_predict_img_path:{img_path}, 耗时时间 {time.time() - s_time}, total_标签名字：{results[0].names}")
        return results

    @staticmethod
    def open_image(file):
        if isinstance(file, np.ndarray):
            img = Image.fromarray(file)
        elif isinstance(file, bytes):
            img = Image.open(BytesIO(file))
        elif isinstance(file, Image.Image):
            img = file
        else:
            img = Image.open(file)
        img = img.convert('RGB')
        return img

    def judge_max_sim(self, cls_xys, img):
        targets = [(i["xyxy"], i["box_mid_xy"]) for i in cls_xys if i["label_name"] == "target"]
        chars = [i["xyxy"] for i in cls_xys if i["label_name"] == "char"]
        chars.sort(key=lambda x: x[0])  # 根据坐标进行排序，并裁剪小字, 因为小字本身就是按坐标顺序的
        chars = [img.crop(char) for char in chars]

        if len(chars) > len(targets):
            logger.warning('chars > targets 使用固定大小裁剪')
            chars = []
            for i in range(len(targets)):
                chars.append(img.crop([29 * i, 348, 29 * (i + 1), 380]))

        # 获取点击顺序
        click_seq_mid_result = []
        click_seq_xyxy_result = []
        for m, img_char in enumerate(chars[:len(targets)]):
            slys = []
            for n, target in enumerate(targets):
                img_target = img.crop(target[0])
                similarity = self.siam_model.predict_siamese(img_char, img_target)
                slys.append(similarity)

            slys_index = slys.index(max(slys))
            click_seq_mid_result.append(targets[slys_index][1])
            click_seq_xyxy_result.append(targets[slys_index][0])
            targets.pop(slys_index)

            # if len(targets) == 0:
            #     break
            # elif len(targets) == 1:
            #     slys_index = 0
            # else:
            #     for n, target in enumerate(targets):
            #         img_target = img.crop(target[0])
            #         similarity = self.siam_model.predict_siamese(img_char, img_target)
            #         slys.append(similarity)
            #     slys_index = slys.index(max(slys))
            # click_seq_result.append(targets[slys_index][1])
            # targets.pop(slys_index)
            # if len(targets) == 0:
            #     break

        return click_seq_mid_result, click_seq_xyxy_result

    def get_xy_seq(self, img_path, show=False):
        """
        获取点击顺序
        :return:
        """
        img = self.open_image(img_path)

        a = time.time()
        result = self.y8_model_predict(img)[0]
        # yolov8识别结果
        cls_xys = self.y8_detect_xy(result)
        # 孪生判断相似度结果
        mid_seq, xyxy_seq = self.judge_max_sim(cls_xys, img)
        logger.success(f">>最终点击顺序坐标是 {mid_seq} time: {time.time() - a}")

        if show:
            if type(img_path).__name__ == 'str':
                image = cv2.imread(img_path)
            else:
                image = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
            for index, coordinate in enumerate(xyxy_seq):
                coordinate = list(map(round, coordinate))
                mid_seq[index] = list(map(round, mid_seq[index]))
                cv2.rectangle(image, (coordinate[0], coordinate[1]), (coordinate[2], coordinate[3]), (0, 255, 0),
                              thickness=1)
                cv2.putText(image, str(index + 1), (mid_seq[index][0], mid_seq[index][1]),
                            fontFace=cv2.FONT_HERSHEY_SCRIPT_SIMPLEX, fontScale=1.5, color=(240, 248, 255), thickness=2)
            cv2.imshow(f'image', image)

            cv2.waitKey()
            cv2.destroyAllWindows()

        return mid_seq, xyxy_seq


click_obj = ClickPredict(r"best.onnx", r"jy3_char.onnx")


@app.post('/dianxuan')
def ocr(files: List[UploadFile]):
    pic: bytes = files[0].file.read()
    mid_seq, xyxy_seq = click_obj.get_xy_seq(pic)

    return [mid_seq, xyxy_seq]


if __name__ == '__main__':
    uvicorn.run("__main__:app", host="0.0.0.0", port=1314)
