import time
from PIL import Image
import onnxruntime
import numpy as np
from io import BytesIO
import os
from threading import Lock


class SiamOnnx(object):
    def __init__(self, path, providers=None):
        if not providers:
            providers = ['CPUExecutionProvider']
        self.sess = onnxruntime.InferenceSession(path, providers=providers)
        self.loadSize = 512
        self.input_shape = [105, 105]
        # self.lock = Lock()

    @staticmethod
    def sigmoid(x):
        return 1 / (1 + np.exp(-x))

    @staticmethod
    def convert(file):
        # 图片转换为矩阵
        if isinstance(file, np.ndarray):
            img = Image.fromarray(file)
        elif isinstance(file, bytes):
            img = Image.open(BytesIO(file))
        elif isinstance(file, Image.Image):
            img = file
        else:
            img = Image.open(file)
        return img

    def open_image(self, file, input_shape, nc=3):
        out = self.convert(file)
        # 改变大小 并保证其不失真
        out = out.convert('RGB')
        h, w = input_shape
        out = out.resize((w, h), 1)
        if nc == 1:
            out = out.convert('L')
        return out

    def set_img(self, lines):
        image = self.open_image(lines, self.input_shape, 3)
        image = np.array(image).astype(np.float32) / 255.0
        photo = np.expand_dims(np.transpose(image, (2, 0, 1)), 0)
        return photo

    def predict_siamese(self, image1, image2):
        photo_1 = self.set_img(image1)
        photo_2 = self.set_img(image2)
        # with self.lock:
        out = self.sess.run(None, {"x1": photo_1, "x2": photo_2})
        out = out[0]
        out = self.sigmoid(out)
        out = out[0][0]
        return out


if __name__ == '__main__':
    pre_onnx_path = r"jy3_char.onnx"
    pre = SiamOnnx(os.path.join(os.getcwd(), pre_onnx_path))
    image_1 = r"E:\demo_program\python\pycharm\capture\ultralytics\My\jy3_char_pic_test\1\char_1.png"
    image_2 = r"E:\demo_program\python\pycharm\capture\ultralytics\My\jy3_char_pic_test\1\target_3.png"
    s_time = time.time()
    large_img = pre.predict_siamese(image_1, image_2)
    print(time.time() - s_time, "相似度", large_img)
