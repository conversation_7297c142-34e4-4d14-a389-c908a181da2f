import requests
from loguru import logger


class YdmVerify(object):
    def click_verify(self, image):
        try:
            res = requests.post('http://10.99.205.13:1314/dianxuan', files=[('files', image)], timeout=5)
            mid_seq, xyxy_seq = res.json()[0], res.json()[1]
            for idx, i in enumerate(mid_seq):
                mid_seq[idx] = list(map(round, i))
            # mid_seq = list(map(round, mid_seq))
            return mid_seq
        except requests.exceptions.Timeout:
            logger.warning("-------------------------------------------------------------------------------\n"
                           ""
                           "验证码识别超时5s\n"
                           ""
                           "-------------------------------------------------------------------------------")
            raise Exception("验证码识别超时5s")


if __name__ == '__main__':
    a = YdmVerify()
    image = open(r"C:\Users\<USER>\Downloads\1.jpg", 'rb').read()
    print(a.click_verify(image))

