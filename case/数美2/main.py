# -*- coding: utf-8 -*-
import re

import requests
import execjs
import random
import time

headers = {
    "Accept": "*/*",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "Cache-Control": "no-cache",
    "Connection": "keep-alive",
    "Origin": "https://castatic.fengkongcloud.cn",
    "Pragma": "no-cache",
    "Referer": "https://castatic.fengkongcloud.cn/",
    "Sec-Fetch-Dest": "script",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-site",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "sec-ch-ua": "\"Microsoft Edge\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\""
}


def get_captcha_uuid():
    characters = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
    timestamp = time.strftime('%Y%m%d%H%M%S', time.localtime())  # 获取当前年月日时分秒
    # milliseconds = str(random.randint(0, 99)).zfill(2)  # 保证毫秒部分是两位数
    random_chars = ''.join(random.choice(characters) for _ in range(18))  # 生成8位随机字符
    return timestamp + random_chars


for i in range(10):
    # 示例用法
    captcha_uuid = get_captcha_uuid()
    url = "https://captcha1.fengkongcloud.cn/ca/v1/register"
    init_params = {
        "data": "{}",
        "channel": "DEFAULT",
        "lang": "zh-cn",
        "callback": f"sm_{int(time.time() * 1000)}",
        "appId": "default",
        "captchaUuid": f"{captcha_uuid}",
        "sdkver": "1.1.3",
        "model": "auto_slide",
        "organization": "RlokQwRlVjUrTUlkIqOg",
        "rversion": "1.0.4"
    }
    response = requests.get(url, headers=headers, params=init_params)

    # with open('huakuai.js', 'r', encoding='utf-8') as f:
    #     code = f.read()
    # comp = execjs.compile(code)
    # params = comp.call('get_parmas')

    params = {"je": 'qgW9zJneBQw=', "mu": 'XUJPJgJ25McMkm4bp8y/8w==', "ww": 'WSRPcsKyRz0=', "nu": 'C0kH/bWLjw8=', "dy": 'Rfpr5oqb5y4=', 'act.os': 'web_pc',
              "tb": '3jSn4gNaAVM=', "en": 'y+ugz9NIWys=', "kq": 'mtlOTdT5LOE=', "organization": 'RlokQwRlVjUrTUlkIqOg', "mp": 'WYfkIZp7GoA=',
              "oc": 'h9oFKi8cHpg=', "xy": 'YabT6nmJOC0=', "jo": 'l3aEINYnwpY=', "captcha_uuid": captcha_uuid,
              "rid": re.findall('"rid":"(.*?)"', response.text)[0], "act.os": "web_pc", "sdkver": '1.1.3', "rversion": '1.0.4',
              "callback": f"sm_{int(time.time() * 1000)}", "protocol": "180", "ostype": "web"}

    url = "https://captcha1.fengkongcloud.cn/ca/v2/fverify"
    response = requests.get(url, headers=headers, params=params)

    print(response.content.decode('latin1'))
    print(response)
    time.sleep(1)
