import requests
import urllib3
import re
from loguru import logger

urllib3.disable_warnings()
timeout = 3
headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
}
host = 'https://amr.sz.gov.cn'

url = f"{host}/outer/entSelect/gs.html"
session = requests.session()
response = session.get(url, headers=headers, verify=False, timeout=timeout)
with open('rs6.html', 'w', encoding='utf-8') as f:
    f.write(response.text)

js_url = re.findall(r'src="(/.*?\.js)"', response.text)[0]
logger.info(f"js_url: {js_url}")

js_code = session.get(f'{host}{js_url}', headers=headers, verify=False, timeout=timeout).text
with open('web-js3.js', 'w', encoding='utf-8') as f:
    f.write(js_code)

auto_js = re.findall(r"t\" r='m'>(.*?)</script><script type=\"t", response.text)[0]
logger.info(f"auto_js: {auto_js}")
