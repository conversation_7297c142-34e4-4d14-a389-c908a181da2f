import requests
import time
from loguru import logger
import execjs
import re
import json
import ddddocr
import cv2
import numpy as np
from binascii import b2a_hex
import rsa
import base64
import uuid

from 九宫格 import YdmVerify
from 语序点选 import b64_api


def get_uuid():
    js_code = """
    var uuid = function () {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            var r = Math.random() * 16 | 0;
            var v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    };
    """
    compile_js = execjs.compile(js_code)
    return compile_js.call("uuid")


headers = {
    "Accept": "*/*",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Cache-Control": "no-cache",
    "Connection": "keep-alive",
    "Pragma": "no-cache",
    "Referer": "http://cx.cnca.cn/",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36"
}
captcha_id = "08c16c99330a5a1d6b7f4371bbd5a978"


def get_load():
    url = "http://gcaptcha4.geetest.com/load"
    params = {
        "captcha_id": captcha_id,
        "challenge": get_uuid(),
        "client_type": "web",
        "lang": "zh-cn",
        "callback": f"geetest_{int(time.time() * 1000)}"
    }
    response = requests.get(url, headers=headers, params=params, verify=False)

    logger.success(response.status_code)
    logger.info(response.text)

    return json.loads(re.search(r'geetest_\d{13}\((.*)\)', response.text).group(1))


def get_w(load_params):
    img = requests.get("http://static.geetest.com/" + load_params['data']['imgs'], headers=headers).content
    image = cv2.imdecode(np.frombuffer(img, np.uint8), cv2.IMREAD_COLOR)
    identify_list = []
    captcha_type = load_params['data']['captcha_type']
    if captcha_type == 'nine':
        img2 = requests.get("http://static.geetest.com/" + load_params['data']['ques'][0], headers=headers).content
        y = YdmVerify()
        identify_result = y.click_verify(img, img2)  # "[[1, 2], [2, 2], [3, 1]]"
        identify_list = eval(identify_result)
        open('nine.png', 'wb').write(img2)
    elif captcha_type == 'phrase':
        identify_result = b64_api('jiejie', 'ss520999', img)
        for i in identify_result['data'].values():
            x = i['X坐标值']
            y = i['Y坐标值']
            cv2.circle(image, (x, y), 20, (0, 255, 0), 2)
            x = int(round(x / 300, 4) * 10000)
            y = int(round(y / 200, 4) * 10000)
            identify_list.append([x, y])

    logger.info(identify_list)
    cv2.imwrite('verify.png', image)

    js_code = open(r'./w.js', 'r', encoding='utf-8').read()
    js_compile = execjs.compile(js_code)

    # 获取gct4.5··· js文件的两个函数字符串
    response_ = requests.get('http://static.geetest.com' + load_params['data']['gct_path'], headers=headers)
    code = \
        re.findall(r'];(function Fmhz\(t\){var .*;break;}}})(function .*\(.*\){var .*\)]\(\)\);break;}}})',
                   response_.text)[
            0]

    # todo 获取固定值
    # response_2 = requests.get('http://static.geetest.com' + load_params['data']['static_path'] + '/js/gcaptcha4.js',
    #                           headers=headers)

    js_result = js_compile.call('get_w', identify_list, load_params['data']['lot_number'], captcha_id,
                                load_params['data']['pow_detail'], code[0], code[1])
    logger.info(js_result['params'])
    logger.info(js_result['key'])

    return js_result['w']


def verify(load_params, w):
    url = "http://gcaptcha4.geetest.com/verify"
    params = {
        "callback": f"geetest_{int(time.time() * 1000)}",
        "captcha_id": captcha_id,
        "client_type": "web",
        "lot_number": load_params['data']['lot_number'],
        "payload": load_params['data']['payload'],
        "process_token": load_params['data']['process_token'],
        "payload_protocol": "1",
        "pt": "1",
        "w": w
    }
    response = requests.get(url, headers=headers, params=params, verify=False)

    logger.success(response.status_code)
    logger.success(response.text)

    return json.loads(re.search(r'geetest_\d{13}\((.*)\)', response.text).group(1))


if __name__ == '__main__':
    load = get_load()
    # logger.info(load)
    w = get_w(load)
    verify(load, w)
