import json
import time

import requests
import base64


class YdmVerify(object):
    _custom_url = "http://api.jfbym.com/api/YmServer/customApi"
    _token = "0dlGZ5ljUggnUQObBPw4TcrkDaG6dHOzIWkOSm3BJ88"
    _headers = {
        'Content-Type': 'application/json'
    }

    def click_verify(self, image, label_image=None, extra=None, verify_type="30008"):
        payload = {
            "image": base64.b64encode(image).decode(),
            "label_image": base64.b64encode(label_image).decode(),
            "token": self._token,
            "type": verify_type
        }
        if extra:
            payload['extra'] = extra
        resp = requests.post(self._custom_url, headers=self._headers, data=json.dumps(payload))
        print(resp.text)
        return resp.json()['data']['data']


if __name__ == '__main__':
    y = YdmVerify()
    # y.hcaptcha_verify()
    a = open(r"C:\Users\<USER>\Downloads\7acb48719d8140f8bf6767ae8f6c3b27.jpg", 'rb').read()
    b = open(r"C:\Users\<USER>\Downloads\03f00e8e9d0d0847bb10a3a22334274a.png", 'rb').read()
    y.click_verify(a, b)
