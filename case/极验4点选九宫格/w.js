const CryptoJS = require('crypto-js')
const _ = require('lodash');

var l = function () {
    function e() {
        return (65536 * (1 + Math["random"]()) | 0)["toString"](16)["substring"](1);
    }

    return function () {
        return e() + e() + e() + e();
    };
}();

function rsa(key) {
    window = global
    window['crypto'] = require('crypto-js')
    navigator = {
        "appName": "Netscape"
    }

    function rsa_webpack(t, n, s) {
        // "use strict";

        // n["$_EK"] = !0, n["default"] = void 0;

        // n["default"] = r;
        return function () {
            function n() {
                this["i"] = 0, this["j"] = 0, this["S"] = [];
            }

            n["prototype"]["init"] = function C(e) {
                var t, n, s;

                for (t = 0; t < 256; ++t) this["S"][t] = t;

                for (t = n = 0; t < 256; ++t) n = n + this["S"][t] + e[t % e["length"]] & 255, s = this["S"][t], this["S"][t] = this["S"][n], this["S"][n] = s;

                this["i"] = 0, this["j"] = 0;
            }, n["prototype"]["next"] = function E() {
                var e;
                return this["i"] = this["i"] + 1 & 255, this["j"] = this["j"] + this["S"][this["i"]] & 255, e = this["S"][this["i"]], this["S"][this["i"]] = this["S"][this["j"]], this["S"][this["j"]] = e, this["S"][e + this["S"][this["i"]] & 255];
            };
            var s,
                r,
                i,
                t,
                o = 256;

            if (null == r) {
                var a;

                if (r = [], i = 0, window["crypto"] && window["crypto"]["getRandomValues"]) {
                    var u = new Uint32Array(256);

                    for (window["crypto"]["getRandomValues"](u), a = 0; a < u["length"]; ++a) r[i++] = 255 & u[a];
                }

                var c = 0,
                    _ = function _(t) {
                        if (256 <= (c = c || 0) || o <= i) window["removeEventListener"] ? (c = 0, window["removeEventListener"]("mousemove", _, !1)) : window["detachEvent"] && (c = 0, window["detachEvent"]("onmousemove", _)); else try {
                            var n = t["x"] + t["y"];
                            r[i++] = 255 & n, c += 1;
                        } catch (e) {
                        }
                    };

                window["addEventListener"] ? window["addEventListener"]("mousemove", _, !1) : window["attachEvent"] && window["attachEvent"]("onmousemove", _);
            }

            function h() {
                if (null == s) {
                    s = function t() {
                        return new n();
                    }();

                    while (i < o) {
                        var e = Math["floor"](65536 * Math["random"]());
                        r[i++] = 255 & e;
                    }

                    for (s["init"](r), i = 0; i < r["length"]; ++i) r[i] = 0;

                    i = 0;
                }

                return s["next"]();
            }

            function l() {
            }

            l["prototype"]["nextBytes"] = function A(e) {
                var t;

                for (t = 0; t < e["length"]; ++t) e[t] = h();
            };

            function b(e, t, n) {
                null != e && ("number" == typeof e ? this["fromNumber"](e, t, n) : null == t && "string" != typeof e ? this["fromString"](e, 256) : this["fromString"](e, t));
            }

            function w() {
                return new b(null);
            }

            t = "Microsoft Internet Explorer" == navigator["appName"] ? (b["prototype"]["am"] = function B(e, t, n, s, r, i) {
                var o = 32767 & t,
                    a = t >> 15;

                while (0 <= --i) {
                    var u = 32767 & this[e],
                        c = this[e++] >> 15,
                        _ = a * u + c * o;

                    r = ((u = o * u + ((32767 & _) << 15) + n[s] + (1073741823 & r)) >>> 30) + (_ >>> 15) + a * c + (r >>> 30), n[s++] = 1073741823 & u;
                }

                return r;
            }, 30) : "Netscape" != navigator["appName"] ? (b["prototype"]["am"] = function S(e, t, n, s, r, i) {
                while (0 <= --i) {
                    var o = t * this[e++] + n[s] + r;
                    r = Math["floor"](o / 67108864), n[s++] = 67108863 & o;
                }

                return r;
            }, 26) : (b["prototype"]["am"] = function D(e, t, n, s, r, i) {
                var o = 16383 & t,
                    a = t >> 14;

                while (0 <= --i) {
                    var u = 16383 & this[e],
                        c = this[e++] >> 14,
                        _ = a * u + c * o;

                    r = ((u = o * u + ((16383 & _) << 14) + n[s] + r) >> 28) + (_ >> 14) + a * c, n[s++] = 268435455 & u;
                }

                return r;
            }, 28), b["prototype"]["DB"] = t, b["prototype"]["DM"] = (1 << t) - 1, b["prototype"]["DV"] = 1 << t;
            b["prototype"]["FV"] = Math["pow"](2, 52), b["prototype"]["F1"] = 52 - t, b["prototype"]["F2"] = 2 * t - 52;
            var p,
                f,
                d = "0123456789abcdefghijklmnopqrstuvwxyz",
                g = [];

            for (p = "0"["charCodeAt"](0), f = 0; f <= 9; ++f) g[p++] = f;

            for (p = "a"["charCodeAt"](0), f = 10; f < 36; ++f) g[p++] = f;

            for (p = "A"["charCodeAt"](0), f = 10; f < 36; ++f) g[p++] = f;

            function m(e) {
                return d["charAt"](e);
            }

            function v(e) {
                var t = w();
                return t["fromInt"](e), t;
            }

            function y(e) {
                var t,
                    n = 1;
                return 0 != (t = e >>> 16) && (e = t, n += 16), 0 != (t = e >> 8) && (e = t, n += 8), 0 != (t = e >> 4) && (e = t, n += 4), 0 != (t = e >> 2) && (e = t, n += 2), 0 != (t = e >> 1) && (e = t, n += 1), n;
            }

            function x(e) {
                this["m"] = e;
            }

            function k(e) {
                this["m"] = e, this["mp"] = e["invDigit"](), this["mpl"] = 32767 & this["mp"], this["mph"] = this["mp"] >> 15, this["um"] = (1 << e["DB"] - 15) - 1, this["mt2"] = 2 * e["t"];
            }

            function T() {
                this["n"] = null, this["e"] = 0, this["d"] = null, this["p"] = null, this["q"] = null, this["dmp1"] = null, this["dmq1"] = null, this["coeff"] = null;
                this["setPublic"]("00C1E3934D1614465B33053E7F48EE4EC87B14B95EF88947713D25EECBFF7E74C7977D02DC1D9451F79DD5D1C10C29ACB6A9B4D6FB7D0A0279B6719E1772565F09AF627715919221AEF91899CAE08C0D686D748B20A3603BE2318CA6BC2B59706592A9219D0BF05C9F65023A21D2330807252AE0066D59CEEFA5F2748EA80BAB81", "10001");
            }

            return x["prototype"]["convert"] = function z(e) {
                return e["s"] < 0 || 0 <= e["compareTo"](this["m"]) ? e["mod"](this["m"]) : e;
            }, x["prototype"]["revert"] = function F(e) {
                return e;
            }, x["prototype"]["reduce"] = function M(e) {
                e["divRemTo"](this["m"], null, e);
            }, x["prototype"]["mulTo"] = function O(e, t, n) {
                e["multiplyTo"](t, n), this["reduce"](n);
            }, x["prototype"]["sqrTo"] = function R(e, t) {
                e["squareTo"](t), this["reduce"](t);
            }, k["prototype"]["convert"] = function I(e) {
                var t = w();
                return e["abs"]()["dlShiftTo"](this["m"]["t"], t), t["divRemTo"](this["m"], null, t), e["s"] < 0 && 0 < t["compareTo"](b["ZERO"]) && this["m"]["subTo"](t, t), t;
            }, k["prototype"]["revert"] = function P(e) {
                var t = w();
                return e["copyTo"](t), this["reduce"](t), t;
            }, k["prototype"]["reduce"] = function j(e) {
                while (e["t"] <= this["mt2"]) e[e["t"]++] = 0;

                for (var t = 0; t < this["m"]["t"]; ++t) {
                    var n = 32767 & e[t],
                        s = n * this["mpl"] + ((n * this["mph"] + (e[t] >> 15) * this["mpl"] & this["um"]) << 15) & e["DM"];
                    e[n = t + this["m"]["t"]] += this["m"]["am"](0, s, e, t, 0, this["m"]["t"]);

                    while (e[n] >= e["DV"]) e[n] -= e["DV"], e[++n]++;
                }

                e["clamp"](), e["drShiftTo"](this["m"]["t"], e), 0 <= e["compareTo"](this["m"]) && e["subTo"](this["m"], e);
            }, k["prototype"]["mulTo"] = function N(e, t, n) {
                e["multiplyTo"](t, n), this["reduce"](n);
            }, k["prototype"]["sqrTo"] = function L(e, t) {
                e["squareTo"](t), this["reduce"](t);
            }, b["prototype"]["copyTo"] = function q(e) {
                for (var t = this["t"] - 1; 0 <= t; --t) e[t] = this[t];

                e["t"] = this["t"], e["s"] = this["s"];
            }, b["prototype"]["fromInt"] = function H(e) {
                this["t"] = 1, this["s"] = e < 0 ? -1 : 0, 0 < e ? this[0] = e : e < -1 ? this[0] = e + this["DV"] : this["t"] = 0;
            }, b["prototype"]["fromString"] = function U(e, t) {
                var n;
                if (16 == t) n = 4; else if (8 == t) n = 3; else if (256 == t) n = 8; else if (2 == t) n = 1; else if (32 == t) n = 5; else {
                    if (4 != t) return void this["fromRadix"](e, t);
                    n = 2;
                }
                this["t"] = 0, this["s"] = 0;
                var s,
                    r,
                    i = e["length"],
                    o = !1,
                    a = 0;

                while (0 <= --i) {
                    var u = 8 == n ? 255 & e[i] : (s = i, null == (r = g[e["charCodeAt"](s)]) ? -1 : r);
                    u < 0 ? "-" == e["charAt"](i) && (o = !0) : (o = !1, 0 == a ? this[this["t"]++] = u : a + n > this["DB"] ? (this[this["t"] - 1] |= (u & (1 << this["DB"] - a) - 1) << a, this[this["t"]++] = u >> this["DB"] - a) : this[this["t"] - 1] |= u << a, (a += n) >= this["DB"] && (a -= this["DB"]));
                }

                8 == n && 0 != (128 & e[0]) && (this["s"] = -1, 0 < a && (this[this["t"] - 1] |= (1 << this["DB"] - a) - 1 << a)), this["clamp"](), o && b["ZERO"]["subTo"](this, this);
            }, b["prototype"]["clamp"] = function V() {
                var e = this["s"] & this["DM"];

                while (0 < this["t"] && this[this["t"] - 1] == e) --this["t"];
            }, b["prototype"]["dlShiftTo"] = function $(e, t) {
                var n;

                for (n = this["t"] - 1; 0 <= n; --n) t[n + e] = this[n];

                for (n = e - 1; 0 <= n; --n) t[n] = 0;

                t["t"] = this["t"] + e, t["s"] = this["s"];
            }, b["prototype"]["drShiftTo"] = function X(e, t) {
                for (var n = e; n < this["t"]; ++n) t[n - e] = this[n];

                t["t"] = Math["max"](this["t"] - e, 0), t["s"] = this["s"];
            }, b["prototype"]["lShiftTo"] = function G(e, t) {
                var n,
                    s = e % this["DB"],
                    r = this["DB"] - s,
                    i = (1 << r) - 1,
                    o = Math["floor"](e / this["DB"]),
                    a = this["s"] << s & this["DM"];

                for (n = this["t"] - 1; 0 <= n; --n) t[n + o + 1] = this[n] >> r | a, a = (this[n] & i) << s;

                for (n = o - 1; 0 <= n; --n) t[n] = 0;

                t[o] = a, t["t"] = this["t"] + o + 1, t["s"] = this["s"], t["clamp"]();
            }, b["prototype"]["rShiftTo"] = function W(e, t) {
                t["s"] = this["s"];
                var n = Math["floor"](e / this["DB"]);
                if (n >= this["t"]) t["t"] = 0; else {
                    var s = e % this["DB"],
                        r = this["DB"] - s,
                        i = (1 << s) - 1;
                    t[0] = this[n] >> s;

                    for (var o = n + 1; o < this["t"]; ++o) t[o - n - 1] |= (this[o] & i) << r, t[o - n] = this[o] >> s;

                    0 < s && (t[this["t"] - n - 1] |= (this["s"] & i) << r), t["t"] = this["t"] - n, t["clamp"]();
                }
            }, b["prototype"]["subTo"] = function Z(e, t) {
                var n = 0,
                    s = 0,
                    r = Math["min"](e["t"], this["t"]);

                while (n < r) s += this[n] - e[n], t[n++] = s & this["DM"], s >>= this["DB"];

                if (e["t"] < this["t"]) {
                    s -= e["s"];

                    while (n < this["t"]) s += this[n], t[n++] = s & this["DM"], s >>= this["DB"];

                    s += this["s"];
                } else {
                    s += this["s"];

                    while (n < e["t"]) s -= e[n], t[n++] = s & this["DM"], s >>= this["DB"];

                    s -= e["s"];
                }

                t["s"] = s < 0 ? -1 : 0, s < -1 ? t[n++] = this["DV"] + s : 0 < s && (t[n++] = s), t["t"] = n, t["clamp"]();
            }, b["prototype"]["multiplyTo"] = function K(e, t) {
                var n = this["abs"](),
                    s = e["abs"](),
                    r = n["t"];
                t["t"] = r + s["t"];

                while (0 <= --r) t[r] = 0;

                for (r = 0; r < s["t"]; ++r) t[r + n["t"]] = n["am"](0, s[r], t, r, 0, n["t"]);

                t["s"] = 0, t["clamp"](), this["s"] != e["s"] && b["ZERO"]["subTo"](t, t);
            }, b["prototype"]["squareTo"] = function Y(e) {
                var t = this["abs"](),
                    n = e["t"] = 2 * t["t"];

                while (0 <= --n) e[n] = 0;

                for (n = 0; n < t["t"] - 1; ++n) {
                    var s = t["am"](n, t[n], e, 2 * n, 0, 1);
                    (e[n + t["t"]] += t["am"](n + 1, 2 * t[n], e, 2 * n + 1, s, t["t"] - n - 1)) >= t["DV"] && (e[n + t["t"]] -= t["DV"], e[n + t["t"] + 1] = 1);
                }

                0 < e["t"] && (e[e["t"] - 1] += t["am"](n, t[n], e, 2 * n, 0, 1)), e["s"] = 0, e["clamp"]();
            }, b["prototype"]["divRemTo"] = function Q(e, t, n) {
                var s = e["abs"]();

                if (!(s["t"] <= 0)) {
                    var r = this["abs"]();
                    if (r["t"] < s["t"]) return null != t && t["fromInt"](0), void (null != n && this["copyTo"](n));
                    null == n && (n = w());
                    var i = w(),
                        o = this["s"],
                        a = e["s"],
                        u = this["DB"] - y(s[s["t"] - 1]);
                    0 < u ? (s["lShiftTo"](u, i), r["lShiftTo"](u, n)) : (s["copyTo"](i), r["copyTo"](n));
                    var c = i["t"],
                        _ = i[c - 1];

                    if (0 != _) {
                        var h = _ * (1 << this["F1"]) + (1 < c ? i[c - 2] >> this["F2"] : 0),
                            l = this["FV"] / h,
                            p = (1 << this["F1"]) / h,
                            f = 1 << this["F2"],
                            d = n["t"],
                            g = d - c,
                            m = null == t ? w() : t;
                        i["dlShiftTo"](g, m), 0 <= n["compareTo"](m) && (n[n["t"]++] = 1, n["subTo"](m, n)), b["ONE"]["dlShiftTo"](c, m), m["subTo"](i, i);

                        while (i["t"] < c) i[i["t"]++] = 0;

                        while (0 <= --g) {
                            var v = n[--d] == _ ? this["DM"] : Math["floor"](n[d] * l + (n[d - 1] + f) * p);

                            if ((n[d] += i["am"](0, v, n, g, 0, c)) < v) {
                                i["dlShiftTo"](g, m), n["subTo"](m, n);

                                while (n[d] < --v) n["subTo"](m, n);
                            }
                        }

                        null != t && (n["drShiftTo"](c, t), o != a && b["ZERO"]["subTo"](t, t)), n["t"] = c, n["clamp"](), 0 < u && n["rShiftTo"](u, n), o < 0 && b["ZERO"]["subTo"](n, n);
                    }
                }
            }, b["prototype"]["invDigit"] = function J() {
                if (this["t"] < 1) return 0;
                var e = this[0];
                if (0 == (1 & e)) return 0;
                var t = 3 & e;
                return 0 < (t = (t = (t = (t = t * (2 - (15 & e) * t) & 15) * (2 - (255 & e) * t) & 255) * (2 - ((65535 & e) * t & 65535)) & 65535) * (2 - e * t % this["DV"]) % this["DV"]) ? this["DV"] - t : -t;
            }, b["prototype"]["isEven"] = function ee() {
                return 0 == (0 < this["t"] ? 1 & this[0] : this["s"]);
            }, b["prototype"]["exp"] = function te(e, t) {
                if (4294967295 < e || e < 1) return b["ONE"];
                var n = w(),
                    s = w(),
                    r = t["convert"](this),
                    i = y(e) - 1;
                r["copyTo"](n);

                while (0 <= --i) if (t["sqrTo"](n, s), 0 < (e & 1 << i)) t["mulTo"](s, r, n); else {
                    var o = n;
                    n = s, s = o;
                }

                return t["revert"](n);
            }, b["prototype"]["toString"] = function ne(e) {
                if (this["s"] < 0) return "-" + this["negate"]()["toString"](e);
                var t;
                if (16 == e) t = 4; else if (8 == e) t = 3; else if (2 == e) t = 1; else if (32 == e) t = 5; else {
                    if (4 != e) return this["toRadix"](e);
                    t = 2;
                }
                var n,
                    s = (1 << t) - 1,
                    r = !1,
                    i = "",
                    o = this["t"],
                    a = this["DB"] - o * this["DB"] % t;

                if (0 < o--) {
                    a < this["DB"] && 0 < (n = this[o] >> a) && (r = !0, i = m(n));

                    while (0 <= o) a < t ? (n = (this[o] & (1 << a) - 1) << t - a, n |= this[--o] >> (a += this["DB"] - t)) : (n = this[o] >> (a -= t) & s, a <= 0 && (a += this["DB"], --o)), 0 < n && (r = !0), r && (i += m(n));
                }

                return r ? i : "0";
            }, b["prototype"]["negate"] = function se() {
                var e = w();
                return b["ZERO"]["subTo"](this, e), e;
            }, b["prototype"]["abs"] = function re() {
                return this["s"] < 0 ? this["negate"]() : this;
            }, b["prototype"]["compareTo"] = function ie(e) {
                var t = this["s"] - e["s"];
                if (0 != t) return t;
                var n = this["t"];
                if (0 != (t = n - e["t"])) return this["s"] < 0 ? -t : t;

                while (0 <= --n) if (0 != (t = this[n] - e[n])) return t;

                return 0;
            }, b["prototype"]["bitLength"] = function oe() {
                return this["t"] <= 0 ? 0 : this["DB"] * (this["t"] - 1) + y(this[this["t"] - 1] ^ this["s"] & this["DM"]);
            }, b["prototype"]["mod"] = function ae(e) {
                var t = w();
                return this["abs"]()["divRemTo"](e, null, t), this["s"] < 0 && 0 < t["compareTo"](b["ZERO"]) && e["subTo"](t, t), t;
            }, b["prototype"]["modPowInt"] = function ue(e, t) {
                var n;
                return n = e < 256 || t["isEven"]() ? new x(t) : new k(t), this["exp"](e, n);
            }, b["ZERO"] = v(0), b["ONE"] = v(1), T["prototype"]["doPublic"] = function ce(e) {
                return e["modPowInt"](this["e"], this["n"]);
            }, T["prototype"]["setPublic"] = function $_CEq(e, t) {
                null != e && null != t && 0 < e["length"] && 0 < t["length"] ? (this["n"] = function n(e, t) {
                    return new b(e, t);
                }(e, 16), this["e"] = parseInt(t, 16)) : console && console["error"] && console["error"]("Invalid RSA public key");
            }, T["prototype"]["encrypt"] = function he(e) {
                var t = function a(e, t) {
                    if (t < e["length"] + 11) return console && console["error"] && console["error"]("Message too long for RSA"), null;
                    var n = [],
                        s = e["length"] - 1;

                    while (0 <= s && 0 < t) {
                        var r = e["charCodeAt"](s--);
                        r < 128 ? n[--t] = r : 127 < r && r < 2048 ? (n[--t] = 63 & r | 128, n[--t] = r >> 6 | 192) : (n[--t] = 63 & r | 128, n[--t] = r >> 6 & 63 | 128, n[--t] = r >> 12 | 224);
                    }

                    n[--t] = 0;
                    var i = new l(),
                        o = [];

                    while (2 < t) {
                        o[0] = 0;

                        while (0 == o[0]) i["nextBytes"](o);

                        n[--t] = o[0];
                    }

                    return n[--t] = 2, n[--t] = 0, new b(n);
                }(e, this["n"]["bitLength"]() + 7 >> 3);

                if (null == t) return null;
                var n = this["doPublic"](t);
                if (null == n) return null;
                var s = n["toString"](16);
                return 0 == (1 & s["length"]) ? s : "0" + s;
            }, T;
        }()
    }

    let rsa = rsa_webpack()
    let T = new rsa()
    // console.log(T["encrypt"]("8f709698ffeda094"))

    return T["encrypt"](key)
}

function aes_encrypt(text, key, iv = "0000000000000000") {
    iv = CryptoJS.enc.Utf8.parse(iv)
    key = CryptoJS.enc.Utf8.parse(key)
    text = CryptoJS.enc.Utf8.parse(text)

    return CryptoJS.AES.encrypt(text, key, {
        mode: CryptoJS.mode.CBC,
        iv: iv,
        padding: CryptoJS.pad.Pkcs7
    }).ciphertext.toString()
}

function get_device_id() {
    var canvas_data = '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'
    // canvas_data = canvas_data.split('')
    // canvas_data[_.random(0, canvas_data.length - 1, false)] = _.random(0, 9, false)
    // canvas_data = canvas_data.join('')
    return CryptoJS.MD5(canvas_data).toString()
}

function get__pow_msg__pow_sign(e, t, n, s, r, i, o) {
    var a = r % 4,
        u = parseInt(r / 4, 10),
        c = function g(e, t) {
            return new Array(t + 1)["join"](e);
        }("0", u),
        _ = s + "|" + r + "|" + n + "|" + i + "|" + t + "|" + e + "|" + o + "|";

    while (1) {
        var rr = function () {
            function e() {
                return (65536 * (1 + Math["random"]()) | 0)["toString"](16)["substring"](1);
            }

            return function () {
                return e() + e() + e() + e();
            };
        }()
        var h = rr(),
            l = _ + h,
            p = void 0;

        switch (n) {
            case "md5":
                p = CryptoJS.MD5(l).toString();
                break;

            case "sha1":
                p = CryptoJS.SHA1(l).toString();
                break;

            case "sha256":
                p = CryptoJS.SHA256(l).toString();
        }

        if (0 == a) {
            if (0 === p["indexOf"](c)) return {
                "pow_msg": _ + h,
                "pow_sign": p
            };
        } else if (0 === p["indexOf"](c)) {
            var f = void 0,
                d = p[u];

            switch (a) {
                case 1:
                    f = 7;
                    break;

                case 2:
                    f = 3;
                    break;

                case 3:
                    f = 1;
            }

            if (d <= f) return {
                "pow_msg": _ + h,
                "pow_sign": p
            };
        }
    }
}

function get_biht(code1, code2) {
    let temp = function (t) {
        var e = 5381;
        var n = t["length"];
        var o = 0;

        while (n--) {
            e = (e << 5) + e + t["charCodeAt"](o++);
        }

        e &= ~(1 << 31);
        return e;
    }

    return temp(code2 + temp(code1))
}

function get_w(identify_list, lot_number, captcha_id, pow_detail, code1, code2) {
    // identify_list = [[1, 2]]
    // lot_number = "59fa8a4d21fd495b8cac324656902198"
    // captcha_id = "08c16c99330a5a1d6b7f4371bbd5a978"
    // pow_detail = {
    //     "version": "1",
    //     "bits": 13,
    //     "datetime": "2023-11-12T19:08:02.224076+08:00",
    //     "hashfunc": "sha1"
    // }
    // code1 = "function Fmhz(t){var cwl=AdJTB.EoY()[0][7];for(;cwl!==AdJTB.EoY()[3][6];){switch(cwl){case AdJTB.EoY()[3][7]:var e=5381;var n=t[TioX(17)];var o=0;while(n--){e=(e<<5)+e+t[UBkT(26)](o++);}e&=~(1<<31);return e;break;}}}"
    // code2 = "function GErG(t){var dkV=AdJTB.EoY()[3][7];for(;dkV!==AdJTB.EoY()[3][6];){switch(dkV){case AdJTB.EoY()[0][7]:if(t[UBkT(84)]&&t[UBkT(34)]){t[e]=Fmhz(GErG[TioX(50)]()+Fmhz(Fmhz[UBkT(50)]()))+UBkT(78);}return Fmhz(Fmhz[TioX(50)]());break;}}}"


    let pow_msg_sign = get__pow_msg__pow_sign(lot_number, captcha_id,
        pow_detail['hashfunc'], pow_detail['version'], pow_detail['bits'], pow_detail['datetime'], "")
    let params = {
        "passtime": _.random(1500, 4000, false),
        "userresponse": identify_list,
        "device_id": get_device_id(),
        "lot_number": lot_number,
        "pow_msg": pow_msg_sign['pow_msg'],
        "pow_sign": pow_msg_sign['pow_sign'],
        "geetest": "captcha",
        "lang": "zh",
        "ep": "123",
        "biht": get_biht(code1, code2),
        "ERjg": "fyvw",
        "em": {
            "ph": 0,
            "cp": 0,
            "ek": "11",
            "wd": 1,
            "nt": 0,
            "si": 0,
            "sc": 0
        }
    }

    let key = l()
    // let key = "8f709698ffeda094"
    // console.log(JSON.stringify(params))
    // console.log(key)
    // return aes_encrypt('{"passtime":529,"userresponse":[[2,2],[2,1],[3,1]],"device_id":"74bdc8b1cf8ae04f096d1fea34b0bc60","lot_number":"a4e561fe55734e10a9d8b7439e8217ec","pow_msg":"1|13|sha1|2023-11-12T19:40:00.167590+08:00|08c16c99330a5a1d6b7f4371bbd5a978|a4e561fe55734e10a9d8b7439e8217ec||2ae0cd59e0efb4e7","pow_sign":"00069c386ea2688f76ceb422b513ed0f789ea0bf","geetest":"captcha","lang":"zh","ep":"123","biht":"1426265548","ERjg":"fyvw","em":{"ph":0,"cp":0,"ek":"11","wd":1,"nt":0,"si":0,"sc":0}}', 'fe8de23be8ca3410')

    let u = rsa(key)

    return {
        'w': aes_encrypt(JSON.stringify(params), key) + u,
        'params': JSON.stringify(params),
        'key': key
    }
}

//! =============================================================================

// console.log(get_w())
// console.log(get_device_id())