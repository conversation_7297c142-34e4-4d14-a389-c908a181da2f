import base64
import json

import cv2
import requests


def b64_api(username, password, img, ID="35484990"):
    # with open(img_path, 'rb') as f:
    #     b64_data = base64.b64encode(f.read())
    img = base64.b64encode(img)
    b64 = img.decode()
    data = {"username": username, "password": password, "ID": ID, "b64": b64, "version": "3.1.1"}
    data_json = json.dumps(data)
    result = json.loads(requests.post("http://www.fdyscloud.com.cn/tuling/predict", data=data_json).text)
    return result


if __name__ == "__main__":
    img_path = r"C:\Users\<USER>\Downloads\73d83b815aab4737bec4e362fe8061b1.jpg"
    # result = b64_api(username="jiejie", password="ss520999", img_path=img_path, ID="35484990")
    # print(result)

    result = {'code': 1, 'message': '', 'data': {'顺序1': {'文字': '理', 'X坐标值': 197, 'Y坐标值': 115},
                                                 '顺序2': {'文字': '事', 'X坐标值': 142, 'Y坐标值': 46},
                                                 '顺序3': {'文字': '长', 'X坐标值': 33, 'Y坐标值': 80}}}
    a = result['data']
    image = cv2.imread(r"C:\Users\<USER>\Downloads\73d83b815aab4737bec4e362fe8061b1.jpg")
    for i in result['data'].values():
        x = i['X坐标值']
        y = i['Y坐标值']
        cv2.circle(image, (x, y), 20, (0, 255, 0), 2)
        x = int(round(x / 300, 4) * 10000)
        y = int(round(y / 200, 4) * 10000)
        print(x)
        print(y)
        print('--------------------------------')

    cv2.imwrite('verify.png', image)
