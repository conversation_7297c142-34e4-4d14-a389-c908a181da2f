import requests
import urllib3
import re
from loguru import logger

urllib3.disable_warnings()
timeout = 3
headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
}
host = 'https://bidding.sysu.edu.cn'

url = f"{host}/information/notice"
session = requests.session()
response = session.get(url, headers=headers, verify=False, timeout=timeout)
with open('rs5.html', 'w', encoding='utf-8') as f:
    f.write(response.text)

js_url = re.findall(r'src="(/.*?\.js)"', response.text)[0]
logger.info(f"js_url: {js_url}")

js_code = session.get(f'{host}{js_url}', headers=headers, verify=False, timeout=timeout).text
with open('rs5.js', 'w', encoding='utf-8') as f:
    f.write(js_code)

auto_js = re.findall(r'r="m">(\(function\(\).*?)</script>', response.text)[0]
logger.info(f"auto_js: {auto_js}")
