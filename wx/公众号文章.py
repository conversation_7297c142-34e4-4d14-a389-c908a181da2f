import requests
import json
import re
from urllib.parse import quote
import time

headers = {
    "referer": "https://mp.weixin.qq.com/cgi-bin/appmsg?t=media/appmsg_edit_v2&action=edit&isNew=1&type=77&createType=0&token=1984120152&lang=zh_CN&timestamp=1750845715187",
    "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
}
cookies = {
    "noticeLoginFlag": "1",
    "remember_acct": "1767518397%40qq.com",
    "appmsglist_action_3191410391": "card",
    "wxuin": "50845576422499",
    "ua_id": "YeCliBjSJEcJzMgbAAAAAM2fhLt3-81U13O7kptYsnw=",
    "mm_lang": "zh_C<PERSON>",
    "_clck": "3191410391|1|fxf|0",
    "uuid": "09f639ca6a68adde6113c57066fde218",
    "bizuin": "3191410391",
    "ticket": "7686d3e3cd5fe19c41c1662eb8757cb9af1b9439",
    "ticket_id": "gh_5a68a44ffe2c",
    "slave_bizuin": "3191410391",
    "cert": "KlDZSIRGfKMY9MzJsQPVuD9ACeLMP3LY",
    "rand_info": "CAESILoN35bSFpp66IgrZLm7amuvrTney6lUYOgQwIsQfKO6",
    "data_bizuin": "3191410391",
    "data_ticket": "dZzq57BfYC65677e4UZQ164PM1Iie/z2gTf9O1YjhY8F8Ndiw81Be2mjX7Wtu3pj",
    "slave_sid": "ejNDR2Iybm1aeGpXeE9SdnpGNXp3V2F4bVVVbkpHYWN2WXZ2TXJmOWp0RnlydDRqT0dnbnRNeDdrd1NIUm1SQzE4TlJhdlZyUzkySHRiR2g5a3JHbzN3TXptTkg4U01DUTJKSENaQ3hSV3FUVkRtd3ZrM1VpcDZsMDdheTE1eVIwMFhvZ1FlbEJGcm54ckN5",
    "slave_user": "gh_5a68a44ffe2c",
    "xid": "64d5697be7fcf2e2e0f626bc66de51a5",
    "openid2ticket_o1w45vr-OryLVwcKQpzEv3UsIgrk": "XsJKQt4FYvuM/VI37l8WThEATEZX3hYahJhVWxQeLK8=",
    "_clsk": "aalyf0|1751939328284|3|1|mp.weixin.qq.com/weheat-agent/payload/record"
}
gzh = [
    # {
    #     "fakeid": "MzkzODIxNDA4Mg==",
    #     "nickname": "北京通信业",
    # },
    # {
    #     "fakeid": "Mzg4MTU4MTQ4Mg==",
    #     "nickname": "重庆信息通信业",
    # },
    # {
    #     "fakeid": "Mzg3MDU4NTE1OQ==",
    #     "nickname": "海南省通信管理局",
    # },
    # {
    #     "fakeid": "MzIzNDU1NDc5OQ==",
    #     "nickname": "江苏通信业",
    # },
    # {
    #     "fakeid": "MzkyNjIxNjI3NA==",
    #     "nickname": "津通信",
    # },
    # {
    #     "fakeid": "MzUwOTA0NDEzNA==",
    #     "nickname": "辽宁省通信管理局",
    # },
    # {
    #     "fakeid": "Mzg4OTE5MTcwMg==",
    #     "nickname": "贵州省通信管理局",
    # },
    # {
    #     "fakeid": "MzkzODIwNTcyMA==",
    #     "nickname": "江西信息通信业",
    # },
    # {
    #     "fakeid": "MzIzODkzMjE4OA==",
    #     "nickname": "河南省通信管理局",
    # },
    # {
    #     "fakeid": "MzU5NzA0NjIzMg==",
    #     "nickname": "广东信息通信业",
    # },
    # {
    #     "fakeid": "MzkyMTIxNTU4NQ==",
    #     "nickname": "山西省通信管理局",
    # },

    # {
    #     "fakeid": "MzA3ODk3NDEzMQ==",
    #     "nickname": "甘肃省通信管理局",
    # },
    # {
    #     "fakeid": "Mzg3ODI0NDYxOA==",
    #     "nickname": "浙江省通信管理局",
    # },
    # {
    #     "fakeid": "Mzg3MTQ0NTU5MA==",
    #     "nickname": "安徽省通信管理局",
    # },
    # {
    #     "fakeid": "Mzg4OTU2NTQxOA==",
    #     "nickname": "吉林省通信管理局",
    # },
    # {
    #     "fakeid": "Mzg2NDU3MTUxOQ==",
    #     "nickname": "冀通信",
    # },
    # {
    #     "fakeid": "MzkxNTIxNTQwOA==",
    #     "nickname": "四川省通信管理局",
    # },
    # {
    #     "fakeid": "MzU5MTE1MzY1MQ==",
    #     "nickname": "湖南信息通信业",
    # },
    # {
    #     "fakeid": "MzUxMjczMDU0MQ==",
    #     "nickname": "湖北省通信管理局",
    # },
    # {
    #     "fakeid": "MzU3NzQ1ODk1OA==",
    #     "nickname": "广西区通信管理局",
    # },
    # {
    #     "fakeid": "MzU0NjI2Mjg2MA==",
    #     "nickname": "深圳信息通信业",
    # },
    # {
    #     "fakeid": "MzIxMzg1NTk3OQ==",
    #     "nickname": "青海省通信管理局",
    # },
    # {
    #     "fakeid": "Mzk0NzIwNDY2Mg==",
    #     "nickname": "山东省通信管理局",
    # },
    # {
    #     "fakeid": "Mzg5MzU4MzI3Nw==",
    #     "nickname": "西藏自治区通信管理局",
    # },
    {
        "fakeid": "MzI5ODc0MTE0OQ==",
        "nickname": "陕西省通信管理局",
    },
    {
        "fakeid": "Mzg4NTU4MTU3Ng==",
        "nickname": "云南省通信管理局",
    },
    {
        "fakeid": "MzAwOTYxNTU5Nw==",
        "nickname": "上海通信圈",
    },
    {
        "fakeid": "Mzg3NzU4ODI2Nw==",
        "nickname": "新疆维吾尔自治区通信管理局",
    },
    {
        "fakeid": "Mzg5MDU4MDcxMg==",
        "nickname": "内蒙古通信管理局",
    },
    {
        "fakeid": "MzAxMzI2Nzk4NA==",
        "nickname": "龙江通信",
    },
    {
        "fakeid": "MzI3ODI4MDE1OQ==",
        "nickname": "八闽通信",
    },
    {
        "fakeid": "MzI5NjYwMTk4Nw==",
        "nickname": "宁夏信息通信业",
    },
]

url = "https://mp.weixin.qq.com/cgi-bin/appmsgpublish"


def get_article_list(fakeid, begin=0, all_articles=None, query=''):
    if all_articles is None:
        all_articles = []

    params = {
        "sub": "search", "search_field": "7", "fakeid": fakeid, "type": "101_1",
        "free_publish_type": "1", "sub_action": "list_ex",
        "fingerprint": "fb1c880cbf725b72011bac0073eb1f00", "token": "957612441",
        "lang": "zh_CN", "f": "json", "ajax": "1",
        "begin": str(begin),
        "count": "20",
        "query": quote(query),
    }

    response = requests.get(url, headers=headers, cookies=cookies, params=params)

    if 'invalid' in response.json().get('base_resp', {}).get('err_msg'):
        print(response.text)
        print("Session expired, please refresh the session.")
        return all_articles

    publish_page_data = json.loads(response.json()['publish_page'])
    total_count = publish_page_data['total_count']

    # 处理当前页的文章
    current_page_articles = []
    for i in publish_page_data['publish_list']:
        ii = json.loads(i['publish_info'])
        title = re.sub(r'<[^>]+>', '', ii['appmsgex'][0]['title'])
        url_ = ii['appmsgex'][0]['link']
        current_page_articles.append((title, url_))

    all_articles.extend(current_page_articles)
    print(f"已获取 {len(all_articles)} / {total_count} 篇文章")

    # 如果当前页有文章且还有更多文章，递归获取下一页
    if current_page_articles and len(all_articles) < total_count:
        time.sleep(1)
        return get_article_list(fakeid, begin + 20, all_articles, query)

    print(f"Total articles found: {len(all_articles)}")
    return all_articles


bb = []
for j in gzh[:]:
    print(j['nickname'])
    aa = get_article_list(j['fakeid'], query='app 通报')
    # bb = get_article_list(j['fakeid'], query='app')
    # print(list(set(bb)))
    # print(aa)
    bb.extend(aa)
    time.sleep(1)

print(bb)
