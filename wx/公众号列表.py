import requests

headers = {
    "accept": "*/*",
    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
    "cache-control": "no-cache",
    "dnt": "1",
    "pragma": "no-cache",
    "priority": "u=1, i",
    "referer": "https://mp.weixin.qq.com/cgi-bin/appmsg?t=media/appmsg_edit_v2&action=edit&isNew=1&type=10&token=1178717204&lang=zh_CN&timestamp=1751271960200",
    "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"macOS\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
    "x-requested-with": "XMLHttpRequest"
}
cookies = {
    "noticeLoginFlag": "1",
    "remember_acct": "1767518397%40qq.com",
    "appmsglist_action_3191410391": "card",
    "wxuin": "50845576422499",
    "ua_id": "YeCliBjSJEcJzMgbAAAAAM2fhLt3-81U13O7kptYsnw=",
    "mm_lang": "zh_CN",
    "_clck": "3191410391|1|fx7|0",
    "cert": "u4irz_gGXC36x0pyvZ4cBUjWKcGB_On_",
    "uuid": "fe297ccd136c8b6a5fa731fe02cbc927",
    "bizuin": "3191410391",
    "ticket": "86fce766a0f01f863029184860c1f4cb7ebb3f6c",
    "ticket_id": "gh_5a68a44ffe2c",
    "slave_bizuin": "3191410391",
    "rand_info": "CAESIH2jC1AMKyyuCmgFgBMFHwJJNDKZMoa6ejH89yCLyOa0",
    "data_bizuin": "3191410391",
    "data_ticket": "LODCW21es7k4FzmmXaVvmPzxCH7Z2CDuYOTUmxXLew34hzzZpzbevpZM2s4ktnYb",
    "slave_sid": "b19PaWpZQnV6RERtQkJrVTRaNnk5SmJUR1NqcXBPX3dsTlBYOF9YVkpXTkoya21abUxKN21GSWpLTFVYeTlVUGNzaG5RZ2FEWFdENlNFVV9vck9LcFVKMEdaanFiQllkOXB5OEViMlZ5RnZmaUZZbXowbzdQSkYwRk5UN2hoSlFwTWp1bTJ2VnhQWE9KYjJR",
    "slave_user": "gh_5a68a44ffe2c",
    "xid": "836e948e9794d62494e5c0565008b68b",
    "openid2ticket_o1w45vr-OryLVwcKQpzEv3UsIgrk": "KMnx27m2SGrPfP9YicsP+2wbyhCgfqpF+1sggTZTsow=",
    "_clsk": "oma8g2|1751271962201|6|1|mp.weixin.qq.com/weheat-agent/payload/record"
}
url = "https://mp.weixin.qq.com/cgi-bin/searchbiz"
params = {
    "action": "search_biz",
    "begin": "0",
    "count": "10",
    "query": "通信管理局",
    "fingerprint": "fb1c880cbf725b72011bac0073eb1f00",
    "token": "1178717204",
    "lang": "zh_CN",
    "f": "json",
    "ajax": "2"
}
response = requests.get(url, headers=headers, cookies=cookies, params=params)

print(response.json())


