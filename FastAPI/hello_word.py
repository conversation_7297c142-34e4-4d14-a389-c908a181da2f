from typing import Union, List, Set, Dict
import uvicorn
from fastapi import FastAPI, Query, Depends, Header, Form, File, UploadFile, HTTPException, Response, Request
from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel, Field
from fastapi.responses import J<PERSON>NResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import time

app = FastAPI()


@app.get("/")
async def root():
    time.sleep(1)
    return {
        "message": "Hello world"
    }


# 动态路径的请求
@app.get("/param/{item_id}")
async def get_itemId(item_id: int):
    return {"item_id": item_id}


fake_items_db = [{"item_name": "Foo"}, {"item_name": "Bar"}, {"item_name": "Baz"}]


# ! 包含参数请求
@app.get("/get")
async def get1(id: int = 10):
    return {"id": id, "data": fake_items_db[:3]}


# ! 可选参数的设置
@app.get('/get')
async def get2(id: int = 10, limit: Union[int, None] = None):
    if limit:
        return {"id": id, "data": fake_items_db[:limit]}
    return {"id": id, "data": fake_items_db[:3]}


# @app.get('/items')
# async def read_items(q: Union[str, None] = Query(default=None, max_length=50, min_length=5, pattern="^shushujiejie.*")):
#     result = {"items": [{"id1": "1"}, {"id2": "2"}]}
#     if q:
#         result.update({q: q})
#     return result


"""
Query函数参数: 
default: 参数的默认值
pattern (regex): 正则表达式检验
max_length: 最大值检验
min_length: 最小值检验
title: 参数的标题。用作在 OpenAPI 和自动 API 文档用户界面中作为 API 的标题/名称使用
description: 参数的说明。用作在 OpenAPI 和自动 API 文档用户界面中对该参数的描述
gt: 要求参数大于这个值，必须为数字
"""


class Item(BaseModel):
    name: str
    description: Union[str, None] = None
    price: float = Field(gt=0, description="必须大于0")  # ! 使用Filed声明效验
    tax: Union[float, None] = None

    # ! 对于接口文档 声明示例
    class Config:
        json_schema_extra = {
            "example": {
                "name": "jiejieshushu",
                "description": "I love jiejie you",
                "price": 500,
                "tax": 20
            }
        }


@app.post("/items")
async def create_items(item: Item):
    print("item type: ", type(item))
    item_dict = item.model_dump()
    print("item_dict type: ", type(item_dict))
    if item.tax:
        item_dict.update({"price_with_tax": item.price + item.tax})

    return item_dict


# @app.put("/items/{item_id}")
# async def create_items(item_id: int, item: Item):
#     return {"item_id": item_id, **item.model_dump()}


# ! 高级类型定义 可以嵌套
class Item2(BaseModel):
    a: List[str] = []
    b: Set[str] = set()
    c: Dict[str, int] = {}


# ! GET查询的请求体参数拼装
# !! 第一种实现方法
async def common_parameters(q: Union[str, None] = None, skip: int = 0, limit: int = 10):
    return {"q": q, "skip": skip, "limit": limit}


@app.get('/items/')
async def read_items(commons: dict = Depends(common_parameters)):
    return commons


# !! 第二种实现方法
class Student(BaseModel):
    name: str
    age: Union[int, None] = None


@app.get("/student")
async def read_student(student: Student = Depends()):
    print(type(student))
    return dict(student)


items = {}


# ! put更新
@app.put("/items/{item_id}", response_model=Item)
async def update_item(item_id: str, item: Item):
    update_item_encoded = jsonable_encoder(item)  # ! 转为json格式字典
    items[item_id] = update_item_encoded
    print(update_item_encoded)
    print(type(update_item_encoded))

    return update_item_encoded


# ! 请求头部
@app.get('/headers/')
async def get_headers(admin_token: Union[str, None] = Header(default=None)):
    return {'Authorization': admin_token}


# ! 响应模型
items2 = {
    "foo": {"name": "Foo", "price": 50.2},
    "bar": {"name": "Bar", "description": "The Bar fighters", "price": 62, "tax": 20.2},
    "baz": {
        "name": "Baz",
        "description": "There goes my baz",
        "price": 50.2,
        "tax": 10.5,
    },
}


# ! response_model_exclude排除指定属性
@app.get('/items/{item_id}/public', response_model=Item, response_model_exclude={"tax"})
async def read_item_public(item_id: str):
    return items2[item_id]


class UserIn(BaseModel):
    username: str
    password: str
    full_name: Union[str, None] = None


class UserOut(BaseModel):
    username: str
    full_name: Union[str, None] = None


@app.post("/user/", response_model=UserOut)
async def create_user(user_in: UserIn):
    print(dict(user_in))
    return user_in


# ! 表单数据请求
@app.post("/login")
async def login(username: str = Form(), password: str = Form()):
    return {"username": username, "password": password}


# ! 上传文件请求
@app.post("/file/")
async def create_file(file: bytes = File()):
    return {"file length": len(file)}


@app.post("/uploadfile")
async def upload_file(file: UploadFile):
    return {"file_name": file.filename}


"""
UploadFile的属性
filename: 
content_type: e.g image/jpeg
file: 
write(data) (str or bytes)
read(size)  (int)
seek(offset) (int)
close() 
在async def 搭配「await」使用, def 直接访问
"""


# ! 多文件上传

@app.post("/files")
async def create_files(files: List[bytes] = File()):
    return {"files_size": [len(file) for file in files]}


@app.post("/uploadfiles")
async def upload_files(files: List[UploadFile]):
    return {"files_name": [file.filename for file in files]}


# ! 抛出错误
# !! 抛出普通浏览器错误
@app.get("/scrapy")
async def scrape():
    raise HTTPException(status_code=400, detail="傻逼别爬了，进局子吧")


# !! 抛出自定义错误
class UvicornException(Exception):
    def __init__(self, name: str):
        self.name = name


@app.exception_handler(UvicornException)
async def uvicorn_exception_handler(request: Request, exc: UvicornException):
    return JSONResponse(
        status_code=418,
        content={"message": f"Oops! {exc.name} did something. There goes a rainbow..."}
    )


@app.get("/exception/{name}")
async def exception(name: str):
    if name == "傻逼":
        raise UvicornException(name)

    return "你不是傻逼"


class Item3(BaseModel):
    name: str = None


# ! 路径操作配置
@app.get("/test", response_model=Item3, status_code=200, tags=["test"], summary="Test", description="do test")
async def test():
    """
    Create an item with all the information:

    - **name**: each item must have a name
    - **description**: a long description
    - **price**: required
    - **tax**: if the item doesn't have tax, you can omit this
    - **tags**: a set of unique tag strings for this item
    """
    return "test"


"""
返回状态: status_code
接口分类: tags
接口说明: summary接口标题 description接口说明
"""


# ! 创建中间件
@app.middleware('http')
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers['X-Process-Time'] = str(process_time)
    return response


# ! 配置CORS
origins = ['*']
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,  # ! 表示允许的请求服务器地址
    allow_credentials=True,  # ! 表示指示跨域请求是否支持 cookies 默认是 False
    allow_methods=['*'],  # ! 表示允许的请求方法
    allow_headers=['*']  # ! 表示允许的求头部
)

# ! 挂载静态资源
app.mount("/static", StaticFiles(directory='./static'), name='static')

if __name__ == '__main__':
    uvicorn.run("__main__:app", host="127.0.0.1", port=8000, reload=True)
