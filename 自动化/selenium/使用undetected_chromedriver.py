import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
import time
from retrying import retry
import datetime

chrome_options = uc.ChromeOptions()
chrome_options.add_argument("--disable-extensions")
chrome_options.add_argument("--disable-popup-blocking")
chrome_options.add_argument("--profile-directory=Default")
chrome_options.add_argument("--ignore-certificate-errors")
chrome_options.add_argument("--disable-plugins-discovery")
chrome_options.add_argument("--incognito")
chrome_options.add_argument('--no-first-run')
chrome_options.add_argument('--no-service-autorun')
chrome_options.add_argument('--no-default-browser-check')
chrome_options.add_argument('--password-store=basic')
chrome_options.add_argument('--no-sandbox')
# chrome_options.page_load_strategy = 'none'
chrome_options.add_argument('--user-data-dir=E:\\demo_program\\python\\pycharm\\temp\\userdata')
# chrome_options.user_data_dir = r'E:\demo_program\python\pycharm\temp\userdata'


driver = uc.Chrome(options=chrome_options,
                   driver_executable_path=r"C:\Users\<USER>\.cache\selenium\chromedriver\win32\111.0.5563.64"
                                          r"\chromedriver.exe")


@retry(wait_fixed=100, stop_max_attempt_number=20)
def click(path):
    driver.find_element(By.XPATH, path).click()


driver.get('https://www.iqiyi.com/')
# click('//*[@id="block-C"]/div/div/div/div[1]/div[1]/div/div[1]/div/div/a/span[2]')

time.sleep(5)
driver.quit()
