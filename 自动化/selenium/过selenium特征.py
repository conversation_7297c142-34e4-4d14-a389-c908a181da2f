from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
import time
import undetected_chromedriver as uc

chrome_options = Options()
# chrome_options.add_argument(
#     'user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
# chrome_options.add_argument('--headless')
# ! 去除“Chrome正受到自动测试软件的控制”的显示
# chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
s = Service(r"C:\Program Files\Google\Chrome\Application\chrome.exe")

# driver = webdriver.Chrome(service=s, options=chrome_options)
driver = webdriver.Chrome(options=chrome_options)
# ! 三
# driver = uc.Chrome(options=chrome_options)

# ! 一

driver.execute_cdp_cmd(
    "Page.addScriptToEvaluateOnNewDocument",
    {
        "source": """
            Object.defineProperty(window, 'chrome', {
                    get: () => {}
            });
            
            Object.defineProperty(navigator, 'webdriver', {
              get: () => false
            });
            
            Object.defineProperty(navigator, 'webdriver', {
                    get: () => [1, 2, 3]
            });
            
            Object.defineProperty(Notification, 'permission', { 
                    get: () => "default"
            });
            """
    },
)

driver.execute_cdp_cmd(
    "Network.setUserAgentOverride",
    {
        "userAgent": driver.execute_script(
            "return navigator.userAgent"
        ).replace("Headless", "")
    },
)

# ! 二
with open('./stealth.min.js') as f:
    driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
        "source": f.read()
    })

driver.get(url='https://www.jd.com/')
time.sleep(1000)
driver.quit()
