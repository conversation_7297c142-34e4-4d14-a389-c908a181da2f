# 案例10-2 瑞数vmp版本 接口的方式（只获取cookie）
from DrissionPage import WebPage, ChromiumPage, ChromiumOptions
import requests
from lxml import etree
co = ChromiumOptions().set_paths(browser_path=r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe")
co.set_load_mode('none')
page = WebPage('d', chromium_options=co)
page.get('http://beijing.chinatax.gov.cn/bjsat/office/jsp/zdsswfaj/wwquery.jsp', retry=3, interval=2, timeout=15)   # 访问网页
print('>>>当前cookie', page.cookies(as_dict=True))
browser_cookies = page.cookies(as_dict=True)
print('>>>VIP9lLgDcAL2T', browser_cookies['VIP9lLgDcAL2T'])
# # page.refresh()
# # browser_cookies = page.cookies(as_dict=True)
# # print('>>>VIP9lLgDcAL2T', browser_cookies['VIP9lLgDcAL2T'])

# 不切换模式，直接page.post 配合page.refresh()

sss = time.time()
for c_page in range(1, 6):
    _post_data = {"orgCode": "11100000000", "bz": "dq", "dq": "东城", 'dqy': str(c_page)}
    for i in range(3):
        page.post("http://beijing.chinatax.gov.cn/bjsat/office/jsp/zdsswfaj/wwquery", data=_post_data, retry=0)
        r = page.response
        print(f"当前页面{c_page} res响应状态码", r.status_code)
        if r.status_code != 200:
            # print(f"切换到{page.mode}模式，刷新网页获取cookie")
            page.refresh()
            # page.cookies_to_session()
            # print(f"切换到{page.mode}模式，继续接口请求")
        else:
            al_res = etree.HTML(r.text)
            for tr in al_res.xpath("//tr"):
                tds = [td.strip() for td in tr.xpath(".//td/text()")]
                print(tds)
            break
print(time.time() -sss)


# # 通过requests的方式
# sss = time.time()
# url = "http://beijing.chinatax.gov.cn/bjsat/office/jsp/zdsswfaj/wwquery"
# for c_page in range(1, 6):
#     _post_data = {"orgCode": "11100000000", "bz": "dq", "dq": "东城", 'dqy': str(c_page)}
#     headers = {
#         'user-agent': page.user_agent,
#         'cookie': f"VIP9lLgDcAL2S={browser_cookies['VIP9lLgDcAL2S']}; VIP9lLgDcAL2T={browser_cookies['VIP9lLgDcAL2T']}",
#         'Content-Type': 'application/x-www-form-urlencoded'
#     }
#     # res = requests.post(url, headers=headers, data=_post_data, timeout=10)
#     # print(f"当前页面{c_page} res响应状态码", res.status_code)
#     for i in range(3):
#         res = requests.post(url, headers=headers, data=_post_data, timeout=10)
#         print(f"当前页面{c_page} res响应状态码", res.status_code)
#         if res.status_code != 200:
#             page.refresh()
#             browser_cookies = page.cookies(as_dict=True)
#             print('VIP9lLgDcAL2T', browser_cookies['VIP9lLgDcAL2T'])
#             headers.update({'cookie': f"VIP9lLgDcAL2S={browser_cookies['VIP9lLgDcAL2S']}; VIP9lLgDcAL2T={browser_cookies['VIP9lLgDcAL2T']}"})
#             continue
#         else:
#             break
#     al_res = etree.HTML(res.text)
#     for tr in al_res.xpath("//tr"):
#         tds = [td.strip() for td in tr.xpath(".//td/text()")]
#         print(tds)
# print(time.time() -sss)

# for i in range(3):
#     res = requests.post(url, headers=headers, data=_post_data, timeout=10)
#     print(f"当前页面{c_page} res响应状态码", res.status_code)
#     if res.status_code != 200:
#         page.refresh()
#         browser_cookies = page.cookies(as_dict=True)
#         print('VIP9lLgDcAL2T', browser_cookies['VIP9lLgDcAL2T'])
#         headers.update({'cookie': f"VIP9lLgDcAL2S={browser_cookies['VIP9lLgDcAL2S']}; VIP9lLgDcAL2T={browser_cookies['VIP9lLgDcAL2T']}"})
#         continue
#     else:
#         break


# # 通过webpage（s）模式替代
# sss = time.time()
# page.change_mode()
# for c_page in range(1, 6):
#     _post_data = {"orgCode": "11100000000", "bz": "dq", "dq": "东城", 'dqy': str(c_page)}
#     for i in range(3):
#         page.post("http://beijing.chinatax.gov.cn/bjsat/office/jsp/zdsswfaj/wwquery", data=_post_data)
#         r = page.response
#         print(f"当前页面{c_page} res响应状态码", r.status_code)
#         if r.status_code != 200:
#             page.change_mode()
#             print(f"切换到{page.mode}模式，刷新网页获取cookie")
#             page.refresh()
#             page.change_mode()
#             print(f"切换到{page.mode}模式，继续接口请求")
#         else:
#             break
#     for tr in page.eles('x://tr'):
#         tds = [td.text for td in tr.eles('x://td')]
#         print(tds)
# print(time.time() -sss)