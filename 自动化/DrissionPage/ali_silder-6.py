import time
import random
import math
import pyautogui
from DrissionPage import ChromiumPage, ChromiumOptions


def get_tracks(data):
    # 存储结果的列表
    tracks = []
    # 遍历数据数组并计算差值
    for i in range(1, len(data)):
        current = data[i]
        previous = data[i - 1]
        tracks.append([current[0] - previous[0], current[1] - previous[1]])
    return tracks


def get_bezier_curve(start_pos, end_pos, control_point):
    # 计算贝塞尔曲线上的点
    curve_points = []
    for t in range(0, 21, 1):
        x = int(math.pow((1 - t / 20), 2) * start_pos[0] + 2 * (1 - t / 20) * t / 20 * control_point[0] + math.pow(
            t / 20, 2) * end_pos[0])
        y = int(math.pow((1 - t / 20), 2) * start_pos[1] + 2 * (1 - t / 20) * t / 20 * control_point[1] + math.pow(
            t / 20, 2) * end_pos[1])
        curve_points.append((x, y))
    return curve_points


def simulate_dragging(start_pos, end_pos, duration):
    # 生成贝塞尔曲线上的点
    control_point = ((start_pos[0] + end_pos[0]) // 2, start_pos[1] - random.randint(10, 20))
    curve_points = get_bezier_curve(start_pos, end_pos, control_point)
    print(curve_points)
    print(get_tracks(curve_points))
    # 模拟鼠标拖动滑块
    pyautogui.moveTo(start_pos, duration=duration / 4)
    time.sleep(0.5)
    pyautogui.mouseDown()
    time.sleep(0.5)
    for point in curve_points:
        pyautogui.moveTo(point, duration=duration / len(curve_points) + random.uniform(-0.05, 0.05))
    pyautogui.moveTo(end_pos, duration=duration / 4)
    pyautogui.mouseUp()


co = ChromiumOptions()
page = ChromiumPage(co)
# page.set.window.size(1920, 1080)  # 使用虚拟屏幕分辨率，例如：1920x1080
# page.get('http://tingshen.court.gov.cn/search/full?address=/page/review/screenPage&dataType=2&pageSize=10')
page.get('https://tingshen.court.gov.cn/live/38998205')


# todo 网站出现滑块后先获取滑块初始（start_pos）和终止（end_pos）鼠标位置，修改对应的start_pos、end_pos后,关闭这个while 1即可
def get_pos():
    width, height = pyautogui.size()
    print(f"屏幕分辨率：{width} x {height}")
    while 1:
        x, y = pyautogui.position()
        print("当前鼠标在屏幕上的位置", x, y)
        time.sleep(2)


""" 只运行一次get_pos，拿到（start_pos）和终止（end_pos）鼠标位置，然后注释 """
# get_pos()

""" 修改完start_pos 和 end_pos 后再次运行 """
# 滑块初始时鼠标位置
start_pos = (1157, 582)
# 滑块终止时鼠标位置
end_pos = (1596, 597)
# 控制滑动时间
duration = 0.1
# # 示例：拖动滑块
simulate_dragging(start_pos, end_pos, duration)
time.sleep(10)
