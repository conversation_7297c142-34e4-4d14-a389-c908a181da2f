import time
import random
from DrissionPage import ChromiumPage, ChromiumOptions

_js = """
sy_trace_total = {}; 
sy_xy = [];
window.sliderElement = document.getElementById('aliyunCaptcha-sliding-slider');
isMouseDown = false;
console.log("注入成功")
window.addEventListener('mousedown', () => {
    if (sliderElement) {
        console.log('元素已出现！');
		console.log(sliderElement);
		// mouseDown

		console.log('Mouse down at:', event.clientX, event.clientY);
		init_clientX = event.clientX;
		init_clientY = event.clientY;
		init_time = new Date()["getTime"]();
		sy_xy.push([event.clientX-init_clientX, event.clientY-init_clientY, 0]);
		isMouseDown = true;

    }
});

sliderElement.addEventListener('mousemove', function(event) {
	if (isMouseDown) {
		console.log('Mouse move at:', event.clientX, event.clientY);
		sy_xy.push([event.clientX-init_clientX, event.clientY-init_clientY, new Date()["getTime"]()-init_time]);
		sessionStorage.setItem('sy_xy',JSON.stringify(sy_xy))  // sessionStorage.getItem('sy_xy')
		let nDiv = document.createElement('div');
		let e = event || window.event;
		nDiv.style.cssText = "position:absolute; width:5px; height:5px; background-color:red; border-radius:50%; z-index:9999; pointer-events:none";
		nDiv.style.left = e.pageX + "px";
		nDiv.style.top = e.pageY + "px";
		document.body.appendChild(nDiv);
		setTimeout(function() {
			nDiv.remove();
		}, 3000)
	}
});

sliderElement.addEventListener('mouseup', function(event) {
	console.log('Mouse up at:', event.clientX, event.clientY);
	sy_xy.push([event.clientX-init_clientX, event.clientY-init_clientY,new Date()["getTime"]()-init_time]);
	isMouseDown = false;  
	console.log("the_end_sy_xy", JSON.stringify(sy_xy));
	sy_offset = event.clientX-init_clientX;
	sy_trace_total[sy_offset+'_'+init_time] = sy_xy;
	sessionStorage.setItem('sy_xy',JSON.stringify(sy_xy))  // sessionStorage.getItem('sy_xy')
	//debugger;
});


"""


def get_tracks(data):
    # 存储结果的列表
    tracks = []
    # 遍历数据数组并计算差值
    for i in range(1, len(data)):
        current = data[i]
        previous = data[i - 1]
        tracks.append([current[0] - previous[0], current[1] - previous[1], current[2] - previous[2]])
    return tracks


def __ease_out_expo(sep):
    if sep == 1:
        return 1
    else:
        return 1 - pow(2, -10 * sep)


def get_slide_track(distance):
    """
    根据滑动距离生成滑动轨迹
    :param distance: 需要滑动的距离
    :return: 滑动轨迹<type 'list'>: [[x,y,t], ...]
        x: 已滑动的横向距离
        y: 已滑动的纵向距离, 除起点外, 均为0
        t: 滑动过程消耗的时间, 单位: 毫秒
    """

    if not isinstance(distance, int) or distance < 0:
        raise ValueError(f"distance类型必须是大于等于0的整数: distance: {distance}, type: {type(distance)}")
    # 初始化轨迹列表
    slide_track = [[0, 0, 0]]
    # 共记录count次滑块位置信息
    count = 30 + int(distance / 2)
    # 初始化滑动时间
    t = random.randint(50, 100)
    # 记录上一次滑动的距离
    _x = 0
    _y = 0
    for i in range(count):
        # 已滑动的横向距离
        x = round(__ease_out_expo(i / count) * distance)
        # 滑动过程消耗的时间
        t += random.randint(10, 20)
        if x == _x:
            continue
        slide_track.append([x, _y, t])
        _x = x
    slide_track.append(slide_track[-1])
    return slide_track


def slide1():
    # ss = 320
    page.wait.ele_loaded('x://div[@id="aliyunCaptcha-sliding-slider"]')
    button = page.ele('x://div[@id="aliyunCaptcha-sliding-slider"]')
    trace = [[0, 0, 0], [0, 0, 2], [2, 0, 159], [3, 0, 168], [5, 0, 177], [8, 1, 185], [13, 2, 194], [17, 3, 203], [21, 4, 212],
             [27, 5, 220], [35, 6, 230], [44, 6, 238], [54, 7, 247], [65, 8, 255], [326, 29, 1035], [316, 30, 1043]]
    trace = get_slide_track(random.randint(320, 325))
    trace = get_tracks(trace)
    print("当前轨迹", trace)
    page.actions.move_to(button).hold()
    for xyt in trace:
        page.actions.move(offset_x=xyt[0], offset_y=xyt[1], duration=xyt[2] / 1000)


co = ChromiumOptions()
page = ChromiumPage(co)
# kwargs = {"source": _js}
# print(page.run_cdp(cmd="Page.addScriptToEvaluateOnNewDocument", **kwargs))
for i in range(3):
    page.get('https://tingshen.court.gov.cn/live/38998205')  # Action: InitCaptchaV2
    time.sleep(1)
    page.run_js(_js)
    time.sleep(1)
    if page.ele('text:请按住滑块，拖动到最右边'):
        slide1()
        print("storage", page.session_storage())
        # 验证失败，请刷新重试
        if page.ele("#aliyunCaptcha-sliding-fail-text"):
            print("验证失败")
            continue
        else:
            print("验证成功")
            with open("./sy_trace", "a+", encoding='utf-8') as f:
                f.write(page.session_storage()['sy_xy'] + "\n")
            break
