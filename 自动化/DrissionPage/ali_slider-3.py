import random

from DrissionPage import ChromiumPage, ChromiumOptions

co = ChromiumOptions()
page = ChromiumPage(co)
# page.set.window.max()
# page.clear_cache()

page.get('https://tingshen.court.gov.cn/live/38998205')  # Action: InitCaptchaV2

# * 一
slide_button = page('x://div[contains(@id, "aliyunCaptcha-sliding-slider")]', timeout=5)
page.actions.move_to(ele_or_loc=slide_button)
page.actions.hold(slide_button).move(320, offset_y=random.randint(1, 3), duration=0.02)

# * 二
ee = page.ele('x://div[@id="aliyunCaptcha-sliding-slider"]')
page.actions.move_to(ee).hold().move(320, duration=0.02).release()
