import time

from DrissionPage import ChromiumPage, ChromiumOptions

co = ChromiumOptions()
page = ChromiumPage(co)

_js = """
sy_trace_total = {}; 
sy_xy = [];
window.sliderElement = document.getElementById('aliyunCaptcha-sliding-slider');
isMouseDown = false;
console.log("注入成功")
window.addEventListener('mousedown', () => {
    if (sliderElement) {
        console.log('元素已出现！');
		console.log(sliderElement);
		// mouseDown

		console.log('Mouse down at:', event.clientX, event.clientY);
		init_clientX = event.clientX;
		init_clientY = event.clientY;
		init_time = new Date()["getTime"]();
		sy_xy.push([event.clientX-init_clientX, event.clientY-init_clientY, 0]);
		isMouseDown = true;

    }
});

sliderElement.addEventListener('mousemove', function(event) {
	if (isMouseDown) {
		console.log('Mouse move at:', event.clientX, event.clientY);
		sy_xy.push([event.clientX-init_clientX, event.clientY-init_clientY, new Date()["getTime"]()-init_time]);
		sessionStorage.setItem('sy_xy',JSON.stringify(sy_xy))  // sessionStorage.getItem('sy_xy')
		let nDiv = document.createElement('div');
		let e = event || window.event;
		nDiv.style.cssText = "position:absolute; width:5px; height:5px; background-color:red; border-radius:50%; z-index:9999; pointer-events:none";
		nDiv.style.left = e.pageX + "px";
		nDiv.style.top = e.pageY + "px";
		document.body.appendChild(nDiv);
		setTimeout(function() {
			nDiv.remove();
		}, 3000)
	}
});

sliderElement.addEventListener('mouseup', function(event) {
	console.log('Mouse up at:', event.clientX, event.clientY);
	sy_xy.push([event.clientX-init_clientX, event.clientY-init_clientY,new Date()["getTime"]()-init_time]);
	isMouseDown = false;  
	console.log("the_end_sy_xy", JSON.stringify(sy_xy));
	sy_offset = event.clientX-init_clientX;
	sy_trace_total[sy_offset+'_'+init_time] = sy_xy;
	sessionStorage.setItem('sy_xy',JSON.stringify(sy_xy))  // sessionStorage.getItem('sy_xy')
	//debugger;
});


"""

# kwargs = {"source": _js}
# print(page.run_cdp(cmd="Page.addScriptToEvaluateOnNewDocument", **kwargs))
page.get('https://tingshen.court.gov.cn/live/38998205')  # Action: InitCaptchaV2
time.sleep(1)
page.run_js(_js)
time.sleep(1)
if page.ele('text:请按住滑块，拖动到最右边'):
    ee = page.ele('x://div[@id="aliyunCaptcha-sliding-slider"]')
    page.actions.move_to(ee).hold().move(320, duration=0.02).release()
print("storage", page.session_storage())
