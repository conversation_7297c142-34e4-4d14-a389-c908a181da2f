from DrissionPage import Chromium, ChromiumOptions


def init_co(port=9222):
    co = ChromiumOptions().set_local_port(port)
    co.ignore_certificate_errors()
    co.set_load_mode('none')  # eager none
    co.set_argument('--no-sandbox')
    co.set_argument("--disable-gpu")
    # co.set_argument('--disable-features=DisableLoadExtensionCommandLineSwitch')
    # co.add_extension('/Users/<USER>/Downloads/pygs-work-parent/apps/dingliangyi/switch_proxy_plugin')
    # co.set_browser_path('/Applications/Microsoft Edge.app')
    # co.incognito(True)
    # co.headless(True)
    # co.set_argument('--auto-open-devtools-for-tabs')  # 自动打开开发者工具
    return co


def get_tab(browser: Chromium):
    tab = browser.latest_tab
    tab.set.cookies.clear()
    # tab.run_cdp('Network.setCacheDisabled', cacheDisabled=True)
    # tab.run_cdp('Runtime.enable')
    # tab.run_cdp('Log.enable')
    # tab.set.load_mode('none')
    tab.set.user_agent(tab.user_agent.replace('Headless', ''))  # 去掉Headless
    tab.listen.start([
        'search/joblist.json',
        'captcha/gettype'
    ])
    return tab
