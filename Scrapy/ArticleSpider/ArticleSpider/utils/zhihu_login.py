from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as Ec
from selenium.webdriver.support.wait import WebDriver<PERSON>ait
from selenium.webdriver.common.action_chains import <PERSON><PERSON>hains
from selenium.webdriver.common.keys import Keys
import time
import requests
import os
import random
import cv2
import numpy as np
import undetected_chromedriver as uc
import math


def ease_in_quad(x):
    return x * x


def ease_out_quad(x):
    return 1 - (1 - x) * (1 - x)


def ease_out_quart(x):
    return 1 - pow(1 - x, 4)


def ease_out_expo(x):
    if x == 1:
        return 1
    else:
        return 1 - pow(2, -10 * x)


def ease_out_bounce(x):
    n1 = 7.5625
    d1 = 2.75
    if x < 1 / d1:
        return n1 * x * x
    elif x < 2 / d1:
        x -= 1.5 / d1
        return n1 * x * x + 0.75
    elif x < 2.5 / d1:
        x -= 2.25 / d1
        return n1 * x * x + 0.9375
    else:
        x -= 2.625 / d1
        return n1 * x * x + 0.984375


def ease_out_elastic(x):
    if x == 0:
        return 0
    elif x == 1:
        return 1
    else:
        c4 = (2 * math.pi) / 3
        return pow(2, -10 * x) * math.sin((x * 10 - 0.75) * c4) + 1


def get_tracks(distance, seconds, ease_func):
    tracks = [0]
    offsets = [0]
    for t in np.arange(0.0, seconds, 0.1):
        ease = globals()[ease_func]
        offset = round(ease(t / seconds) * distance)
        tracks.append(offset - offsets[-1])
        offsets.append(offset)
    return offsets, tracks


class Code:
    '''
    滑动验证码破解
    '''

    def __init__(self, slider_ele=None, background_ele=None, count=1, save_image=False):
        '''

        :param slider_ele:
        :param background_ele:
        :param count:  验证重试次数
        :param save_image:  是否保存验证中产生的图片， 默认 不保存
        '''

        self.count = count
        self.save_images = save_image
        self.slider_ele = slider_ele
        self.background_ele = background_ele

    def get_slide_locus(self, distance):
        distance += 8
        v = 0
        m = 0.3
        # 保存0.3内的位移
        tracks = []
        current = 0
        mid = distance * 4 / 5
        while current <= distance:
            if current < mid:
                a = 2
            else:
                a = -3
            v0 = v
            s = v0 * m + 0.5 * a * (m ** 2)
            current += s
            tracks.append(round(s))
            v = v0 + a * m
        # 由于计算机计算的误差，导致模拟人类行为时，会出现分布移动总和大于真实距离，这里就把这个差添加到tracks中，也就是最后进行一步左移。
        # tracks.append(-(sum(tracks) - distance * 0.5))
        # tracks.append(10)
        return tracks

    def slide_verification(self, driver, slide_element, distance):
        '''

        :param driver: driver对象
        :param slide_element: 滑块元祖
        :type   webelement
        :param distance: 滑动距离
        :type: int
        :return:
        '''
        # 获取滑动前页面的url网址
        start_url = driver.current_url
        print('滑动距离是: ', distance)
        # 根据滑动的距离生成滑动轨迹
        # locus = self.get_slide_locus(distance)
        _, locus = get_tracks(distance + 8, 1, "ease_out_quad")

        print('生成的滑动轨迹为:{},轨迹的距离之和为{}'.format(locus, distance))

        # 按下鼠标左键
        ActionChains(driver).click_and_hold(slide_element).perform()

        time.sleep(0.5)

        # 遍历轨迹进行滑动
        for loc in locus:
            time.sleep(0.01)
            # ActionChains(driver, duration=50).move_by_offset(loc, random.randint(-5, 5)).perform()
            ActionChains(driver).move_by_offset(loc, random.randint(-5, 5)).perform()
            # ActionChains(driver).context_click(slide_element)

        # 释放鼠标
        # ActionChains(driver).release(on_element=slide_element).perform()
        ActionChains(driver).pause(0.3).release(on_element=slide_element).perform()

        # # 判断是否通过验证，未通过下重新验证
        # time.sleep(2)
        # # 滑动之后的yurl链接
        # end_url = driver.current_url

        # if start_url == end_url and self.count > 0:
        #     print('第{}次验证失败，开启重试'.format(6 - self.count))
        #     self.count -= 1
        #     self.slide_verification(driver, slide_element, distance)

    def onload_save_img(self, url, filename="image.png"):
        """
        下载图片并保存
        :param url: 图片网址
        :param filename: 图片名称
        :return:
        """
        try:
            response = requests.get(url)
        except Exception as e:
            print('图片下载失败')
            raise e
        else:
            with open(filename, 'wb') as f:
                f.write(response.content)

    def get_element_slide_distance(self, slider_ele, background_ele, correct=0):
        '''
        根据传入滑块， 和背景的节点， 计算滑块的距离
        :param slider_ele: 滑块节点参数
        :param background_ele:  背景图的节点
        :param correct:
        :return:
        '''
        # 获取验证码的图片
        slider_url = slider_ele.get_attribute('src')
        background_url = background_ele.get_attribute('src')

        # 下载验证码链接
        slider = 'slider.jpg'
        background = 'background.jpg'

        self.onload_save_img(slider_url, slider)

        self.onload_save_img(background_url, background)

        slider_pic = cv2.imread(slider, 0)
        background_pic = cv2.imread(background, 0)
        width, height = slider_pic.shape[::-1]
        slider01 = 'slider01.jpg'
        slider02 = 'slider02.jpg'
        background01 = 'background01.jpg'
        cv2.imwrite(slider01, slider_pic)
        cv2.imwrite(background01, background_pic)
        slider_pic = cv2.imread(slider01)
        slider_pic = cv2.cvtColor(slider_pic, cv2.COLOR_BGR2GRAY)
        slider_pic = abs(255 - slider_pic)
        cv2.imwrite(slider02, slider_pic)
        slider_pic = cv2.imread(slider02)
        background_pic = cv2.imread(background01)

        result = cv2.matchTemplate(slider_pic, background_pic, cv2.TM_CCOEFF_NORMED)

        # 通过数组运算，获取图片缺口位置
        top, left = np.unravel_index(result.argmax(), result.shape)

        # 背景图缺口坐标
        print('当前滑块缺口位置', (left, top, left + width, top + height))

        # 判读是否需求保存识别过程中的截图文件
        if self.save_images:
            loc = [(left + correct, top + correct), (left + width - correct, top + height - correct)]
            self.image_crop(cv2.imread(background), loc)

        else:
            pass
            # 删除临时文件
            # os.remove(slider01)
            # os.remove(slider02)
            # os.remove(background01)
            # os.remove(background)
            # os.remove(slider)
            # print('删除')
            # os.remove(slider)
        # 返回需要移动的位置距离
        return left

    def image_crop(self, image, loc):
        cv2.rectangle(image, loc[0], loc[1], (0, 255, 0), 2)
        cv2.imshow('Show', image)
        # cv2.imshow('Show2', slider_pic)
        # cv2.waitKey(0)
        cv2.destroyAllWindows()


class Login(object):
    def __init__(self, user, password, retry):
        # self.display = Display(visible=0, size=(800, 800))
        # self.display.start()
        # 创建一个参数对象，用来控制chrome以无界面模式打开
        chrome_options = webdriver.ChromeOptions()
        # chrome_options.add_argument('--headless')  # # 浏览器不提供可视化页面
        chrome_options.add_argument('--disable-gpu')  # 禁用GPU加速,GPU加速可能会导致Chrome出现黑屏，且CPU占用率高达80%以上
        # chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')

        self.browser = uc.Chrome(options=chrome_options,
                                 driver_executable_path=r"E:\temp\Software\chromedriver.exe")
        self.wait = WebDriverWait(self.browser, 20)
        self.url = 'https://www.zhihu.com/signin'
        self.sli = Code(save_image=True)
        self.user = user
        self.password = password
        self.retry = retry  # 重试次数

    def login(self):
        self.browser.get(self.url)
        login_element = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, 'div.SignFlow-tabs>div:nth-child(2)')))
        self.browser.execute_script("arguments[0].click();", login_element)

        username = self.wait.until(Ec.element_to_be_clickable((By.CSS_SELECTOR, '.SignFlow-account input')))
        username.send_keys(self.user)
        password = self.wait.until(Ec.element_to_be_clickable((By.CSS_SELECTOR, '.SignFlow-password input')))
        password.send_keys(self.password)

        submit = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, '.Button.SignFlow-submitButton'))
        )
        ActionChains(self.browser).move_to_element(submit).move_by_offset(5, 5).click().perform()
        # self.browser.execute_script("arguments[0].click();", submit)
        submit.send_keys(Keys.RETURN)

        # submit.click()

        k = 1
        while k < self.retry:

            bg_img = self.wait.until(
                Ec.element_attribute_to_include((By.CSS_SELECTOR, 'img[alt="验证码背景"]'), 'src')
            )
            front_img = self.wait.until(
                Ec.element_attribute_to_include((By.CSS_SELECTOR, 'img[alt="验证码滑块"]'), 'src')
            )

            if not bg_img and not front_img:
                continue

            bg_img = self.browser.find_element(By.CSS_SELECTOR, 'img[alt="验证码背景"]')
            front_img = self.browser.find_element(By.CSS_SELECTOR, 'img[alt="验证码滑块"]')

            # 获取验证码滑动距离
            distance = self.sli.get_element_slide_distance(front_img, bg_img)
            print('滑动距离是', distance)

            # 2. 乘缩放比例， -去  滑块前面的距离  下面给介绍
            distance = distance - 3
            print('实际滑动距离是', distance)

            # 滑块对象
            element = self.browser.find_element(By.CSS_SELECTOR, '.yidun_slider')
            # 滑动函数
            self.sli.slide_verification(self.browser, element, distance)

            # 滑动之后的url链接
            time.sleep(5)
            # 登录框
            try:
                submit = self.wait.until(
                    Ec.element_to_be_clickable((By.CSS_SELECTOR, '.Button.SignFlow-submitButton'))
                )
                submit.click()
                time.sleep(3)
            except:
                pass

            end_url = self.browser.current_url
            print(end_url)

            if end_url == "https://www.zhihu.com/":
                cookies = self.get_cookies()
                print(cookies)
                return cookies
            else:
                # reload = self.browser.find_element_by_css_selector("#reload div")
                # self.browser.execute_script("arguments[0].click();", reload)
                time.sleep(3)

                k += 1

        return None

    def get_cookies(self):
        '''
        登录成功后 保存账号的cookies
        :return:
        '''
        cookies = self.browser.get_cookies()
        self.cookies = ''
        for cookie in cookies:
            self.cookies += '{}={};'.format(cookie.get('name'), cookie.get('value'))
        return cookies

    def __del__(self):
        self.browser.quit()


if __name__ == "__main__":
    login = Login("17673355057", "ss520999", 6)
    login.login()
