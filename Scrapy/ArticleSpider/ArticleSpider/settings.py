BOT_NAME = "ArticleSpider"

SPIDER_MODULES = ["ArticleSpider.spiders"]
NEWSPIDER_MODULE = "ArticleSpider.spiders"

# 通过在用户代理上识别自己（和您的网站）进行负责任的爬网
# USER_AGENT = "ArticleSpider (+http://www.yourdomain.com)"

# Obey robots.txt rules
ROBOTSTXT_OBEY = False

# 配置Scrapy执行的最大并发请求数（默认值：16）
# CONCURRENT_REQUESTS = 32

# 为同一网站的请求配置延迟（默认值：0）
# DOWNLOAD_DELAY = 3
# 下载延迟设置将仅支持以下其中一项：
# CONCURRENT_REQUESTS_PER_DOMAIN = 16
# CONCURRENT_REQUESTS_PER_IP = 16

# Disable cookies (enabled by default)
# COOKIES_ENABLED = False

# Disable Telnet Console (enabled by default)
# TELNETCONSOLE_ENABLED = False

# ! headers
# DEFAULT_REQUEST_HEADERS = {
#    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
#    "Accept-Language": "en",
# }

# ! spider中间件
# SPIDER_MIDDLEWARES = {
#    "ArticleSpider.middlewares.ArticlespiderSpiderMiddleware": 543,
# }

# ! 下载中间件
DOWNLOADER_MIDDLEWARES = {
    # "ArticleSpider.middlewares.ArticlespiderDownloaderMiddleware": 543,
    'ArticleSpider.middlewares.RandomUserAgentMiddleware': 543,
    'scrapy.downloadermiddlewares.useragent.UserAgentMiddleware': None,
}

# ! 扩展
# EXTENSIONS = {
#    "scrapy.extensions.telnet.TelnetConsole": None,
# }

# ! item管道
ITEM_PIPELINES = {
    # "ArticleSpider.pipelines.ArticlespiderPipeline": 300,
    # 'ArticleSpider.pipelines.ArticleImagePipeline': 1,
    # 'ArticleSpider.pipelines.JsonWithEncodingPipeline': 2,
    # 'ArticleSpider.pipelines.JsonExporterPipeline': 3,
    # 'ArticleSpider.pipelines.MysqlPipeline': 4,
    'ArticleSpider.pipelines.MysqlTwistedPipeline': 4,
}

# ! 启用和配置AutoThrottle扩展（默认情况下禁用）
# See https://docs.scrapy.org/en/latest/topics/autothrottle.html
AUTOTHROTTLE_ENABLED = True
# 初始下载延迟
AUTOTHROTTLE_START_DELAY = 1
# 在延迟较高的情况下设置的最大下载延迟
# AUTOTHROTTLE_MAX_DELAY = 60
# Scrapy应该并行发送到每个远程服务器的平均请求数
# AUTOTHROTTLE_TARGET_CONCURRENCY = 1.0
# 启用显示收到的每个响应的限制统计信息：
# AUTOTHROTTLE_DEBUG = False

# ! 启用和配置HTTP缓存（默认情况下禁用）
# See https://docs.scrapy.org/en/latest/topics/downloader-middleware.html#httpcache-middleware-settings
# HTTPCACHE_ENABLED = True
# HTTPCACHE_EXPIRATION_SECS = 0
# HTTPCACHE_DIR = "httpcache"
# HTTPCACHE_IGNORE_HTTP_CODES = []
# HTTPCACHE_STORAGE = "scrapy.extensions.httpcache.FilesystemCacheStorage"

# ! 将默认值已弃用的设置设置为经得起将来验证的值
REQUEST_FINGERPRINTER_IMPLEMENTATION = "2.7"
TWISTED_REACTOR = "twisted.internet.asyncioreactor.AsyncioSelectorReactor"
FEED_EXPORT_ENCODING = "utf-8"

import os

images_path = os.path.dirname(os.path.abspath(__file__))
IMAGES_STORE = os.path.join(images_path, "images")
IMAGES_URLS_FIELD = "front_image_url"

MYSQL_DBNAME = "article_spider"
MYSQL_PASSWORD = "ss520999"
MYSQL_USER = "root"
MYSQL_HOST = "127.0.0.1"
SQL_DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"

USERAGENT_TYPE = 'chrome'

# ! scrapy-redis配置使用
SCHEDULER = "scrapy_redis.scheduler.Scheduler"
DUPEFILTER = "scrapy_redis.dupefiler.RFPDupeFilter"
REDIS_URL = "redis://127.0.0.1:6379"
# SCHEDULER_QUEUE_CLASS = "scrapy_redis.queue.PriorityQueue"  # todo 默认
# SCHEDULER_QUEUE_CLASS = "scrapy_redis.queue.FifoQueue"
# SCHEDULER_QUEUE_CLASS = "scrapy_redis.queue.LifoQueue"
SCHEDULER_PERSIST = False  # todo 默认 持久化设置
SCHEDULER_FLUSH_ON_START = False  # todo 默认 是否重爬

