# Define your item pipelines here
#
# Don't forget to add your pipeline to the ITEM_PIPELINES setting
# See: https://docs.scrapy.org/en/latest/topics/item-pipeline.html
import loguru
# useful for handling different item types with a single interface
from itemadapter import ItemAdapter
from scrapy.pipelines.images import ImagesPipeline
from scrapy.exporters import JsonItemExporter
from scrapy.exceptions import DropItem
from twisted.enterprise import adbapi
import json
import codecs
import logging
import MySQLdb


class ArticlespiderPipeline:
    def process_item(self, item, spider):
        return item


class MysqlPipeline(object):
    def __init__(self):
        self.conn = MySQLdb.connect(
            host='127.0.0.1',
            port=3306,
            user='root',
            passwd='ss520999',
            db='article_spider',
            charset='utf8',
            use_unicode=True
        )
        self.cursor = self.conn.cursor()

    def process_item(self, item, spider):
        insert_sql = """
            insert into jobbole_article value (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s) on DUPLICATE key update
             content = values(content),comment_nums = values(comment_nums),fav_nums = values(fav_nums),
             praise_nums = values(praise_nums)
        """
        params = []
        for key, value in item.items():
            params.append(value)
        self.cursor.execute(insert_sql, tuple(params))

        self.conn.commit()

        return item

    def close_spider(self, spider):
        self.cursor.close()
        self.conn.close()


class MysqlTwistedPipeline(object):
    def __init__(self, dbpool):
        self.dbpool = dbpool

    @classmethod
    def from_settings(cls, settings):
        db_params = dict(
            host=settings["MYSQL_HOST"],
            user=settings["MYSQL_USER"],
            passwd=settings["MYSQL_PASSWORD"],
            db='zhihu',
            charset='utf8',
            use_unicode=True,
            cursorclass=MySQLdb.cursors.DictCursor,
        )
        dbpool = adbapi.ConnectionPool('MySQLdb', **db_params)

        return cls(dbpool)

    def process_item(self, item, spider):
        query = self.dbpool.runInteraction(self.do_insert, item)
        query.addErrback(self.handle_error, item, spider)
        return item

    def do_insert(self, cursor, item):
        insert_sql, params = item.get_insert_sql()
        cursor.execute(insert_sql, params)

    def handle_error(self, failure, item, spider):
        logging.error(failure)


class JsonWithEncodingPipeline:
    def __init__(self):
        self.file = codecs.open(r'article.json', 'a', encoding='utf-8')

    def process_item(self, item, spider):
        lines = json.dumps(dict(item), ensure_ascii=False) + '\n'
        self.file.write(lines)
        return item

    def close_spider(self, spider):
        logging.info('spider closed')
        self.file.close()


class JsonExporterPipeline:
    def __init__(self):
        self.file = open(r'article_export.json', 'wb')
        self.exporter = JsonItemExporter(self.file, encoding='utf-8', ensure_ascii=False)
        self.exporter.start_exporting()

    def process_item(self, item, spider):
        self.exporter.export_item(item)
        return item

    def close_spider(self, spider):
        logging.info('article_export closed')
        self.exporter.finish_exporting()
        self.file.close()


class ArticleImagePipeline(ImagesPipeline):
    def item_completed(self, results, item, info):
        if "front_image_url" in item:
            for ok, value in results:
                if ok:
                    item["front_image_path"] = value['path']

        return item


class AioMysql:

    def __init__(self, pool):
        self.pool = pool

    @classmethod
    def from_settings(cls, settings):
        params = dict(
            host=settings['MYSQL_HOST'],
            user=settings['MYSQL_USER'],
            passwd=settings['MYSQL_PASSWORD'],
            db=settings['MYSQL_DBNAME'],
            charset='utf-8',
            use_unicode=True,
            cursorclass=MySQLdb.cursors.DictCursor
        )
        pool = adbapi.ConnectionPool('MySQLdb', **params)

        return cls(pool)

    def process_item(self, item, spider):
        self.pool.runInteraction(self.insert, item)
        self.pool.addErrCallback(item, spider)

        return item

    def insert(self, cursor, item):
        insert_sql, params = item.get_insert_sql()
        cursor.execute(insert_sql, params)

    def handle_error(self, error, item, spider):
        logging.error(error)
