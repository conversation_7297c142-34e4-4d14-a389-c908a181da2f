import re
import jsonpath
import json
import scrapy
from scrapy import Request
from urllib import parse
import logging
import retry
import datetime
import http.cookies
import undetected_chromedriver as uc
from selenium.webdriver.chrome import options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from Scrapy.ArticleSpider.ArticleSpider.items import ZhiHuItemLoader, ZhiHuItem, ZhiHuAnswerItem
from scrapy import signals


class ZhihuSpider(scrapy.Spider):
    name = 'zhihu'
    allowed_domains = ['www.zhihu.com']
    start_urls = ['https://www.zhihu.com/']
    custom_settings = {
        'COOKIE_ENABLED': True,
    }
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) '
                      'Chrome/80.0.3987.163 Safari/537.36',
        'Referer': 'https://www.zhihu.com/signin?next=%2F'
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        chrome_options = options.Options()
        chrome_options.add_argument('--no-first-run')
        chrome_options.add_argument('--disable-gpu')
        self.driver = uc.Chrome(options=chrome_options,
                                driver_executable_path=r"E:\temp\Software\chromedriver.exe")

    def closed(self, spider, reason):
        self.driver.quit()

    def start_requests(self):
        cookies = GetCookie.get_cookies('***********', 'ss520999')
        for url in self.start_urls:
            yield Request(url, headers=self.headers, cookies=cookies, meta={'cookies': cookies})

    def parse(self, response, **kwargs):
        urls = response.css('a::attr(href)').extract()
        urls = [parse.urljoin(response.url, url) for url in urls]
        filter(lambda x: True if x.startswith('https') else False, urls)
        for url in urls:
            result = re.search(r'question/(\d+)', url)
            if result:
                url = f'https://www.zhihu.com/question/{result[1]}'
                yield Request(url, headers=self.headers, cookies=response.meta['cookies'], callback=self.parse_question,
                              meta={'cookies': response.meta['cookies'], 'questionID': result[1]})

    def parse_question(self, response, **kwargs):
        json_data = response.css('#js-initialData::text').extract_first()
        data = json.loads(json_data)
        result = jsonpath.jsonpath(data, f'$..{response.meta["questionID"]}')
        questionID = result[0].get("id")
        title = result[0].get("title")
        content = result[0].get("excerpt")
        # url = result[0].get("url").replace('\u002F', '')
        url = 'https://www.zhihu.com/question/' + str(questionID)
        browsed = result[0].get('visitCount')
        followers = result[0].get('followerCount')
        answer_num = result[0].get('answerCount')

        question_item = ZhiHuItem()
        question_item['questionID'] = questionID
        question_item['title'] = title
        question_item['content'] = content
        question_item['url'] = url
        question_item['browsed'] = browsed
        question_item['followers'] = followers
        question_item['answer_num'] = answer_num

        yield question_item

        next_ = result[1].get('next')
        start_answer = jsonpath.jsonpath(data, '$..entities.answers.*')

        for data in start_answer:
            answer_item = ZhiHuAnswerItem()
            answer_item['answerID'] = jsonpath.jsonpath(data, '$.id')[0]
            answer_item['questionID'] = jsonpath.jsonpath(data, '$.question.id')[0]
            answer_item['author'] = jsonpath.jsonpath(data, '$.author.name')[0]
            answer_item['content'] = re.sub(r'\\u\d{4}', '', jsonpath.jsonpath(data, '$.content')[0])
            answer_item['parise_num'] = jsonpath.jsonpath(data, '$.voteupCount')[0]
            answer_item['comments_num'] = jsonpath.jsonpath(data, '$.commentCount')[0]
            answer_item['create_time'] = jsonpath.jsonpath(data, '$.createdTime')[0]
            answer_item['update_time'] = jsonpath.jsonpath(data, '$.updatedTime')[0]
            answer_item['crawl_time'] = datetime.datetime.now()
            yield answer_item

        yield Request(next_, headers=self.headers, cookies=response.meta['cookies'], callback=self.parse_answers,
                      meta={'cookies': response.meta['cookies']})

    def parse_answers(self, response, **kwargs):
        json_data = response.text
        data = json.loads(json_data)
        datas = jsonpath.jsonpath(data, '$.data')[0]
        is_end = jsonpath.jsonpath(data, '$.paging.is_end')
        next_ = jsonpath.jsonpath(data, '$.paging.next')

        for data in datas:
            data = data.get('target')
            answer_item = ZhiHuAnswerItem()
            answer_item['answerID'] = jsonpath.jsonpath(data, '$.id')[0]
            answer_item['questionID'] = jsonpath.jsonpath(data, '$.question.id')[0]
            answer_item['author'] = jsonpath.jsonpath(data, '$.author.name')[0]
            answer_item['content'] = re.sub(r'\\u\d{4}', '', jsonpath.jsonpath(data, '$.content')[0])
            answer_item['parise_num'] = jsonpath.jsonpath(data, '$.voteup_count')[0]
            answer_item['comments_num'] = jsonpath.jsonpath(data, '$.comment_count')[0]
            answer_item['create_time'] = jsonpath.jsonpath(data, '$.created_time')[0]
            answer_item['update_time'] = jsonpath.jsonpath(data, '$.updated_time')[0]
            answer_item['crawl_time'] = datetime.datetime.now()
            yield answer_item

        if not is_end:
            yield Request(next_, headers=self.headers, cookies=response.meta['cookies'], callback=self.parse_answers,
                          meta={'cookies': response.meta['cookies']})


class GetCookie:
    # @retry(stop_max_attempt_number=10, wait_fixed=100)
    @staticmethod
    def login(driver, account, password):
        wait = WebDriverWait(driver, 10)
        login_element = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, 'div.SignFlow-tabs>div:nth-child(2)')))
        driver.execute_script("arguments[0].click();", login_element)

        username = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, '.SignFlow-account input')))
        username.send_keys('***********')

        password = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, '.SignFlow-password input')))
        password.send_keys('ss520999')

        submit = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, '.Button.SignFlow-submitButton')))
        submit.click()

    @staticmethod
    def get_cookies(account, password):
        chrome_options = options.Options()
        chrome_options.add_argument('--no-first-run')
        chrome_options.add_argument('--disable-gpu')
        # chrome_options.add_argument('--user-data-dir=E:\\demo_program\\python\\pycharm\\Scrapy\\ArticleSpider'
        #                             '\\chrome_user_data')
        driver = uc.Chrome(options=chrome_options,
                           driver_executable_path=r"E:\temp\Software\chromedriver.exe")

        driver.get(ZhihuSpider.start_urls[0])

        # * 登陆
        GetCookie.login(driver, account, password)

        input("回车继续：")

        cookies = driver.get_cookies()
        logging.info(cookies)

        driver.quit()
        cookies_dic = {}
        for cookie in cookies:
            cookies_dic[cookie['name']] = cookie['value']

        return cookies_dic


def cookie_str_to_dict(cookie_str):
    cookie = http.cookies.SimpleCookie()
    cookie.load(cookie_str)

    cookie_dict = {}
    for key, morsel in cookie.items():
        cookie_dict[key] = morsel.value

    return cookie_dict
