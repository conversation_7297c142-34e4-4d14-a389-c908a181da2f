from urllib import parse
import re
import json

import requests
import scrapy
from scrapy import Selector
from scrapy import Request
# from scrapy.http import Request
import undetected_chromedriver as uc
from selenium.webdriver.chrome import options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
import time
from retrying import retry
import logging

from Scrapy.ArticleSpider.ArticleSpider.utils.common import get_md5
from Scrapy.ArticleSpider.ArticleSpider.items import JobboleArticleItem
from scrapy.loader import ItemLoader
from Scrapy.ArticleSpider.ArticleSpider.items import JobboleItemLoader


@retry(stop_max_attempt_number=10, wait_fixed=100)
def login(driver, account, password):
    wait = WebDriverWait(driver, 10)
    a = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '#mat-input-0')))
    b = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '#mat-input-1')))
    a.send_keys(account)
    b.send_keys(password)
    button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, 'div[fxlayout="column"] button')))
    button.click()


def get_cookies(account, password):
    chrome_options = options.Options()
    chrome_options.add_argument('--no-first-run')
    chrome_options.add_argument('--disable-gpu')
    # chrome_options.add_argument('--user-data-dir=E:\\demo_program\\python\\pycharm\\Scrapy\\ArticleSpider'
    #                             '\\chrome_user_data')
    driver = uc.Chrome(options=chrome_options,
                       driver_executable_path=r"E:\temp\Software\chromedriver.exe")

    driver.get('https://account.cnblogs.com/signin')

    # * 登陆
    login(driver, account, password)

    input("回车继续：")

    cookies = driver.get_cookies()
    logging.info(cookies)

    driver.quit()
    cookies_dic = {}
    for cookie in cookies:
        cookies_dic[cookie['name']] = cookie['value']

    return cookies_dic


class JobboleSpider(scrapy.Spider):
    name = "jobbole"
    allowed_domains = ["news.cnblogs.com"]
    start_urls = ["https://news.cnblogs.com/"]
    custom_settings = {
        'COOKIES_ENABLED': True,
    }
    sum = 0

    @classmethod
    def from_crawler(cls, crawler, *args, **kwargs):
        super().from_crawler(crawler, *args, **kwargs)
        return cls()

    def start_requests(self):
        cookies = get_cookies('<EMAIL>', 'ss520999')
        for url in self.start_urls:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) '
                              'Chrome/80.0.3987.163 Safari/537.36',
                'Referer': 'https://news.cnblogs.com/',
                # 'Cookie': cookies
            }
            yield Request(url, headers=headers, dont_filter=True, meta={'cookies': cookies})

    def parse(self, response, **kwargs):
        """
        1. 获取文章列表页中的文章url并交给scrapy下载后并进行解析
        2. 获取下一页的url并交给scrapy进行下载， 下载完成后交给parse
        :param response:
        :param kwargs:
        :return:
        """
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) '
                          'Chrome/80.0.3987.163 Safari/537.36',
            'Referer': 'https://news.cnblogs.com/',
            # 'Cookie': response.meta['cookies']
        }
        post_nodes = response.css('#news_list .news_block')
        for i in post_nodes:
            post_url = i.css('div.content h2.news_entry a::attr(href)').extract_first()
            image_url = i.css('div.content div.entry_summary img::attr(src)').extract_first()
            if image_url.startswith('http'):
                image_url = 'https:' + image_url
            yield Request(url=parse.urljoin(response.url, post_url), headers=headers, cookies=response.meta['cookies'],
                          meta={"front_image_url": image_url},
                          callback=self.parse_detail)

            if self.sum >= 100:
                return

        next_url = response.css('div.pager a:last-child::text').extract_first()
        if next_url == "Next >":
            next_url = response.css('div.pager a:last-child::attr(href)').extract_first()
            yield Request(url=parse.urljoin(response.url, next_url), callback=self.parse,
                          meta={'cookies': response.meta['cookies']})

    def parse_detail(self, response, **kwargs):
        self.sum += 1
        match_re = re.match(r'.*?(\d+)', response.url)
        if match_re:
            url_id = match_re.group(1)

            # * items
            item_loader = JobboleItemLoader(JobboleArticleItem(), response)
            item_loader.add_css('title', '#news_title a::text')
            item_loader.add_css('create_date', 'span.time::text')
            item_loader.add_css('content', '#news_content')
            item_loader.add_css('tags', 'div.news_tags a::text')
            item_loader.add_value('url', response.url)
            item_loader.add_value('url_object_id', get_md5(response.url))
            item_loader.add_value('front_image_url', response.meta['front_image_url'])
            # article_item = item_loader.load_item()

        yield Request(parse.urljoin(response.url, f'/NewsAjax/GetAjaxNewsInfo?contentId={url_id}'), headers={
            'referer': response.url,
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) '
                          'Chrome/111.0.0.0 Safari/537.36',
        }, callback=self.parse_info, meta={'item_loader': item_loader})

    def parse_info(self, response, **kwargs):
        info_json = json.loads(response.text)

        item_loader = response.meta['item_loader']
        item_loader.add_value('comment_nums', info_json['CommentCount'])
        item_loader.add_value('fav_nums', info_json['TotalView'])
        item_loader.add_value('praise_nums', info_json['DiggCount'])

        yield item_loader.load_item()
