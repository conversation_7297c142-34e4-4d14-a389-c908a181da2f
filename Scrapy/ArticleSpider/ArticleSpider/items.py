# Define here the models for your scraped items
#
# See documentation in:
# https://docs.scrapy.org/en/latest/topics/items.html
import datetime
from Scrapy.ArticleSpider.ArticleSpider.settings import SQL_DATETIME_FORMAT
import scrapy
from scrapy.loader.processors import MapCompose, TakeFirst, Identity, Join
from scrapy.loader import ItemLoader


class ArticlespiderItem(scrapy.Item):
    # define the fields for your item here like:
    # name = scrapy.Field()
    pass


class JobboleItemLoader(ItemLoader):
    default_output_processor = TakeFirst()


class JobboleArticleItem(scrapy.Item):
    title = scrapy.Field(
        # input_processor=MapCompose()
        # out_processor=TakeFirst()
    )
    create_date = scrapy.Field()
    url = scrapy.Field()
    url_object_id = scrapy.Field()
    front_image_url = scrapy.Field(
        output_processor=Identity()
    )
    front_image_path = scrapy.Field()
    praise_nums = scrapy.Field()
    comment_nums = scrapy.Field()
    fav_nums = scrapy.Field()
    tags = scrapy.Field(
        out_processor=Join(',')
    )
    content = scrapy.Field()


class ZhiHuItemLoader(ItemLoader):
    pass


class ZhiHuItem(scrapy.Item):
    questionID = scrapy.Field()
    title = scrapy.Field()
    content = scrapy.Field()
    url = scrapy.Field()
    browsed = scrapy.Field()
    followers = scrapy.Field()
    answer_num = scrapy.Field()

    def get_insert_sql(self):
        insert_sql = """
            INSERT INTO question(questionID,title,content,url,browsed,followers,answer_num,crawl_time) 
            VALUES(%s,%s,%s,%s,%s,%s,%s,%s)
             ON DUPLICATE KEY UPDATE content=VALUES(content),browsed=VALUES(browsed),followers=VALUES(followers),
             answer_num=VALUES(answer_num),crawl_time=VALUES(crawl_time)
        """

        crawl_time = datetime.datetime.now().strftime(SQL_DATETIME_FORMAT)

        params = (self['questionID'], self['title'], self['content'],
                  self['url'], self['browsed'], self['followers'], self['answer_num'], crawl_time)

        return insert_sql, params


class ZhiHuAnswerItem(scrapy.Item):
    answerID = scrapy.Field()
    questionID = scrapy.Field()
    author = scrapy.Field()
    content = scrapy.Field()
    parise_num = scrapy.Field()
    comments_num = scrapy.Field()
    create_time = scrapy.Field()
    update_time = scrapy.Field()
    crawl_time = scrapy.Field()

    def get_insert_sql(self):
        insert_sql = """
            INSERT INTO answer(answerID,questionID,author,content,parise_num,comments_num,create_time,update_time,crawl_time) 
            VALUES(%s,%s,%s,%s,%s,%s,%s,%s,%s) ON DUPLICATE KEY UPDATE content=VALUES(content),parise_num=VALUES(parise_num),
            comments_num=VALUES(comments_num),update_time=VALUES(update_time),crawl_time=VALUES(crawl_time)
        """
        create_time = datetime.datetime.fromtimestamp(self['create_time']).strftime(SQL_DATETIME_FORMAT)
        update_time = datetime.datetime.fromtimestamp(self['update_time']).strftime(SQL_DATETIME_FORMAT)
        crawl_time = datetime.datetime.now().strftime(SQL_DATETIME_FORMAT)

        params = (self['answerID'], self['questionID'], self['author'], self['content'],
                  self['parise_num'], self['comments_num'], create_time, update_time, crawl_time)

        return insert_sql, params
