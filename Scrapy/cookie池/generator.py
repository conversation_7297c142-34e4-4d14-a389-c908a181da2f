import json
from loguru import logger
from abc import abstractmethod, ABCMeta
from redis_db import *
from Scrapy.cookie池.login import zhihu


class BaseGenerator(metaclass=ABCMeta):
    def __init__(self, website):
        self.db_password = RedisClient(type='password', website=website)
        self.db_cookie = RedisClient(type='cookie', website=website)

    @abstractmethod
    def generate(self, account, password):
        pass

    def run(self):
        account_password = self.db_password.all()
        for account, password in account_password.items():
            self.generate(account, password)


class ZhihuGenerator(BaseGenerator):

    def generate(self, account, password):
        if self.db_cookie.get(account):
            logger.debug(f'账号: {account} cookie已存在')
        cookie = zhihu.Login(account, password, 6).login()
        cookie = json.dumps(cookie)
        self.db_cookie.set(account, cookie)
