import requests
from redis_db import *
from settings import *
from requests.exceptions import ConnectionError
from abc import ABCMeta, abstractmethod
from loguru import logger
import json


class BaseTester(metaclass=ABCMeta):
    def __init__(self, website):
        self.db_cookie = RedisClient(type='cookie', website=website)

    @abstractmethod
    def test(self, account, cookie):
        pass

    def run(self):
        account_cookies = self.db_cookie.all()
        for account, cookie in account_cookies.items():
            self.test(account, cookie)


class ZhihuTester(BaseTester):
    def __init__(self, website):
        super().__init__(website)

    def test(self, account, cookie):
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
                'Referer': 'https://www.zhihu.com/',
            }
            cookie = json.loads(cookie)
            response = requests.get('https://www.zhihu.com', headers=headers, cookies=cookie, allow_redirects=False)
            if response.status_code == 302:
                self.db_cookie.delete(account)
            else:
                logger.info(f'zhihu account: {account} 有效')
        except ConnectionError:
            logger.warning("Tester Connection Error")
