import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as Ec
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
import time
import requests
import random
import cv2
import numpy as np
import undetected_chromedriver as uc
from Scrapy.cookie池.tools.simulated_trajectory import get_locus_easingFunc, get_locus_acceleration


class Code:
    """
    滑动验证码破解
    """

    def __init__(self, count=1, save_image=False):
        """
        :param count:  验证重试次数
        :param save_image:  是否保存验证中产生的图片， 默认 不保存
        """
        self.count = count
        self.save_images = save_image

    def slide_verification(self, driver, slide_element, distance):
        """
        :param driver: driver对象
        :param slide_element: 滑块元素
        :param distance: 滑动距离
        :return:
        """
        print('滑动距离是: ', distance)

        # 根据滑动的距离生成滑动轨迹
        if distance < 100:
            seconds = round(random.randint(500, 1500) / 1000, 2)
        else:
            seconds = round(random.randint(1000, 2000) / 1000, 2)
        # locus = get_locus_acceleration(distance)
        _, locus = get_locus_easingFunc(distance + 8, seconds, "ease_out_quad")

        print('生成的滑动轨迹为:{},轨迹的距离之和为{}'.format(locus, distance + 8))

        # 按下鼠标左键
        ActionChains(driver).click_and_hold(slide_element).perform()
        time.sleep(0.2)

        # 遍历轨迹进行滑动
        for loc in locus:
            time.sleep(0.01)
            # todo duration=50 ActionChains参数可以控制速度
            ActionChains(driver, duration=50).move_by_offset(loc, random.randint(-5, 5)).perform()

        # 释放鼠标
        ActionChains(driver).pause(0.3).release(on_element=slide_element).perform()

    def get_element_slide_distance(self, slider_ele, background_ele, correct=0):
        """
        根据传入滑块和背景的节点， 计算滑块的距离
        :param slider_ele: 滑块节点参数
        :param background_ele:  背景图的节点
        :param correct:
        :return:
        """
        # 获取验证码的图片
        slider_url = slider_ele.get_attribute('src')
        background_url = background_ele.get_attribute('src')

        # 下载验证码的图片
        slider_path = 'slider.jpg'
        background_path = 'background.jpg'
        self.onload_save_img(slider_url, slider_path)
        self.onload_save_img(background_url, background_path)

        # # 读取灰度图片
        # slider_pic = cv2.imread(slider_path)
        # background_pic = cv2.imread(background_path)
        # slider_pic = cv2.cvtColor(slider_pic, cv2.COLOR_BGR2GRAY)
        # background_pic = cv2.cvtColor(background_pic, cv2.COLOR_BGR2GRAY)
        # # 获取缺口数组的形状
        # width, height = slider_pic.shape[::-1]
        # # 色差的绝对值 处理滑块图
        # slider_pic = abs(255 - slider_pic)

        slider_pic = cv2.imread(slider_path, 0)
        background_pic = cv2.imread(background_path, 0)
        width, height = slider_pic.shape[::-1]
        slider01 = 'slider01.jpg'
        slider02 = 'slider02.jpg'
        background01 = 'background01.jpg'
        cv2.imwrite(slider01, slider_pic)
        cv2.imwrite(background01, background_pic)
        slider_pic = cv2.imread(slider01)
        slider_pic = cv2.cvtColor(slider_pic, cv2.COLOR_BGR2GRAY)
        slider_pic = abs(255 - slider_pic)
        cv2.imwrite(slider02, slider_pic)
        slider_pic = cv2.imread(slider02)
        background_pic = cv2.imread(background01)

        result = cv2.matchTemplate(slider_pic, background_pic, cv2.TM_CCOEFF_NORMED)

        # 通过数组运算，获取图片缺口位置
        right, left = np.unravel_index(result.argmax(), result.shape)

        # 背景图缺口坐标
        print('当前滑块缺口位置', (left, right, left + width, right + height))

        # 判读是否保存识别结果图片
        if self.save_images:
            loc = [(left + correct, right + correct), (left + width - correct, right + height - correct)]
            result_pic = cv2.imread(background_path)
            cv2.rectangle(result_pic, loc[0], loc[1], (0, 255, 0), 2)
            cv2.imshow('result_pic', result_pic)
            cv2.waitKey(500)
            cv2.destroyAllWindows()
            cv2.imwrite('result.jpg', result_pic)

        # 删除临时文件
        os.remove(background_path)
        os.remove(slider_path)
        os.remove(background01)
        os.remove(slider02)
        os.remove(slider01)

        return left

    def onload_save_img(self, url, filepath="image.png"):
        """
        下载图片并保存
        :param url: 图片网址
        :param filepath: 图片路径
        :return:
        """
        try:
            response = requests.get(url)
        except Exception as e:
            print('图片下载失败')
            raise e
        else:
            with open(filepath, 'wb') as f:
                f.write(response.content)


class Login(object):
    def __init__(self, user, password, retry):
        chrome_options = webdriver.ChromeOptions()
        # chrome_options.add_argument('--headless')
        chrome_options.add_argument('--disable-gpu')
        # todo 防止Docker限制的内存空间导致Chrome崩溃,是运行在Docker上的Chrome必须的一个配置选项
        # chrome_options.add_argument('--disable-dev-shm-usage')

        self.browser = uc.Chrome(options=chrome_options,
                                 driver_executable_path=r"E:\temp\Software\chromedriver.exe")
        self.wait = WebDriverWait(self.browser, 20)
        self.url = 'https://www.zhihu.com/signin'
        self.sli = Code(save_image=True)
        self.user = user
        self.password = password
        self.retry = retry  # 重试次数

    def login(self):
        self.browser.get(self.url)

        # 切换到密码登陆面板
        login_element = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, 'div.SignFlow-tabs>div:nth-child(2)')))
        self.browser.execute_script("arguments[0].click();", login_element)

        # 输入账号密码 登陆
        username = self.wait.until(Ec.element_to_be_clickable((By.CSS_SELECTOR, '.SignFlow-account input')))
        username.send_keys(self.user)
        password = self.wait.until(Ec.element_to_be_clickable((By.CSS_SELECTOR, '.SignFlow-password input')))
        password.send_keys(self.password)
        submit = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, '.Button.SignFlow-submitButton'))
        )
        ActionChains(self.browser).move_to_element(submit).click().perform()
        # self.browser.execute_script("arguments[0].click();", submit)
        # submit.send_keys(Keys.RETURN)
        # submit.click()

        k = 0
        while k < self.retry:
            bg_img = self.wait.until(
                Ec.element_attribute_to_include((By.CSS_SELECTOR, 'img[alt="验证码背景"]'), 'src')
            )
            front_img = self.wait.until(
                Ec.element_attribute_to_include((By.CSS_SELECTOR, 'img[alt="验证码滑块"]'), 'src')
            )

            if not bg_img and not front_img:
                continue

            bg_img = self.browser.find_element(By.CSS_SELECTOR, 'img[alt="验证码背景"]')
            front_img = self.browser.find_element(By.CSS_SELECTOR, 'img[alt="验证码滑块"]')

            # 获取验证码滑动距离
            distance = self.sli.get_element_slide_distance(front_img, bg_img)
            print('滑动距离是', distance)

            # 滑块对象
            element = self.browser.find_element(By.CSS_SELECTOR, '.yidun_slider')
            # 滑动函数
            self.sli.slide_verification(self.browser, element, distance)

            # 登录框
            try:
                submit = self.wait.until(
                    Ec.element_to_be_clickable((By.CSS_SELECTOR, '.Button.SignFlow-submitButton'))
                )
                submit.click()
            except Exception as e:
                pass

            time.sleep(1)
            end_url = self.browser.current_url
            print("end_url: ", end_url)

            if end_url == "https://www.zhihu.com/":
                cookies = self.get_cookies()
                print("cookies: ", cookies)
                return cookies
            else:
                print("重新滑动滑块")
                time.sleep(1.5)
                k += 1

        return None

    def get_cookies(self, dict_format=True):
        """
        登录成功后 保存账号的cookies
        :return:
        """
        cookies = self.browser.get_cookies()
        if dict_format:
            cookies_dic = {}
            for cookie in cookies:
                cookies_dic[cookie['name']] = cookie['value']
            return cookies_dic
        else:
            cookie_str = ''
            for cookie in cookies:
                cookie_str += '{}={};'.format(cookie.get('name'), cookie.get('value'))
            return cookie_str

    def __del__(self):
        print('浏览器关闭')
        self.browser.quit()


if __name__ == "__main__":
    login = Login("17673355057", "ss520999", 6)
    cookie = login.login()
    # print(cookie)
