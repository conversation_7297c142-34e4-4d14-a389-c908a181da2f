from flask import Flask, g
from settings import *
from redis_db import *
from loguru import logger
import json

app = Flask(__name__)


@app.route('/')
def index():
    return '欢迎使用cookies池'


def get_G_context():
    for website in website_generators:
        setattr(g, f'{website}_password', RedisClient('password', website))
        setattr(g, f'{website}_cookie', RedisClient('cookie', website))

    return g


@app.route('/<website>/random')
def random(website):
    g_context = get_G_context()
    cookie = getattr(g_context, f'{website}_cookie').random()
    logger.debug('get cookie: ', cookie)
    return cookie


if __name__ == '__main__':
    app.run(host='0.0.0.0')
