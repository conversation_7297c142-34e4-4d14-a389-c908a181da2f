from loguru import logger
import multiprocessing
from settings import *
import sys
import tester as testers
import generator as generators
from server import app
import time

if sys.platform.lower() == 'window':
    multiprocessing.freeze_support()


class Scheduler:
    def run_tester(self, website, cycle=cycle_tester):
        loop = 0
        while True:
            logger.debug(f'Running tester {loop}次')
            tester = getattr(testers, website_tester[website])(website)
            tester.run()

            loop += 1
            time.sleep(cycle)

    def run_generator(self, website, cycle=cycle_generator):
        loop = 0
        while True:
            logger.debug(f'Running generator {loop}次')
            generator = getattr(generators, website_generators[website])(website)
            generator.run()

            loop += 1
            time.sleep(cycle)

    def run_server(self):
        app.run(host=FLASK_HOST, port=FLASK_PORT)

    def run(self, website):
        try:
            generator_process = multiprocessing.Process(target=self.run_generator, args=(website,))
            generator_process.start()
            logger.info(f'run generator process started pid: {generator_process.pid}')

            server_process = multiprocessing.Process(target=self.run_server, args=(website,))
            server_process.start()
            logger.info(f'run server process started pid: {server_process.pid}')

            tester_process = multiprocessing.Process(target=self.run_tester, args=(website,))
            tester_process.start()
            logger.info(f'run tester process started pid: {tester_process.pid}')

            generator_process.join()
            server_process.join()
            tester_process.join()
        except KeyboardInterrupt as e:
            logger.debug(f'received keyboard interrupt signal')
            generator_process.terminate()
            server_process.terminate()
            tester_process.terminate()
        finally:
            logger.info('cookie pool terminated')
