import numpy as np
import math
import matplotlib.pyplot as plt


# todo 来源 https://www.aneasystone.com/archives/2018/03/python-selenium-geetest-crack.html
# https://github.com/gdsmith/jquery.easing/blob/master/jquery.easing.js
# https://easings.net/zh-cn
def ease_in_quad(x):
    return x * x


def ease_out_quad(x):
    return 1 - (1 - x) * (1 - x)


def ease_out_quart(x):
    return 1 - pow(1 - x, 4)


def ease_out_expo(x):
    if x == 1:
        return 1
    else:
        return 1 - pow(2, -10 * x)


def ease_out_bounce(x):
    n1 = 7.5625
    d1 = 2.75
    if x < 1 / d1:
        return n1 * x * x
    elif x < 2 / d1:
        x -= 1.5 / d1
        return n1 * x * x + 0.75
    elif x < 2.5 / d1:
        x -= 2.25 / d1
        return n1 * x * x + 0.9375
    else:
        x -= 2.625 / d1
        return n1 * x * x + 0.984375


def ease_out_elastic(x):
    if x == 0:
        return 0
    elif x == 1:
        return 1
    else:
        c4 = (2 * math.pi) / 3
        return pow(2, -10 * x) * math.sin((x * 10 - 0.75) * c4) + 1


def get_locus_easingFunc(distance, seconds, ease_func):
    tracks = [0]
    offsets = [0]
    ease = globals()[ease_func]
    for t in np.arange(0.0, seconds, 0.1):
        offset = round(ease(t / seconds) * distance)
        tracks.append(offset - offsets[-1])
        offsets.append(offset)
    return offsets, tracks


def get_locus_acceleration(distance):
    distance += 8  # 误差
    v = 0
    m = 0.3  # 保存0.3内的位移
    tracks = []
    current = 0
    mid = distance * 4 / 5
    while current <= distance:
        if current < mid:
            a = 2
        else:
            a = -3
        v0 = v
        s = v0 * m + 0.5 * a * (m ** 2)
        current += s
        tracks.append(round(s))
        v = v0 + a * m

    return tracks


if __name__ == '__main__':
    offset, trajectory = get_locus_easingFunc(215, 2, "ease_out_quad")
    plt.plot(np.linspace(0, 2, len(offset)), offset)
    # plt.plot(np.linspace(0, 2, len(trajectory)), trajectory)
    plt.show()
