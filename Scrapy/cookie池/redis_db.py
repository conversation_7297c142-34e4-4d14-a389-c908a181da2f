import random
import redis
from settings import *


class RedisClient:

    def __init__(self, type, website, host=REDIS_HOST, port=REDIS_POST, password=REDIS_PASSWORD):
        """

        :param type: 储存账号或cookie
        :param website:
        :param host:
        :param port:
        :param password:
        """
        self.type = type
        self.website = website
        self.redis = redis.StrictRedis(host=host, port=port, password=password, decode_responses=True)

    def name(self):
        """
        :return: redis的key名
        """
        return f'{self.type}:{self.website}'

    def set(self, account, value):
        """

        :param account:
        :param value:
        :return:
        """
        self.redis.hset(self.name(), account, value)

    def get(self, account):
        """

        :param account:
        :return: 获取对应的账号密码或cookie
        """
        return self.redis.hget(self.name(), account)

    def delete(self, account):
        """
        删除过期的cookie
        :param account:
        :return:
        """
        self.redis.hdel(self.name(), account)

    def random(self):
        """

        :return: 随机返回一个cookie
        """
        all_cookies = self.redis.hvals(self.name())
        return random.choice(all_cookies)

    def count(self):
        """

        :return: 统计使用个数
        """
        return self.redis.hlen(self.name())

    def usernames(self):
        """

        :return: 返回所有账号
        """
        return self.redis.hkeys(self.name())

    def all(self):
        """

        :return: 返回所有账号和对应的值
        """
        return self.redis.hgetall(self.name())
