[bumpversion]
current_version = 0.7.3
commit = False
tag = False
parse = (?P<major>\d+)\.(?P<minor>\d+)\.(?P<patch>\d+)(\-(?P<release>\w+))?
serialize = 
	{major}.{minor}.{patch}-{release}
	{major}.{minor}.{patch}

[bumpversion:part:release]
optional_value = placeholder
values = 
	a1
	b1
	rc1
	placeholder

[bumpversion:file:VERSION]
search = {current_version}
replace = {new_version}

[bumpversion:file:src/scrapy_redis/__init__.py]
search = __version__ = '{current_version}'
replace = __version__ = '{new_version}'

[bumpversion:file:.cookiecutterrc]
search = version: {current_version}
replace = version: {new_version}

[bumpversion:file:HISTORY.rst]
search = .. comment:: bumpversion marker
replace = .. comment:: bumpversion marker
	
	{new_version} ({now:%Y-%m-%d})
	------------------
