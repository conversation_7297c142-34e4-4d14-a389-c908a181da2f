[MASTER]
persistent=no
jobs=1  # >1 hides results
suggestion-mode=yes  # guess common misconfiguration and emit user-friendly hints
py-version = 3.11.3

[MESSAGES CONTROL]
disable=abstract-method,
        anomalous-backslash-in-string,
        arguments-differ,
        arguments-renamed,
        attribute-defined-outside-init,
        bad-classmethod-argument,
        bad-continuation,
        bad-indentation,
        bad-mcs-classmethod-argument,
        bad-super-call,
        bad-whitespace,
        bare-except,
        blacklisted-name,
        broad-except,
        c-extension-no-member,
        catching-non-exception,
        cell-var-from-loop,
        comparison-with-callable,
        consider-iterating-dictionary,
        consider-using-dict-items,
        consider-using-from-import,
        consider-using-in,
        consider-using-set-comprehension,
        consider-using-sys-exit,
        consider-using-with,
        cyclic-import,
        dangerous-default-value,
        deprecated-method,
        deprecated-module,
        duplicate-code,  # https://github.com/PyCQA/pylint/issues/214
        eval-used,
        expression-not-assigned,
        fixme,
        function-redefined,
        global-statement,
        import-error,
        import-outside-toplevel,
        import-self,
        inconsistent-return-statements,
        inherit-non-class,
        invalid-name,
        invalid-overridden-method,
        isinstance-second-argument-not-valid-type,
        keyword-arg-before-vararg,
        line-too-long,
        logging-format-interpolation,
        logging-not-lazy,
        lost-exception,
        method-hidden,
        misplaced-comparison-constant,
        missing-docstring,
        missing-final-newline,
        multiple-imports,
        multiple-statements,
        no-else-continue,
        no-else-raise,
        no-else-return,
        no-init,
        no-member,
        no-method-argument,
        no-name-in-module,
        no-self-argument,
        no-self-use,
        no-value-for-parameter,
        not-an-iterable,
        not-callable,
        pointless-statement,
        pointless-string-statement,
        protected-access,
        raise-missing-from,
        redefined-argument-from-local,
        redefined-builtin,
        redefined-outer-name,
        reimported,
        signature-differs,
        singleton-comparison,
        super-init-not-called,
        super-with-arguments,
        superfluous-parens,
        too-few-public-methods,
        too-many-ancestors,
        too-many-arguments,
        too-many-branches,
        too-many-format-args,
        too-many-function-args,
        too-many-instance-attributes,
        too-many-lines,
        too-many-locals,
        too-many-public-methods,
        too-many-return-statements,
        trailing-newlines,
        trailing-whitespace,
        unbalanced-tuple-unpacking,
        undefined-variable,
        undefined-loop-variable,
        unexpected-special-method-signature,
        ungrouped-imports,
        unidiomatic-typecheck,
        unnecessary-comprehension,
        unnecessary-lambda,
        unnecessary-pass,
        unreachable,
        unspecified-encoding,
        unsupported-assignment-operation,
        unsubscriptable-object,
        unused-argument,
        unused-import,
        unused-private-member,
        unused-variable,
        unused-wildcard-import,
        use-implicit-booleaness-not-comparison,
        used-before-assignment,
        useless-object-inheritance,  # Required for Python 2 support
        useless-return,
        useless-super-delegation,
        wildcard-import,
        wrong-import-order,
        wrong-import-position
