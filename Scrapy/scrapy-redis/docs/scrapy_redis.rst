scrapy_redis package
====================

Submodules
----------

scrapy_redis.connection module
------------------------------

.. automodule:: scrapy_redis.connection
    :members:
    :undoc-members:
    :show-inheritance:

scrapy_redis.dupefilter module
------------------------------

.. automodule:: scrapy_redis.dupefilter
    :members:
    :undoc-members:
    :show-inheritance:

scrapy_redis.pipelines module
-----------------------------

.. automodule:: scrapy_redis.pipelines
    :members:
    :undoc-members:
    :show-inheritance:

scrapy_redis.queue module
-------------------------

.. automodule:: scrapy_redis.queue
    :members:
    :undoc-members:
    :show-inheritance:

scrapy_redis.scheduler module
-----------------------------

.. automodule:: scrapy_redis.scheduler
    :members:
    :undoc-members:
    :show-inheritance:

scrapy_redis.spiders module
---------------------------

.. automodule:: scrapy_redis.spiders
    :members:
    :undoc-members:
    :show-inheritance:


Module contents
---------------

.. automodule:: scrapy_redis
    :members:
    :undoc-members:
    :show-inheritance:
