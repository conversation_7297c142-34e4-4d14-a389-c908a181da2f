.. highlight:: shell

============
Installation
============


Stable release
--------------

To install Scrapy-Redis, run this command in your terminal:

.. code-block:: console

    pip install scrapy-redis

If you don't have `pip`_ installed, this `Python installation guide`_ can guide
you through the process.

.. _pip: https://pip.pypa.io
.. _Python installation guide: http://docs.python-guide.org/en/latest/starting/installation/


From sources
------------

The sources for Scrapy-Redis can be downloaded from the `Github repo`_.

You can either clone the public repository:

.. code-block:: console

    git clone git://github.com/rolando/scrapy-redis

Or download the `tarball`_:

.. code-block:: console

    curl  -OL https://github.com/rolando/scrapy-redis/tarball/master

Once you have a copy of the source, you can install it with:

.. code-block:: console

    pip install -e .


.. _Github repo: https://github.com/rolando/scrapy-redis
.. _tarball: https://github.com/rolando/scrapy-redis/tarball/master
