language: python
python: 3.5
sudo: false

services:
  - redis-server

env:
  - TOXENV=py27-scrapyrel
  - TOXENV=py34-scrapyrel
  - TOXENV=py35-scrapyrel

matrix:
  fast_finish: true

before_install:
  - python --version
  - uname -a
  - lsb_release -a

# command to install dependencies, e.g. pip install -r requirements.txt --use-mirrors
install: 
  - pip install -U pip wheel
  - pip install -U tox twine coverage
  - virtualenv --version
  - pip --version
  - tox --version

# command to run tests, e.g. python setup.py test
script:
  - tox -e $TOXENV --workdir $HOME/.tox

after_success:
  # Codecov requires a single .coverage and will run 'coverage xml' to
  # generate the report.
  - coverage combine
  - bash <(curl -s https://codecov.io/bash)

after_failure:
  - more $HOME/.tox/log/* | cat
  - more $HOME/.tox/*/log/* | cat

before_cache:
  - rm -fr $HOME/.cache/pip/log
  - rm -fr $HOME/.tox/log/*
  - rm -fr $HOME/.tox/*/log/*

cache:
  directories:
    - $HOME/.cache/pip
    - $HOME/.tox/

notifications:
  email:
    on_sucess: never
    on_failure: always

deploy:
  provider: pypi
  distributions: "sdist bdist_wheel"
  user: darkrho
  password:
    secure: "Pgcj+Otx9o2MxOuXibvz9LUd5DqlW0jaKDScVOAcFT+//U0esjRqY08bRFQlrSTXokJa6X/dVZlb2mQE8L4vr7mLFspRGO4FByK34L089/ETwsLKI2rks2zVbmPSyweL3sz88EXLKmYs7WsKtCnET67qra6hreKbO67ALAh5WWk="
  on:
    tags: true
    all_branches: true
    repo: rolando/scrapy-redis
    condition: "$TOXENV == py35-scrapyrel"
