[tox]
envlist = security,flake8,py{38,39,310,311}-scrapy{26,27,28,29}-redis{42,43,44,45}
minversion = 1.7.0

[base]
deps = 
    scrapy>=2.6
    redis>=4.2
    six>=1.5.2

[testenv]
basepython =
    py38: python3.8
    py39: python3.9
    py310: python3.10
    py311: python3.11
deps = 
    {[base]deps}
    mock
    pytest
    pytest-cov
commands = 
    scrapy26: pip install scrapy==2.6.3
    scrapy27: pip install scrapy==2.7.1
    scrapy28: pip install scrapy==2.8.0
    scrapy29: pip install scrapy==2.9.0
    redis42: pip install redis==4.2.0
    redis43: pip install redis==4.3.6
    redis44: pip install redis==4.4.4
    redis45: pip install redis==4.5.5
    pip install .
    python -m pytest # --cov-report term --cov=scrapy_redis

[testenv:flake8]
basepython = python3.11
deps =
    {[base]deps}
    flake8 # https://github.com/tholo/pytest-flake8/issues/81
commands =
    flake8 --ignore=W503,E265,E731 docs/ tests/

[testenv:security]
basepython = python3.11
deps =
    bandit==1.7.3
commands =
    bandit -r -c .bandit.yml src/ tests/

[testenv:pylint]
basepython = python3.11
deps =
    {[base]deps}
    pylint==2.12.2
commands =
    pylint setup.py docs/ src/ tests/

[testenv:pytest]
basepython = python3.11
deps = 
    {[testenv]deps}
    scrapy==2.6.1
    redis==4.2.2
allowlist_externals = sudo
commands =
    sudo apt-get update
    sudo apt-get install -y redis
    sudo systemctl start redis-server
    pip install .
    python -m pytest --cov-report term --cov=scrapy_redis

[testenv:build]
basepython=python3.11
deps = 
    {[base]deps}
commands = 
    pip install .
