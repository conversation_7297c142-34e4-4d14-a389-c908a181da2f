import asyncio
import concurrent.futures
import time


def run(name):
    t = time.time()
    sum([i * i for i in [i * i for i in range(5000000)]])
    return f"{name}耗时 {time.time() - t}"


async def main():
    t = time.time()
    loop = asyncio.get_event_loop()
    thread_future = loop.run_in_executor(thread_executor, run, 'thread')
    process_future = loop.run_in_executor(process_executor, run, 'process')
    await asyncio.sleep(3)

    await thread_future, process_future
    print(thread_future.result())
    print(process_future.result())

    print(f'运行时间: {time.time() - t}')


if __name__ == '__main__':
    thread_executor = concurrent.futures.ThreadPoolExecutor()
    process_executor = concurrent.futures.ProcessPoolExecutor()
    asyncio.run(main())
