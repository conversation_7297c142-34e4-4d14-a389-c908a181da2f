#%%
def test():
    print("生成器开始")
    i = 1
    while True:
        i += 1
        a = yield i
        print(a)
#%%
a = test()
b = test()
#%%
t = a.send(None)
print('t: ', t)
#%%
next(b)
#%%
t = a.send(4)
print('t: ', t)
#%%

#%%
def accumulate():
    tally = 0
    while 1:
        next_ = yield
        if next_ is None:
            return tally
        tally += next_


def gather_tallies(tallies):
    while 1:
        tally = yield from accumulate()
        tallies.append(tally)
#%%
tallies = []
acc = gather_tallies(tallies)
#%%
next(acc)
#%%
for i in range(4):
    acc.send(i)
#%%
acc.send(None)
#%%
print(tallies)
#%%
for i in range(5):
    acc.send(i)
#%%
acc.send(None)
print(tallies)