import asyncio
import aiofiles


async def write(i):
    async with aiofiles.open(f'test/{i}.txt', 'w+') as f:
        await f.write("hello")


async def read(i):
    async with aiofiles.open(f'test/{i}.txt', 'r+') as f:
        text = await f.read()
        print(text)


async def run():
    write_tasks = [write(i) for i in range(8000)]
    read_tasks = [read(i) for i in range(8000)]

    t1 = loop.time()
    await asyncio.gather(*write_tasks)
    print(f"写入耗时 {loop.time() - t1}")

    t2 = loop.time()
    await asyncio.gather(*read_tasks)
    print(f"读取耗时 {loop.time() - t2}")


if __name__ == '__main__':
    loop = asyncio.get_event_loop()
    loop.run_until_complete(run())
    print("Starting")
