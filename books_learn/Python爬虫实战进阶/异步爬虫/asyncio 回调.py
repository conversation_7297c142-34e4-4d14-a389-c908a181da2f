import asyncio


def run(info, loop=None):
    print(f"{info} {loop.time() if loop else '任务完成回调'}")


async def main():
    loop = asyncio.get_event_loop()
    task = asyncio.Future()
    print(f'开始时间戳 {loop.time()}')

    loop.call_soon(run, '立即调用', loop)
    loop.call_at(loop.time() + 2, run, '指定时间戳下调用', loop)
    loop.call_later(1, run, '延迟调用', loop)

    task.add_done_callback(run)
    task.set_result('任务完成')

    await asyncio.sleep(2)


asyncio.run(main())
