import aiohttp
import asyncio
from aiohttp import ClientSession
from python_asyncio并发编程.util import async_timed


@async_timed()
async def fetch_status(session: ClientSession, url: str) -> int:
    async with session.get(url) as result:
        return result.status


@async_timed()
async def main():
    async with aiohttp.ClientSession() as session:
        url = 'https://www.baidu.com'
        status = await fetch_status(session, url)
        print(f'Status for {url} was {status}')


if __name__ == '__main__':
    # ! 报错 RuntimeError: Event loop is closed windows下 或
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(main())

    # asyncio.get_event_loop().run_until_complete(main())
