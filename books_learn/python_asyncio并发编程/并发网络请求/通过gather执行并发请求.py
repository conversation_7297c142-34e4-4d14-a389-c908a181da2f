import asyncio
import aiohttp
from aiohttp import ClientSession
from python_asyncio并发编程.并发网络请求.使用aiohttp设置超时 import fetch_status
from python_asyncio并发编程.util import async_timed


@async_timed()
async def main():
    async with aiohttp.ClientSession() as session:
        urls = ['https://www.httpbin.org/delay/5' for i in range(50)]
        requests = [fetch_status(session, url) for url in urls]
        status_codes = await asyncio.gather(*requests)
        print(status_codes)
        print(len(status_codes))


asyncio.get_event_loop().run_until_complete(main())
