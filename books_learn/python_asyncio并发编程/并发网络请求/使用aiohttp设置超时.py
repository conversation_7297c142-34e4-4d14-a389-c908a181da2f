import asyncio
import aiohttp
from aiohttp import Client<PERSON>ession
from python_asyncio并发编程.util import async_timed


# @async_timed()
async def fetch_status(session: ClientSession, url: str) -> int:
    ten_millis = aiohttp.ClientTimeout(total=0.5)
    try:
        # async with session.get(url, timeout=ten_millis) as result:
        async with session.get(url) as result:
            return result.status
    except asyncio.TimeoutError as e:
        print("Timeout")


@async_timed()
async def main():
    session_timeout = aiohttp.ClientTimeout(total=1, connect=0.1)
    async with aiohttp.ClientSession(timeout=session_timeout) as session:
        await fetch_status(session, 'https://www.baidu.com')


# asyncio.run(main())
asyncio.get_event_loop().run_until_complete(main())
