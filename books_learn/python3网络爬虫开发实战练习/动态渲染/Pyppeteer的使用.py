import asyncio
from pyppeteer import launch
from pyquery import PyQuery as pq


async def main1():
    browser = await launch()
    page = await browser.newPage()
    await page.goto('https://spa2.scrape.center/')
    await page.waitForSelector('.item .name')
    doc = pq(await page.content())
    names = [item.text() for item in doc('.item .name').items()]
    print('Names:', names)
    await browser.close()


width, height = 1366, 768


async def main2():
    browser = await launch(headless=False)
    page = await browser.newPage()
    await page.setViewport({'width': width, 'height': height})
    await page.goto('https://spa2.scrape.center/')
    await page.waitForSelector('.item .name')
    await asyncio.sleep(2)
    await page.screenshot(path='1.png')
    dimensions = await page.evaluate('''
    () => {
    return {
    width:document.documentElement.clientWidth,
    height:document.documentElement.clientHeight,
    deviceScaleFactor:window.devicePixelRatio,
    }
    }
    ''')

    print(dimensions)
    await browser.close()


asyncio.get_event_loop().run_until_complete(main1())
