import asyncio
from pyppeteer import launch

width, height = 1366, 768


async def main1():
    browser = await launch(headless=False, args=[f'--window-size={width},{height}'])
    page = await browser.newPage()
    await page.setViewport({'width': width, 'height': height})
    await page.goto('https://www.baidu.com/')
    page = await browser.newPage()
    await page.goto('https://www.bing.com/')
    pages = await browser.pages()
    print('pages:', pages)
    page1 = pages[1]
    await page1.bringToFront()
    await asyncio.sleep(1000)


async def main2():
    browser = await launch(headless=False, args=[f'--window-size={width},{height}'])
    page = await browser.newPage()
    await page.setViewport({'width': width, 'height': height})
    await page.goto('https://www.bing.com/')
    await asyncio.sleep(1)
    await page.goto('https://blog.csdn.net/wuShiJingZuo/article/details/114769729')
    # pages = await browser.pages()

    # await page.goBack()
    # await page.goForward()
    # await page.reload()
    # await page.pdf({"path": "example.pdf", "format": 'A4'})
    await page.screenshot({"path":'1.png'})
    # await page.setContent('<h>hello world</h>')
    # await page.setUserAgent('Python')
    # await page.setExtraHTTPHeaders(headers={})
    # await page.close()
    await asyncio.sleep(4)
    await browser.close()


asyncio.get_event_loop().run_until_complete(main2())
