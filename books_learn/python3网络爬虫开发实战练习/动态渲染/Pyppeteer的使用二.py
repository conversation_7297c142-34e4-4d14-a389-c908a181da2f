import asyncio
from pyppeteer import launch

width, height = 1366, 768


async def main():
    browser = await launch(headless=False, args=['--disable-infobars', f'--window-size={width},{height}',
                                                 # "--user-data-dir=./userdata"
                                                 ],
                           # userDataDir='./userdata',
                           # executablePath=r"C:\Program Files\Google\Chrome\Application\chrome.exe"
                           )
    page = await browser.newPage()
    await page.setViewport({'width': width, 'height': height})
    await page.evaluateOnNewDocument('Object.defineProperty(navigator, "webdriver", {get: () => undefined})')
    # await page.goto('https://v.qq.com/')
    await page.goto('https://www.baidu.com/')
    await asyncio.sleep(1000)


async def main2():
    browser = await launch(headless=False, args=['--disable-infobars', f'--window-size = {width}, {height}'])
    context = await browser.createIncogniteBrowserContext()
    page = await context.newPage()
    await page.setViewport({'width': width, 'height': height})
    await page.goto('https://www.baidu.com/')
    await asyncio.sleep(1000)


asyncio.get_event_loop().run_until_complete(main2())
