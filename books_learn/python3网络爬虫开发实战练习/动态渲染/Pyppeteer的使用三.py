import asyncio
from pyppeteer import launch
from pyquery import PyQuery as pq


async def main():
    browser = await launch()
    page = await browser.newPage()
    await page.goto('https://spa2.scrape.center/')
    await page.waitForSelector('.item .name')
    result = await page.J('.item .name')
    result2 = await page.querySelector('.item .name')
    result3 = await page.JJ('.item .name')
    result4 = await page.querySelectorAll('.item .name')
    print('result', result)
    print('result2', result2)
    print('result3', result3)
    print('result4', result4)
    await browser.close()


asyncio.get_event_loop().run_until_complete(main())
