from redis import StrictRedis
from pickle import loads, dumps
from request import MovieRequest

REDIS_KEY = 'antispider5'


class RedisQueue:
    def __init__(self):
        self.db = StrictRedis(host='localhost', port=6379, password=None)

    def add(self, request):
        if isinstance(request, MovieRequest):
            return self.db.rpush(REDIS_KEY, dumps(request))
        return False

    def pop(self):
        if self.db.llen(REDIS_KEY):
            return loads(self.db.lpop(REDIS_KEY))
        return False

    def empty(self):
        return self.db.llen(REDIS_KEY) == 0
