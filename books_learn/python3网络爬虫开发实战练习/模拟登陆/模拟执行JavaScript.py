import execjs
import json
import requests

player = {
    'name': '凯文-杜兰特',
    'image': 'durant.png',
    'birthday': '1988-09-29',
    'height': '208cm',
    'weight': '108.9KG'
}

file = '模拟执行javascript.js'
node = execjs.get()
ctx = node.compile(open(file, 'r', encoding='utf-8').read())

js = f"getToken({json.dumps(player, ensure_ascii=False)})"
print(js)
result = ctx.eval(js)
print(result)

# url = 'http://localhost:3000'
# response = requests.post(url, json=player)
# print(response.text)
