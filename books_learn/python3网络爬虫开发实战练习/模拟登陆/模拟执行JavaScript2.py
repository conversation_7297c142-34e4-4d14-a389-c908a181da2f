from playwright.sync_api import sync_playwright
import time
import requests

url = 'https://spa2.scrape.center/'
index_url = url + '/api/movie/?limit={limit}&offset={offset}&token={token}'
max_page = 10
limit = 10

context = sync_playwright().start()
browser = context.chromium.launch(headless=False)
page = browser.new_page()
page.route(
    "/js/chunk-10192a00.243cb8b7.js",
    lambda route: route.fulfill(path="./temp.js")
)
page.goto(url)


def get_token(offset):
    result = page.evaluate('''() => {
    return window.encrypt("%s","%s")
    }''' % ("/api/movie", offset))
    return result


for i in range(max_page):
    offset = i * limit
    token = get_token(offset)
    index_url = index_url.format(limit=limit, offset=offset, token=token)
    response = requests.get(index_url)
    print('response: ', response.json())

browser.close()
