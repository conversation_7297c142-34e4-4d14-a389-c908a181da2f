import aiohttp
import asyncio


async def main():
    data = {'name': 'jiejie', 'age': 25}
    timeout = aiohttp.ClientTimeout(total=1)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        async with session.post('https://www.httpbin.org/delay/5', json=data) as response:
            # print(await response.text())
            # print(response.text())
            print('status:', response.status)
            # print('headers:', response.headers)
            # print('body:', await response.text())
            # print('bytes:', await response.read())
            # print('json:', await response.json())


if __name__ == '__main__':
    asyncio.get_event_loop().run_until_complete(main())
