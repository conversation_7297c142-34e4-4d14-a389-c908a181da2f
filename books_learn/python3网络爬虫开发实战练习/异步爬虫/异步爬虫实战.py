import json
import aiohttp
import asyncio
import logging
import jsonpath
from motor.motor_asyncio import AsyncIOMotorClient

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s: %(message)s')
index_url = 'https://spa5.scrape.center/api/book/?limit=18&offset={offset}'
detail_url = 'https://spa5.scrape.center/api/book/{id}'
page_size = 18
page_number = 503
concurrency = 5

semaphore = asyncio.Semaphore(concurrency)
session = None

mongo_connect_string = 'mongodb://localhost:27017'
mongo_db_name = 'books'
mongo_collection_name = 'books'

client = AsyncIOMotorClient(mongo_connect_string)
db = client[mongo_db_name]
collection = db[mongo_collection_name]


async def scrape_api(url):
    async with semaphore:
        try:
            logging.info('Scraping %s', url)
            async with session.get(url) as response:
                return await response.json()
        except aiohttp.ClientError as e:
            logging.error('error occurred while scraping %s', url, exc_info=True)


async def scrape_index(page):
    url = index_url.format(offset=page_size * (page - 1))
    return await scrape_api(url)


async def scrape_retail(id):
    url = detail_url.format(id=id)
    data = await scrape_api(url)
    await save(data)


async def save(data):
    logging.info('saving data %s', data)
    if data:
        return await collection.update_one({'id': data.get(id)}, {'$set': data}, upsert=True)


async def main():
    global session
    session = aiohttp.ClientSession()
    tasks = [asyncio.ensure_future(scrape_index(page)) for page in range(1, page_number + 1)]
    results = await asyncio.gather(*tasks)
    logging.info('result %s', json.dumps(results, ensure_ascii=False, indent=2))

    ids = []
    for result in results:
        t = jsonpath.jsonpath(result, '$..id')
        ids.append(t)

    tasks2 = [asyncio.ensure_future(scrape_retail(id)) for id in ids]
    await asyncio.wait(tasks2)
    await session.close()


if __name__ == '__main__':
    asyncio.get_event_loop().run_until_complete(main())
