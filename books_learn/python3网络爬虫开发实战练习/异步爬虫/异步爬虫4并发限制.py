import asyncio
import aiohttp

url = 'https://www.baidu.com/'
concurrency = 5
session = None
semaphore = asyncio.Semaphore(concurrency)


async def get():
    async with semaphore:  # todo ???
        async with session.get(url) as response:
            print(response.status)
            return response.status


async def main():
    global session
    session = aiohttp.ClientSession()
    tasks = [asyncio.ensure_future(get()) for _ in range(10000)]
    await asyncio.gather(*tasks)


if __name__ == '__main__':
    # asyncio.run(main())
    asyncio.get_event_loop().run_until_complete(main())
