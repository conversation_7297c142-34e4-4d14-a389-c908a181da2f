import aiohttp
import asyncio


async def fetch(session, url):
    params = {'name': 'jiejie', 'age': 25}
    async with session.get(url, params=params) as response:
        # print(type(response.text()))
        return await response.text(), response.status


async def main():
    async with aiohttp.ClientSession() as session:
        html, status = await fetch(session, 'https://www.httpbin.org/delay/5')
        print(f'html: {html[:100]}....')
        print(f'status: {status}')


if __name__ == '__main__':
    loop = asyncio.get_event_loop()
    loop.run_until_complete(main())
    # asyncio.run(main())
