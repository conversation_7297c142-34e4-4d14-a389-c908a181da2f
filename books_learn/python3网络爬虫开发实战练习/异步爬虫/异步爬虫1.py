import requests
import asyncio
import time
import aiohttp
import json

start = time.time()


async def get(url):
    session = aiohttp.ClientSession()
    response = await session.get(url)
    await response.text()
    await session.close()
    return response


async def main():
    url = 'https://www.httpbin.org/delay/5'
    # url = 'http://127.0.0.1:5000'
    print('waiting for ', url)
    response = await get(url)
    return response


tasks = [asyncio.ensure_future(main()) for _ in range(10)]
loop = asyncio.get_event_loop()
loop.run_until_complete(asyncio.wait(tasks))

end = time.time()
print('cost time：', end - start)
