import httpx
import asyncio

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36 Edg/100.0.1185.50"
}


# response = httpx.get('https://spa16.scrape.center/', headers=headers)
# print(response.status_code)
# print(response.headers)
# print(response.text)
# client = httpx.Client(http2=True)
# response = client.get('https://spa16.scrape.center/')
# print(response.text)

# with httpx.Client() as client:
#     response = client.get('http://www.httpbin.org/get')
#     print(response.status_code)
#     print(response.http_version)

async def fetch():
    async with httpx.AsyncClient(http2=True) as client:
        response = await client.get('http://www.httpbin.org/get')
        print(response.text)


if __name__ == '__main__':
    asyncio.get_event_loop().run_until_complete(fetch())
