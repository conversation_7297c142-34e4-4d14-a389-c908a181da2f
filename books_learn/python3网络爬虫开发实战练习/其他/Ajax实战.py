import requests
import logging
import json
import pymongo

logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s: %(message)s')

index_url = 'https://spa1.scrape.center/api/movie/?limit={limit}&offset={offset}'
detail_url = 'https://spa1.scrape.center/api/movie/{id}'
total_page = 10
limit = 10

mongo_connection_string = 'mongodb://localhost:27017'
mongo_db_name = 'movies'
mongo_collection_name = 'movies'

client = pymongo.MongoClient(mongo_connection_string)
db = client['movies']
collection = db['movies']


def scrape_api(url):
    logging.info('scraping %s....', url)
    try:
        response = requests.get(url)
        if response.status_code == 200:
            return response.json()
        logging.error('get invalid status code %s while scraping %s', response.status_code, url)
    except requests.RequestException:
        logging.error('error occurred while scraping %s', url, exc_info=True)


def scrap_index(page):
    url = index_url.format(limit=limit, offset=limit * (page - 1))
    return scrape_api(url)


def scrap_detail(id):
    url = detail_url.format(id=id)
    return scrape_api(url)


def save_data(data):
    collection.update_one(
        {'name': data.get('name')},
        {'$set': data}, upsert=True
    )


def main():
    for page in range(1, total_page + 1):
        index_data = scrap_index(page)
        for item in index_data.get('results'):
            id = item.get('id')
            detail_data = scrap_detail(id)
            logging.info('detail data %s', detail_data)
            save_data(detail_data)
            logging.info('data saved successfully')


if __name__ == '__main__':
    main()
