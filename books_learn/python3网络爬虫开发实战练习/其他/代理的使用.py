from urllib.error import URLError
from urllib.request import Proxy<PERSON><PERSON><PERSON>, build_opener

# todo urllib http代理
# proxy = '127.0.0.1:7890'
# # proxy = 'username:password@127.0.0.1:7890'
# proxy_handler = ProxyHandler({
#     'http': 'http://' + proxy,
#     'https': 'http://' + proxy
# })
# opener = build_opener(proxy_handler)
# try:
#     response = opener.open('http://www.httpbin.org/get')
#     print(response.read().decode('utf-8'))
# except URLError as e:
#     print(e.reason)

# todo urllib SOCKS代理
# import socks
# import socket
# from urllib import request
# from urllib.error import URLError
#
# socks.setdefaultproxy(socks.SOCKS5, '127.0.0.1', 7890)
# socket.socket = socks.socksocket
# try:
#     response = request.urlopen('http://www.httpbin.org/get')
#     print(response.read().decode('utf-8'))
# except URLError as e:
#     print(e.reason)


# todo requests代理
import requests

# proxy = '127.0.0.1:7890'
proxy = requests.get('http://**************:5555/random').text.strip()
print(proxy)

proxies = {
    'http': 'http://' + proxy,
    'https': 'http://' + proxy
}
try:
    response = requests.get('http://www.httpbin.org/get', proxies=proxies)
    print(response.text)
except requests.exceptions.ConnectionError as e:
    print('Error', e.args)

# todo requests SOCKS代理
# import requests
#
# # proxy = '127.0.0.1:7890'
# proxy = requests.get('http://**************:5555/random').text.strip()
#
# proxies = {
#     'http': 'socks5://' + proxy,
#     'https': 'socks5://' + proxy
# }
# try:
#     response = requests.get('http://www.httpbin.org/get', proxies=proxies)
#     print(response.text)
# except requests.exceptions.ConnectionError as e:
#     print('Error', e.args)
