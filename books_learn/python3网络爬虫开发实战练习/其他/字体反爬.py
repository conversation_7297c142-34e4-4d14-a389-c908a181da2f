import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait
from pyquery import PyQuery as pq
import requests

url = 'https://antispider4.scrape.center/css/app.654ba59e.css'
response = requests.get(url)
pattern = re.compile(r'.icon-(.*?):before\{content:"(.*?)"}')
results = re.findall(pattern, response.text)
dic = dict(results)


def parse_score(item):
    icon = item('.icon')
    result_score = []
    for i in icon.items():
        t = i.attr('class')
        # print(t)
        s = re.search(r'\d+', t).group()
        # print(s)
        result_score.append(dic[s])
    # print(result_score)
    return ''.join(result_score)


browser = webdriver.Chrome()
browser.get('https://antispider4.scrape.center/')
wait = WebDriverWait(browser, 10).until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, '.item')))
html = browser.page_source
doc = pq(html)
items = doc('.item')
# print(items)
for item in items.items():
    name = item('.name').text()
    categories = [o.text() for o in item('.categories button').items()]
    score = parse_score(item)
    print(f'name: {name}, categories: {categories}, score: {score}')

browser.close()
