from mitmproxy import ctx


def request(flow):
    # flow.request.headers['User-Agent'] = 'MitmProxy'
    # # print(flow.request.headers)
    # ctx.log.info(str(flow.request.headers))
    # ctx.log.warn(str(flow.request.headers))
    # ctx.log.error(str(flow.request.headers))

    # request = flow.request
    # info = ctx.log.info
    # info(request.url)
    # info(str(request.headers))
    # info(str(request.cookies))
    # info(request.host)
    # info(request.method)
    # info(request.port)
    # info(request.scheme)

    response = flow.response
    info = ctx.log.info
    # info(str(response.status_code))
    info(str(response.status_code))
    # warn(str(response))
    # info(str(response.headers))
    # info(str(response.cookies))
    # info(str(response.text))
