import pywasm
import requests
import time

from wasmer import engine, Store, Module, Instance, ImportObject, Function, FunctionType, Type
from wasmer_compiler_cranelift import Compiler

total_page = 10
url = 'https://spa14.scrape.center'

runtime = pywasm.load("./Wasm.wasm")

store = Store(engine.JIT(Compiler))
module = Module(store, open('Wasm.wasm', 'rb').read())
instance = Instance(module)

for i in range(total_page):
    offset = i * 10
    # sign = runtime.exec('encrypt', [offset, int(time.time())])
    sign = instance.exports.encrypt(offset, int(time.time()))
    url = f'{url}/api/movie/?limit=10&offset={offset}&sign={sign}'
    response = requests.get(url)
    print(response.json())
