version: "3"
services:
  redis4proxypool:
    image: redis:alpine
    container_name: redis4proxypool
    ports:
      - "6374:6379"
  proxypool:
    build: .
    image: "germey/proxypool:master"
    container_name: proxypool
    ports:
      - "5555:5555"
    restart: always
    #    volumes:
    #      - proxypool/crawlers/private:/app/proxypool/crawlers/private
    environment:
      PROXYPOOL_REDIS_CONNECTION_STRING: redis://@redis4proxypool:6379/0
