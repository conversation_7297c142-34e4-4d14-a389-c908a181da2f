#!/usr/bin/env python3
"""
Switch Proxy Plugin - 代理切换客户端类
提供代理切换功能的封装类，支持复用连接
"""

import asyncio
import websockets
import json
import sys
from loguru import logger
from typing import Optional, Dict, Tuple

# 配置loguru日志
logger.remove()  # 移除默认处理器
logger.add(
    sys.stdout,
    format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
    level="INFO"
)

class ProxySwitchClient:
    """代理切换客户端类"""

    def __init__(self, server_url: str = "ws://localhost:8765", timeout: int = 10):
        """
        初始化代理切换客户端

        Args:
            server_url: WebSocket服务器地址
            timeout: 操作超时时间（秒）
        """
        self.server_url = server_url
        self.timeout = timeout
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self.connected = False

    async def connect(self) -> bool:
        """
        连接到WebSocket服务器

        Returns:
            bool: 连接是否成功
        """
        try:
            logger.info(f"正在连接到代理服务器: {self.server_url}")
            self.websocket = await asyncio.wait_for(
                websockets.connect(self.server_url),
                timeout=self.timeout
            )
            self.connected = True
            logger.success("连接成功")

            # 接收欢迎消息
            welcome_msg = await self.websocket.recv()
            welcome_data = json.loads(welcome_msg)
            logger.info(f"收到欢迎消息: {welcome_data['type']}")

            return True

        except asyncio.TimeoutError:
            logger.error("连接超时")
            return False
        except ConnectionRefusedError:
            logger.error("连接被拒绝，请确保proxy_server.py正在运行")
            return False
        except Exception as e:
            logger.error(f"连接失败: {e}")
            return False

    async def disconnect(self):
        """断开连接"""
        if self.websocket and self.connected:
            try:
                await self.websocket.close()
                self.connected = False
                logger.info("连接已断开")
            except Exception as e:
                logger.warning(f"断开连接时出错: {e}")

    async def send_message(self, message_type: str, data: Dict = None) -> bool:
        """
        发送消息到服务器

        Args:
            message_type: 消息类型
            data: 消息数据

        Returns:
            bool: 发送是否成功
        """
        if not self.websocket or not self.connected:
            logger.error("未连接到服务器")
            return False

        message = {
            "type": message_type,
            "data": data or {}
        }

        try:
            await self.websocket.send(json.dumps(message))
            logger.debug(f"已发送消息: {message_type}")
            return True
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            return False

    async def wait_for_response(self, expected_type: str) -> Optional[Dict]:
        """
        等待特定类型的响应消息

        Args:
            expected_type: 期望的消息类型

        Returns:
            Optional[Dict]: 响应数据，失败返回None
        """
        start_time = asyncio.get_event_loop().time()

        while asyncio.get_event_loop().time() - start_time < self.timeout:
            try:
                response = await asyncio.wait_for(self.websocket.recv(), timeout=1)
                data = json.loads(response)

                logger.debug(f"收到消息: {data['type']}")

                if data['type'] == expected_type:
                    return data.get('data', {})

            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"接收消息时出错: {e}")
                break

        logger.warning(f"等待 {expected_type} 响应超时")
        return None

    async def switch_proxy(self, host: str, port: int, scheme: str = "http") -> Tuple[bool, str]:
        """
        切换代理设置（主要对外接口）

        Args:
            host: 代理主机地址
            port: 代理端口
            scheme: 代理协议

        Returns:
            Tuple[bool, str]: (是否成功, 状态消息)
        """
        try:
            # 1. 检查连接状态
            if not self.connected:
                return False, "未连接到服务器"

            # 2. 发送切换命令
            logger.info(f"正在切换代理到: {scheme}://{host}:{port}")
            proxy_data = {
                "host": host,
                "port": port,
                "scheme": scheme
            }

            if not await self.send_message("switch_proxy", proxy_data):
                return False, "发送切换命令失败"

            # 3. 等待响应
            logger.info("等待Chrome插件响应...")
            response = await self.wait_for_response("proxy_result")

            if not response:
                return False, "等待响应超时"

            # 4. 处理响应结果
            if response.get("success"):
                proxy_info = response.get("proxy", {})
                success_msg = f"代理切换成功: {proxy_info['scheme']}://{proxy_info['host']}:{proxy_info['port']}"
                logger.success(success_msg)
                return True, success_msg
            else:
                error_msg = f"代理切换失败: {response.get('error', '未知错误')}"
                logger.error(error_msg)
                return False, error_msg

        except Exception as e:
            error_msg = f"切换过程出错: {e}"
            logger.error(error_msg)
            return False, error_msg


async def main():
    """主函数 - 演示代理切换功能"""
    print("🚀 Switch Proxy Plugin - 代理切换客户端")
    print("=" * 50)
    print()
    print("⚠️ 使用前请确保:")
    print("1. proxy_server.py 正在运行")
    print("2. Chrome插件已安装并启用")
    print("3. Chrome浏览器已打开")
    print()

    # 创建代理切换客户端
    client = ProxySwitchClient()

    try:
        # 1. 连接到服务器
        logger.info("步骤 1: 连接到WebSocket服务器")
        if not await client.connect():
            logger.error("无法连接到服务器，程序退出")
            return

        # 2. 执行代理切换
        logger.info("步骤 2: 执行代理切换")
        success, message = await client.switch_proxy("127.0.0.1", 6152, "http")

        if success:
            print()
            print("🎉 代理切换成功!")
            print(f"📍 {message}")
            print()
            print("💡 现在可以在Python自动化脚本中使用此类:")
            print("   client = ProxySwitchClient()")
            print("   await client.connect()")
            print("   success, msg = await client.switch_proxy('127.0.0.1', 6152)")
            print("   await client.disconnect()")
        else:
            print()
            print("❌ 代理切换失败!")
            print(f"📍 {message}")
            print()
            print("💡 请检查:")
            print("   - Chrome插件是否正确安装并启用")
            print("   - 代理服务器 127.0.0.1:6152 是否可用")
            print("   - 查看Chrome控制台错误信息")

    except KeyboardInterrupt:
        logger.warning("操作被用户中断")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
    finally:
        # 3. 断开连接
        logger.info("步骤 3: 断开连接")
        await client.disconnect()
        print()
        print("🔚 程序结束")


if __name__ == "__main__":
    asyncio.run(main())
