#!/usr/bin/env python3
"""
Switch Proxy Plugin - Python WebSocket Server
与Chrome插件通信，控制代理切换
"""

import asyncio
import websockets
import json
from loguru import logger
from typing import Optional, Dict, Any
import signal
import sys

# 配置loguru日志
logger.remove()  # 移除默认处理器
logger.add(
    sys.stdout,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    level="INFO"
)


class ProxyServer:
    def __init__(self, host: str = "localhost", port: int = 8765):
        self.host = host
        self.port = port
        self.clients = set()
        self.chrome_clients = set()  # Chrome插件客户端
        self.script_clients = set()  # Python脚本客户端
        self.current_proxy = None
        self.server = None

    async def register_client(self, websocket):
        """注册新的客户端连接"""
        self.clients.add(websocket)
        client_info = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        logger.info(f"客户端已连接: {client_info} (总连接数: {len(self.clients)})")

        # 发送欢迎消息
        await self.send_message(websocket, {
            "type": "welcome",
            "data": {
                "message": "已连接到代理服务器",
                "server_version": "1.0",
                "client_count": len(self.clients)
            }
        })

    async def unregister_client(self, websocket):
        """注销客户端连接"""
        self.clients.discard(websocket)
        self.chrome_clients.discard(websocket)
        self.script_clients.discard(websocket)
        try:
            client_info = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
            logger.info(f"客户端已断开: {client_info} (剩余连接数: {len(self.clients)})")
        except:
            logger.info(f"客户端已断开 (剩余连接数: {len(self.clients)})")

    async def send_message(self, websocket, message: Dict[str, Any]):
        """发送消息到客户端"""
        try:
            await websocket.send(json.dumps(message, ensure_ascii=False))
        except websockets.exceptions.ConnectionClosed:
            logger.warning("尝试向已关闭的连接发送消息")
        except Exception as e:
            logger.error(f"发送消息失败: {e}")

    async def broadcast_message(self, message: Dict[str, Any]):
        """广播消息到所有客户端"""
        if self.clients:
            await asyncio.gather(
                *[self.send_message(client, message)
                  for client in self.clients],
                return_exceptions=True
            )

    async def broadcast_to_chrome_clients(self, message: Dict[str, Any]):
        """广播消息到Chrome插件客户端"""
        if self.chrome_clients:
            await asyncio.gather(
                *[self.send_message(client, message)
                  for client in self.chrome_clients],
                return_exceptions=True
            )

    async def broadcast_to_script_clients(self, message: Dict[str, Any]):
        """广播消息到Python脚本客户端"""
        if self.script_clients:
            await asyncio.gather(
                *[self.send_message(client, message)
                  for client in self.script_clients],
                return_exceptions=True
            )

    async def handle_client_message(self, websocket, message_data: str):
        """处理来自客户端的消息"""
        try:
            message = json.loads(message_data)
            message_type = message.get("type")
            data = message.get("data", {})

            if message_type == "ping":
                # 处理心跳包
                await self.send_message(websocket, {
                    "type": "pong",
                    "data": {"timestamp": data.get("timestamp")}
                })
                return

            logger.info(f"收到消息类型: {message_type}")

            # 识别客户端类型并处理消息
            if message_type == "status":
                # Chrome插件发送的状态消息，标识为Chrome客户端
                self.chrome_clients.add(websocket)
                logger.info(f"Chrome插件状态: {data}")

            elif message_type == "switch_proxy":
                # Python脚本发送的切换命令，标识为脚本客户端
                self.script_clients.add(websocket)
                logger.info(f"转发代理切换命令到Chrome插件: {data}")
                # 只转发给Chrome插件
                await self.broadcast_to_chrome_clients(message)
                return

            elif message_type == "clear_proxy":
                # Python脚本发送的清除命令
                self.script_clients.add(websocket)
                logger.info("转发清除代理命令到Chrome插件")
                await self.broadcast_to_chrome_clients(message)
                return

            elif message_type == "get_status":
                # Python脚本发送的状态查询
                self.script_clients.add(websocket)
                logger.info("转发状态查询命令到Chrome插件")
                await self.broadcast_to_chrome_clients(message)
                return

            elif message_type == "proxy_result":
                # Chrome插件的代理设置结果，转发给Python脚本
                if data.get("success"):
                    self.current_proxy = data.get("proxy")
                    logger.info(f"代理设置成功: {self.current_proxy}")
                else:
                    logger.error(f"代理设置失败: {data.get('error')}")

                # 转发结果给Python脚本客户端
                logger.info("转发代理结果到Python脚本")
                await self.broadcast_to_script_clients(message)
                return

            elif message_type == "status_response":
                # Chrome插件的状态响应，转发给Python脚本
                logger.info(f"Chrome插件状态响应: {data}")
                await self.broadcast_to_script_clients(message)
                return

            else:
                logger.warning(f"未知消息类型: {message_type}")

        except json.JSONDecodeError:
            logger.error("无法解析JSON消息")
        except Exception as e:
            logger.error(f"处理消息时出错: {e}")

    async def switch_proxy(self, host: str, port: int, scheme: str = "http"):
        """切换代理设置"""
        proxy_data = {
            "host": host,
            "port": port,
            "scheme": scheme
        }

        message = {
            "type": "switch_proxy",
            "data": proxy_data
        }

        logger.info(f"切换代理: {proxy_data}")
        await self.broadcast_message(message)

        return proxy_data

    async def clear_proxy(self):
        """清除代理设置"""
        message = {
            "type": "clear_proxy",
            "data": {}
        }

        logger.info("清除代理设置")
        await self.broadcast_message(message)

    async def get_status(self):
        """获取当前状态"""
        message = {
            "type": "get_status",
            "data": {}
        }

        logger.info("请求状态更新")
        await self.broadcast_message(message)

    async def handle_client(self, websocket):
        """处理客户端连接"""
        await self.register_client(websocket)

        try:
            async for message in websocket:
                await self.handle_client_message(websocket, message)
        except websockets.exceptions.ConnectionClosed:
            logger.info("客户端连接已关闭")
        except Exception as e:
            logger.error(f"处理客户端时出错: {e}")
        finally:
            await self.unregister_client(websocket)

    async def start_server(self):
        """启动WebSocket服务器"""
        logger.info(f"启动代理服务器: ws://{self.host}:{self.port}")

        self.server = await websockets.serve(
            self.handle_client,
            self.host,
            self.port,
            ping_interval=20,  # 每20秒发送ping
            ping_timeout=10,  # ping超时时间10秒
            close_timeout=10  # 关闭超时时间10秒
        )

        logger.info("代理服务器已启动，等待Chrome插件连接...")
        return self.server

    async def stop_server(self):
        """停止服务器"""
        if self.server:
            self.server.close()
            await self.server.wait_closed()
            logger.info("服务器已停止")


# 全局服务器实例
proxy_server = ProxyServer()


async def main():
    """主函数"""
    # 启动服务器
    server = await proxy_server.start_server()

    # 设置信号处理
    def signal_handler():
        logger.info("收到停止信号，正在关闭服务器...")
        asyncio.create_task(proxy_server.stop_server())

    # 注册信号处理器
    if sys.platform != "win32":
        loop = asyncio.get_running_loop()
        for sig in (signal.SIGTERM, signal.SIGINT):
            loop.add_signal_handler(sig, signal_handler)

    try:
        # 保持服务器运行
        await server.wait_closed()
    except KeyboardInterrupt:
        logger.info("收到键盘中断，正在关闭服务器...")
    finally:
        await proxy_server.stop_server()


# 便捷函数供外部调用
async def switch_proxy(host: str, port: int, scheme: str = "http"):
    """切换代理 - 外部调用接口"""
    return await proxy_server.switch_proxy(host, port, scheme)


async def clear_proxy():
    """清除代理 - 外部调用接口"""
    await proxy_server.clear_proxy()


async def get_status():
    """获取状态 - 外部调用接口"""
    await proxy_server.get_status()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序已退出")
