import numpy as np
from sklearn.linear_model import LinearRegression
import matplotlib.pyplot as plt

x1 = 2 * np.random.rand(100, 1)
x2 = 2 * np.random.rand(100, 1)
x = np.c_[x1, x2]

y = 4 + 3 * x1 + 5 * x2 + np.random.randn(100, 1)

reg = LinearRegression()
reg.fit(x, y)
print(reg.intercept_, reg.coef_)

x_new = np.array([
    [0, 0],
    [2, 1],
    [2, 4]
])
y_predict = reg.predict(x_new)

plt.plot(x_new[:, 0], y_predict, 'r-')
plt.plot(x1, y, 'b.')
plt.axis([0, 2, 0, 25])
plt.show()
