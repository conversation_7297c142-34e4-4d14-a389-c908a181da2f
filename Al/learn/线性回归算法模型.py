import numpy as np
import matplotlib.pyplot as plt

np.random.seed(42)
x = 2 * np.random.rand(100, 1)

y = 5 + 4 * x + np.random.randn(100, 1)

x_b = np.c_[np.ones((100, 1)), x]

a = np.linalg.inv(x_b.T.dot(x_b)).dot(x_b.T).dot(y)

print(a)

x_new = np.array([[0], [2]])
x_new_b = np.c_[np.ones((2, 1)), x_new]
print('x_new_b: ', x_new_b)
y_predict = x_new_b.dot(a)
print('y_predict:', y_predict)

plt.plot(x_new, y_predict, 'r-')
plt.plot(x, y, 'b.')
plt.axis([0, 2, 0, 15])
plt.show()
