#%%
import pandas as pd
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.neighbors import KNeighborsClassifier
#%%
facebook = pd.read_csv(r"D:\temp\02.课件\train.csv")
print(facebook)
#%%
# 2.1 缩小数据范围
facebook_data = facebook.query("x>2.0 & x<2.5 & y>2.0 & y<2.5")
# 2.2 选择时间特征
time = pd.to_datetime(facebook_data["time"],unit='s')
# print(time)
time = pd.DatetimeIndex(time)
# print(time)
facebook_data["day"] = time.day
facebook_data["hour"] = time.hour
facebook_data["weekday"] = time.weekday
# 2.3 去掉签到较少的地方
place_count = facebook_data.groupby('place_id').count()
# print(place_count)
place_count = place_count[place_count['row_id']>3]
facebook_data = facebook_data[facebook_data['place_id'].isin(place_count.index)]
# 2.4 确定特征值和目标值
x = facebook_data[['x','y','accuracy','day','weekday','hour']]
y = facebook_data['place_id']
# 2.5 分割数据集
x_train,x_test,y_train,y_test = train_test_split(x,y,random_state=22)
#%%
# 3.特征工程 -- 特征预处理(标准化)
transfer = StandardScaler()
x_train = transfer.fit_transform(x_train)
x_test = transfer.transform(x_test)
# 4.机器学习 -- knn+cv
estimator = KNeighborsClassifier()
param_grid = {'n_neighbors':[1,3,5,7,9]}
estimator = GridSearchCV(estimator,param_grid=param_grid,cv=5)
estimator.fit(x_train,y_train)

# 5.模型评估
# 5.1 基本评估方式
score = estimator.score(x_test, y_test)
print("最后预测的准确率为:\n", score)

y_predict = estimator.predict(x_test)
print("最后的预测值为:\n", y_predict)
print("预测值和真实值的对比情况:\n", y_predict == y_test)

# 5.2 使用交叉验证后的评估方式
print("在交叉验证中验证的最好结果:\n", estimator.best_score_)
print("最好的参数模型:\n", estimator.best_estimator_)
print("每次交叉验证后的验证集准确率结果和训练集准确率结果:\n",estimator.cv_results_)
#%%
