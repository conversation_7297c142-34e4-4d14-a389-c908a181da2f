from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.neighbors import KNeighborsClassifier

iris = load_iris()

x_train, x_test, y_train, y_test = train_test_split(iris.data, iris.target, test_size=0.2, random_state=22)
transfer = StandardScaler()
x_train = transfer.fit_transform(x_train)
x_test = transfer.transform(x_test)

estimator = KNeighborsClassifier()

param_grid = {"n_neighbors": [1, 3, 5]}
estimator = GridSearchCV(estimator, param_grid=param_grid, cv=3)

estimator.fit(x_train, y_train)

y_predict = estimator.predict(x_test)
print('y_predict:', y_predict)
print("比对真实值和预测值：\n", y_predict == y_test)

score = estimator.score(x_test, y_test)
print("准确率：", score)

print("在交叉验证中验证的最好结果：\n", estimator.best_score_)
print("最好的参数模型：\n", estimator.best_estimator_)
print("每次交叉验证后的准确率结果：\n", estimator.cv_results_)
