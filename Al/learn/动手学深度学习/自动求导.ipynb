#%%
import torch
#%%
x = torch.arange(4.0)
x
#%%
y = 2 * torch.dot(x,x)
y
#%%
x.requires_grad_(True)
x.grad
#%%
x.backward()
x.grad == 4 * x
#%%
import torch

x = torch.arange(4.0)
x
#%%
x.requires_grad_(True)
x.grad
#%%
y = 2 * torch.dot(x,x)
y
#%%
y.backward()
x.grad
#%%
x.grad == 4 * x
#%%
x.grad.zero_()
#%%
y = x.sum()
y.backward()
x.grad
#%%
x.grad.zero_()
#%%
y = x * x
y.sum().backward()
x.grad
#%%

#%%
