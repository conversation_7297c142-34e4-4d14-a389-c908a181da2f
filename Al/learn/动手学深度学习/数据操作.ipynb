#%%
import torch
#%%
x = torch.arange(12)
x
#%%
x.shape
#%%
x.reshape(3, 4)
#%%
torch.zeros((2, 3, 4))
#%%
torch.ones((2, 3, 4))
#%%
torch.tensor([[2, 3, 4, 5], [7, 7, 8, 5]])
#%%
torch.tensor([[[2, 3, 4, 5], [7, 7, 8, 5]]]).shape
#%%
x = torch.tensor([1.0, 2, 4, 8])
y = torch.tensor([2, 2, 2, 2])
#%%
x + y, x - y, x / y, x ** y
#%%
torch.exp(x)
#%%
x = torch.arange(12, dtype=torch.float32).reshape((3, 4))
y = torch.tensor([[2.0, 1, 4, 3], [1, 2, 3, 4], [4, 3, 2, 1]])
#%%
torch.cat((x, y), dim=0)
#%%
torch.cat((x, y), dim=1)
#%%
x == y
#%%
x.sum()
#%%
a = torch.arange(3).reshape((3, 1))
b = torch.arange(2).reshape((1, 2))
a, b
#%%
a + b
#%%
x[-1]
#%%
x
#%%
x[-1]
#%%
x[1:3]
#%%
x[1, 2] = 9
x
#%%
x[0:2, :] = 12
x
#%%
before = id(y)
y = y + x
id(y) == before
#%%
z = torch.zeros_like(y)
print('id(z):', id(z))
z[:] = x + y
print('id(z):', id(z))
#%%
before = id(x)
x += y
id(x) == before
#%%
A = x.numpy()
B = torch.tensor(A)
type(A), type(B)
#%%
a = torch.tensor([3.5])
a, a.item(), float(a), int(a)
#%%
import os

os.makedirs(os.path.join('..', 'data'), exist_ok=True)
data_file = os.path.join('..', 'data', 'house_tiny.csv')
with open(data_file, 'w') as f:
    f.write('NumRooms,Alley,price\n')
    f.write('NA,Pave,127500\n')
    f.write('2,NA,106000\n')
    f.write('4,NA,178100\n')
    f.write('NA,NA,140000\n')
#%%
import pandas as pd

data = pd.read_csv(data_file)
print(data)
data
#%%
inputs, outputs = data.iloc[:, 0:2], data.iloc[:, 2]
inputs = inputs.fillna(inputs.mean())
print(inputs)
#%%
inputs = pd.get_dummies(inputs, dummy_na=True)
print(inputs)
#%%
x, y = torch.tensor(inputs.values),torch.tensor(outputs.values)
x, y
#%%
