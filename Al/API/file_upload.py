from pathlib import Path

from openai import OpenAI

client = OpenAI()  # 会自动从环境变量读取 API Key

# 1. 上传本地 PDF 文件
#
# purpose 若要与 Assistants/Retrieval 功能结合，写 "assistants"
# 若只是给 chat/completions 做一次性引用，也写 "assistants"
upload = client.files.create(
    file=Path("demo.pdf"),  # 本地 pdf 路径
    purpose="assistants"  # 目前所有检索/引用场景都用 assistants
)

file_id = upload.id  # 形如 "file-abc123..."
print("Uploaded file_id:", file_id)

# 2. 在 chat/completions 把文件作为附件引用
#
# content 可以是纯文本，也可以是「文本块 + 文件块」的列表。
# 文件块结构：{"type": "file", "file_id": "..."}
#
# 下面例子让 GPT-4o 从 PDF 中找一段文字并回答。
response = client.chat.completions.create(
    model="gpt-4o-mini",  # 支持文件引用的模型：gpt-4o-* / gpt-4o
    messages=[
        {
            "role": "user",
            "content": [
                {"type": "text",
                 "text": "请阅读这份 PDF，并用中文总结文档的核心观点："},
                {"type": "file", "file_id": file_id}
            ]
        }
    ]
)

print(response.choices[0].message.content)
