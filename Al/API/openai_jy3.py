import time
from openai import OpenAI
import base64
from loguru import logger
import re
import json
from pydantic import BaseModel
from textwrap import dedent
import time
import os

text1 = '''
Role: 图像物体识别助手 
Task: 
    - 九宫格识别任务，识别出整个九宫格中与白色背景小图（九宫格外）相似的所有小图，返回小图坐标（以左上角的小图为[0,0]的图像坐标系）
要求：
    - 不使用 Python。只返回json，格式：{'(识别图片是什么物体，用中文)':[[x,y],···]}
'''

prompt = '''
Role: 图片物体种类识别专家
Task: 
    - 只识别出整个九宫格中与白色背景小图（九宫格外，左下角）物体种类相同的所有小图，返回小图位置（左上角编号为1，从左往右、从上往下编号 ）列表
要求：
    - 不使用 Python
    - 严格按照给定的结构格式返回
    - ‘object_name’使用中文名称,且必须是白色背景小图的种类
'''

prompt3 = '''
Role: 图片物体种类识别专家
Task: 
    - 1.识别出九宫格中九张小图的物体种类
    - 2.识别出白色背景小图（九宫格外，左下角）的物体种类
    - 3.找出任务1中与任务2种类相同的所有小图
    - 4.返回所有小图位置列表（左上角编号为1，从左往右、从上往下编号）
要求：
    - 不使用 Python
    - 严格按照给定的结构格式返回
    - ‘object_name’使用中文名称
'''
# ,且必须是白色背景小图的种类 ，只能返回 json 数据

prompt2 = '''
Role: 图片物体种类识别专家
Task: 
    - 识别出整个九宫格中与第二张图片相似的所有小图，返回小图位置列表（左上角编号为1，从左往右、从上往下编号 ）
要求：
    - 不使用 Python
    - 严格按照给定的结构格式返回
    - ‘object_name’使用中文名称,且必须是白色背景小图的种类
'''

client = OpenAI(
    api_key="sk-DNKfr634BCNP2GO5AfD73030070448A9A09390A8A82f7c17",
    base_url="https://aihubmix.com/v1",
    # api_key="sk-KcLIy8ODpnnsaBaHB8D8BcD290B84077A541A91c2aD0A448",
    # base_url="https://api.ocoolai.com/v1"
    # api_key="sk-DCdBe9c1o2Q3mpVc232279Bb617846D6B7CdBb3e847f9682",
    # base_url="https://api.oaibest.com/v1"
)


class Info(BaseModel):
    object_name: str
    positions: list[int]


def encode_image(image_path):
    """
    读取本地图片并编码为Base64字符串。
    """
    with open(image_path, "rb") as image_file:
        encoded_string = base64.b64encode(image_file.read()).decode("utf-8")
    return encoded_string


def openai_api(path, path2=''):
    base64_image = encode_image(path)
    # base64_image2 = encode_image(path2)
    a = time.time()
    response = client.beta.chat.completions.parse(
        messages=[  # type: ignore
            # {"role": "system", "content": dedent(prompt2_)},
            {"role": "user", "content": [
                {'text': dedent(prompt3), 'type': 'text'},
                {"image_url": {"url": f"data:image/png;base64,{base64_image}"}, "type": "image_url"},
                # {"image_url": {"url": f"data:image/png;base64,{base64_image2}"}, "type": "image_url"}
            ]}
        ],
        # model='chatgpt-4o-latest',
        # model='gpt-4o',
        # model='gpt-4o-2024-05-13',
        model='gpt-4o-2024-11-20',
        # model='gpt-4o-2024-08-06',

        # model='grok-2-vision-1212',
        temperature=0,
        top_p=1,
        response_format=Info
    )

    _ = response.choices[0].message.parsed
    logger.info(f'{os.path.basename(path)} {round(time.time() - a, 2)}: {_.model_dump()}')
    return _.model_dump()


if __name__ == '__main__':
    for dirpath, dirnames, filenames, in os.walk('/Users/<USER>/Downloads/test'):
        for filename in filenames:
            if filename.endswith('.jpg'):
                filepath = os.path.join(dirpath, filename)
                openai_api(filepath)
                # print(filepath)
