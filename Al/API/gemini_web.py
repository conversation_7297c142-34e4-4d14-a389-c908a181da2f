import asyncio
from gemini_webapi import GeminiClient

# Replace "COOKIE VALUE HERE" with your actual cookie values.
# Leave Secure_1PSIDTS empty if it's not available for your account.
Secure_1PSID = "g.a000oQiOQ4FiheKgl-cykwNt8Thp_gMSx8GUPoY1NtpX-jEY_glzUqhdITuWCATzTJ4gJxolrAACgYKAVYSARESFQHGX2MiiIErrsvHlYp69ngAhTZk-BoVAUF8yKop2jnAwM5rPlhHpm5gG6eA0076"
Secure_1PSIDTS = "sidts-CjIBQlrA-HC7QU0B0OvGSB4IeufOqaoZjgn2P9Wlty6vBZraBN1LNuLKp12TnscwRp0_ThAA"

with open(r"C:\Users\<USER>\Downloads\1.jpg", "rb") as f:
    file = f.read()

str_ = '先判断这张图片是不是 包含或主题是 Particulars of Member(s)of a Non-listed Company (Section 13)，如果是 识别出 Class of Shares、Total Number of Issued Shares in this Class、Name、Address、Current Holding、Remarks对应的值，返回给我json数据，其中Name、Address、Current Holding，如果有多个值，该字段对应的值是一个由多个值组成的列表。注意Name字段识别出所有文字包括繁体中文和英文，注意不能转为简体中文!! 数据中不能含有换行符，直接返回json数据，不需要其他过多解释等，直接返回例如：{"Class of Shares": "Ordinary","Total Number of Issued Shares in this Class": 100000,"Name":["Wu Ru Yin吳如銀"],"Address":[],"Current Holding": [],"Remarks":[]}'


async def main():
    # If browser-cookie3 is installed, simply use `client = GeminiClient()`
    proxies = {
        "http://": "http://127.0.0.1:10809",
        "https://": "http://127.0.0.1:10809",
        # "http://": "http://127.0.0.1:7890",
        # "https://": "http://127.0.0.1:7890",
    }
    # proxies = None
    client = GeminiClient(Secure_1PSID, Secure_1PSIDTS, proxies=proxies)
    await client.init(timeout=30, auto_close=False, close_delay=300, auto_refresh=True)
    response = await client.generate_content(
        str_,
        images=[file],
    )
    print(response.text)


asyncio.run(main())
