# -*- coding: utf-8 -*-
import google.generativeai as genai
import PIL.Image
import typing_extensions
import os
import json
from loguru import logger
import time

# os.environ["HTTP_PROXY"] = "http://127.0.0.1:7890"
# os.environ["HTTP_PROXYS"] = "http://127.0.0.1:7890"

genai.configure(api_key='AIzaSyAK95Ae0l3R_Xts_ZoE9oD_0guLQxZhnuI')  # 填入自己的api_key

# 查询模型
# for m in genai.list_models():
#     print(m.name)
#     print(m.supported_generation_methods)

# model = genai.GenerativeModel('gemini-1.5-pro')
# model = genai.GenerativeModel('gemini-1.5-pro-latest')
# model = genai.GenerativeModel('gemini-1.5-flash-latest')
# model = genai.GenerativeModel('gemini-pro-vision')
# model = genai.GenerativeModel('gemini-pro-vision')
model = genai.GenerativeModel('gemini-1.5-flash', generation_config=genai.GenerationConfig(temperature=0.3))


def upload_file(path):
    # https://generativelanguage.googleapis.com/v1beta/files/bmpe9885g2m0
    file = genai.upload_file(path=path, display_name='22202728675')
    print(f"Uploaded file '{file.display_name}' as: {file.uri}")
    return file.uri.split('/')[-1]


def str_file(path):
    name = upload_file(path)
    file = genai.get_file(name=name)
    str_ = '''你的任务角色是PDF信息提取，请根据pdf文件完成以下三个任务并严格遵守注意事项，生成json数据。
任务一：识别出第一页“Date to which this Return is Made Up”或“Date of this Return”对应的日期，格式为：YYYY-MM-DD，对应的字段名为return_date。
任务二：找出标题是Share Capital的pdf页面，识别出 [Class of Shares、Currency、Total Number、Total Amount、Total Amount Paid up or Regarded as Paid up] 对应的值。依次对应的字段名为 [shares_class、currency、issued_total_number、issued_total_amount、issued_total_amount_paid]
任务三：在附表中找出标题是“（非）上市公司的成員詳情”的一个或多个pdf页面，一种情况是表格数据，另一种情况是以1开头对应的一段文本内容，这种情况特殊是No.of Shares Held表示了Current Holding。
       1、识别出 Total Number of Issued Shares in this Class
       2、识别出 [Name、Address、Current Holding、Remarks、Class of Shares] 对应的值 依次对应的字段名为 [shareholder_name、address、current_holding、remarks、shares_class]
注意事项：
1、任务二和任务三中，识别出表示数值的字段值时，只保留数字部分，例如去除逗号等其他符号，除了小数点。
2、任务二中，每条数据所有字段必须有值，则表示该条数据无效，忽略掉
3、任务三中，一条数据中Name、Address、Current Holding，这三个字段必须都有值，否则无效，忽略掉
4、任务三中，一条数据中Current Holding字段值为"(Nil)"，则无效，略掉掉
5、任务三中，Remarks如果该字段没有对应的值则用“-”代替
6、任务三中，Name字段识别出所有文字包括繁体中文、英文、简体中文，注意繁体中文不能转为简体中文！
7、任务三中，字段对应的值大小写需要与原PDF一种
8、任务三中，数据中不能含有换行符
输出内容要严格遵循给出的json数据样例：
e.g.
{
  "return_date": "2024-06-30",
  "Total Number of Issued Shares in this Class": "12370150983",
  "hk_annual_capital": [
    {
      "shares_class": "普通股",
      "currency": "HKD",
      "issued_total_number": "12370150983",
      "issued_total_amount": "32189017317",
      "issued_total_amount_paid": "32189017317"
    }
  ],
  "hk_annual_shareholders": [
    {
      "shareholder_name": "China Power Development Limited",
      "address": "Suite 6301, 63/F. Central Plaza 18 Harbour Road Wanchai Hong Kong",
      "current_holding": "2662000000",
      "remarks": "-",
      "shares_class": "Ordinary"
    }
  ]
}
e.g. 任务二或任务三没有数据
{
  "return_date": "2024-06-30",
  "hk_annual_capital": [],
  "hk_annual_shareholders": []
}'''
    print(f"Retrieved file '{file.display_name}' as: {file.uri}")
    response = model.generate_content([file, str_])
    print(response.text)
    return response.text[8:-4]


if __name__ == '__main__':
    str_file(r"C:\Users\<USER>\Downloads\hk_pdf_test\20777231\70003705571.pdf")
    pass
    # for path, dirnames, filenames in os.walk(r'C:\Users\<USER>\Downloads\hk_pdf_test'):
    #     if filenames:
    #         pdf_path = os.path.join(path, filenames[1])
    #         print(pdf_path)
    #         try:
    #             data = json.loads(str_file(pdf_path))
    #         except Exception as e:
    #             logger.error(f'{pdf_path} {e}')
    #             continue
    #         with open(os.path.join(path, 'gemini.json'), 'w') as f:
    #             f.write(json.dumps(data, ensure_ascii=False, indent=4))
    #         time.sleep(2)
