from openai import OpenAI
import base64
from textwrap import dedent
from pydantic import BaseModel

client = OpenAI(
    api_key="sk-TaZ7lJp0Q79y98Dp75E7F3E3808e45B9A0822a0dCc4dDe3f",
    base_url="https://api.ocoolai.com/v1"
)
prompt = '''
<task>
描述这是怎样的一张图片?
</task>
'''


def encode_image(binary_data):
    return base64.b64encode(binary_data).decode("utf-8")


with open('1.jpeg', 'rb') as f:
    binary_data = f.read()

base64_image = encode_image(binary_data)
response = client.chat.completions.create(
    model='chatgpt-4o-latest',
    messages=[  # type: ignore
        {
            "role": "user",
            "content": [
                {"type": "text", "text": dedent(prompt)},
                {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{base64_image}"}}
            ]
        }
    ],
    temperature=0.1,
    top_p=0.7,
)

print(response.choices[0].message.content)
