from openai import OpenAI
import httpx

client = OpenAI(
    api_key="sk-TaZ7lJp0Q79y98Dp75E7F3E3808e45B9A0822a0dCc4dDe3f",
    base_url="https://api.ocoolai.com/v1",

    max_retries=0,
    # timeout=60.0
    # timeout=httpx.Timeout(60.0, read=5.0, write=10.0, connect=2.0)
)

# 重写每个请求
# client.with_options(timeout=5 * 1000).completions.create()

response = client.chat.completions.create(  # type: ignore
    messages=[
        {'role': 'user', 'content': "解方程  x^2 - 5x + 6 = 0 ,给出详细步骤和答案"},
    ],
    model='gpt-4.1-2025-04-14',
    stream=False
)

print(response.choices[0].message.content)
