import instructor
from pydantic import BaseModel
from openai import OpenAI


# Define your desired output structure
class ExtractUser(BaseModel):
    name: str
    age: int


# Patch the OpenAI client
client = instructor.from_openai(OpenAI(
    api_key="sk-hFkzyJWLf0i8Kf2XezlpX14POTC4TxnPxIWMHKYrXbQOawAq",
    base_url="https://api.oaibest.com/v1"
))

# Extract structured data from natural language
res: ExtractUser = client.chat.completions.create(  # type: ignore
    model="gpt-4o-mini",
    response_model=ExtractUser,
    messages=[{"role": "user", "content": "<PERSON> is 30 years old."}],
)

assert res.name == "<PERSON>"
assert res.age == 30

print(res.model_dump())
