from openai import OpenAI
from openai import AsyncOpenAI
import asyncio

client = OpenAI(
    api_key="sk-TaZ7lJp0Q79y98Dp75E7F3E3808e45B9A0822a0dCc4dDe3f",
    base_url="https://api.ocoolai.com/v1"
)
async_client = AsyncOpenAI(
    api_key="sk-TaZ7lJp0Q79y98Dp75E7F3E3808e45B9A0822a0dCc4dDe3f",
    base_url="https://api.ocoolai.com/v1"
)


def main():
    response = client.chat.completions.create(  # type: ignore
        messages=[
            {'role': 'user', 'content': "解方程  x^2 - 5x + 6 = 0 ,给出详细步骤和答案"},
        ],
        model='gpt-4.1-2025-04-14',
        stream=True
    )

    for chunk in response:
        print(chunk.choices[0].delta.content, end="", flush=True)


async def main2():
    stream = await async_client.chat.completions.create(  # type: ignore
        messages=[
            {'role': 'user', 'content': "解方程  x^2 - 5x + 6 = 0 ,给出详细步骤和答案"},
        ],
        model='gpt-4.1-2025-04-14',
        stream=True
    )

    async for part in stream:
        print(part.choices[0].delta.content, end="", flush=True)


if __name__ == '__main__':
    asyncio.run(main2())
