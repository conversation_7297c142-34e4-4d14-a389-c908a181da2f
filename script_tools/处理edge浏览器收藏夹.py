from bs4 import BeautifulSoup
import os
from loguru import logger


def get_folder_path(soup, folder_tag):
    """获取文件夹的完整路径"""
    path_parts = []
    current = folder_tag

    while current:
        if current.name == 'h3':
            path_parts.append(current.get_text().strip())

        parent_dl = current.find_parent('dl')
        if parent_dl:
            parent_h3 = parent_dl.find_previous_sibling('h3')
            if parent_h3:
                current = parent_h3
            else:
                break
        else:
            break

    path_parts.reverse()
    return ' > '.join(path_parts) if path_parts else folder_tag.get_text().strip()


def export_links_from_folder_by_name(html_file_path, folder_name):
    """按文件夹名称搜索并导出链接"""
    try:
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        soup = BeautifulSoup(html_content, 'html.parser')

        target_folder_tags = soup.find_all('h3', string=lambda t: t and t.strip() == folder_name)

        if not target_folder_tags:
            logger.warning(f"未找到名为 '{folder_name}' 的文件夹")
            return

        for target_folder_tag in target_folder_tags:
            folder_path = get_folder_path(soup, target_folder_tag)
            logger.info(f"正在处理文件夹：{folder_path}")

            container = target_folder_tag.find_next_sibling('dl')
            if container:
                all_links_in_folder = container.find_all('a')
                logger.info(f"找到 {len(all_links_in_folder)} 个链接")
                for link_tag in all_links_in_folder:
                    print(link_tag['href'])
            else:
                logger.warning("找到了文件夹标签，但其后没有找到预期的<DL>内容列表")
            print('-' * 200)

    except Exception as e:
        logger.error(f"处理文件时发生错误: {e}")


def export_links_from_folder_by_path(html_file_path, folder_path_list):
    """按完整路径搜索并导出链接"""
    try:
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        soup = BeautifulSoup(html_content, 'html.parser')

        def find_folder_by_path(current_element, path_parts, current_path="", depth=0):
            if not path_parts:
                return False

            target_name = path_parts[0]
            remaining_path = path_parts[1:]

            logger.debug(f"{'  ' * depth}搜索层级 {depth}: 查找 '{target_name}' 在路径 '{current_path}'")

            # 查找当前层级的所有 h3 标签
            h3_candidates = []

            if depth == 0:
                # 根层级：直接查找所有 h3 标签
                h3_candidates = current_element.find_all('h3', string=lambda t: t and t.strip() == target_name)
            else:
                # 子层级：只在当前 dl 容器的直接子元素中查找
                for child in current_element.children:
                    if hasattr(child, 'name'):
                        if child.name == 'dt':
                            h3 = child.find('h3', recursive=False)
                            if h3 and h3.get_text().strip() == target_name:
                                h3_candidates.append(h3)

            logger.debug(f"{'  ' * depth}找到 {len(h3_candidates)} 个候选文件夹")

            for folder_tag in h3_candidates:
                new_path = f"{current_path} > {target_name}" if current_path else target_name
                logger.debug(f"{'  ' * depth}检查文件夹: {new_path}")

                if not remaining_path:
                    # 找到目标文件夹
                    logger.info(f"正在处理文件夹：{new_path}")

                    # 查找对应的内容容器
                    container = folder_tag.find_next_sibling('dl')

                    if container:
                        all_links_in_folder = container.find_all('a')
                        logger.info(f"找到 {len(all_links_in_folder)} 个链接")
                        for link_tag in all_links_in_folder:
                            print(link_tag['href'])
                        return True
                    else:
                        logger.warning("找到了文件夹标签，但其后没有找到预期的<DL>内容列表")
                        continue
                else:
                    # 继续在子文件夹中查找
                    container = folder_tag.find_next_sibling('dl')

                    if container:
                        logger.debug(f"{'  ' * depth}进入子层级搜索")
                        if find_folder_by_path(container, remaining_path, new_path, depth + 1):
                            return True
                    else:
                        logger.debug(f"{'  ' * depth}该文件夹没有子内容")

            return False

        path_parts = [part.strip() for part in folder_path_list]
        logger.info(f"开始按路径搜索: {' > '.join(path_parts)}")

        if not find_folder_by_path(soup, path_parts):
            logger.warning(f"未找到路径为 '{' > '.join(path_parts)}' 的文件夹")

    except Exception as e:
        logger.error(f"处理文件时发生错误: {e}")


def main():
    """主函数，提供用户选择处理模式"""
    favorites_file = '/Users/<USER>/Downloads/favorites_2025_8_16.html'

    print("请选择处理模式：")
    print("1. 按文件夹名称搜索")
    print("2. 按完整路径搜索")
    print("3. 启用调试模式的按路径搜索")

    choice = input("请输入选择 (1, 2 或 3): ").strip()

    if choice == '1':
        folder_name = input("请输入要搜索的文件夹名称: ").strip()
        if folder_name:
            export_links_from_folder_by_name(favorites_file, folder_name)
        else:
            logger.error("文件夹名称不能为空")
    elif choice == '2':
        folder_path = input("请输入完整的文件夹路径 (用 > 分隔): ").strip()
        if folder_path:
            path_parts = [part.strip() for part in folder_path.split('>')]
            export_links_from_folder_by_path(favorites_file, path_parts)
        else:
            logger.error("文件夹路径不能为空")
    elif choice == '3':
        # 启用调试模式
        logger.remove()
        logger.add(lambda msg: print(msg, end=''), level="DEBUG")
        folder_path = input("请输入完整的文件夹路径 (用 > 分隔): ").strip()
        if folder_path:
            path_parts = [part.strip() for part in folder_path.split('>')]
            export_links_from_folder_by_path(favorites_file, path_parts)
        else:
            logger.error("文件夹路径不能为空")
    else:
        logger.error("无效的选择，请输入 1, 2 或 3")


if __name__ == "__main__":
    main()
