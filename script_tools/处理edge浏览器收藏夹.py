from bs4 import BeautifulSoup
import os
import re
from loguru import logger


def get_folder_path(soup, folder_tag):
    """获取文件夹的完整路径"""
    path_parts = []
    current = folder_tag

    while current:
        if current.name == 'h3':
            path_parts.append(current.get_text().strip())

        parent_dl = current.find_parent('dl')
        if parent_dl:
            parent_h3 = parent_dl.find_previous_sibling('h3')
            if parent_h3:
                current = parent_h3
            else:
                break
        else:
            break

    path_parts.reverse()
    return ' > '.join(path_parts) if path_parts else folder_tag.get_text().strip()



def parse_folder_path_input(raw: str):
    """解析用户输入的路径，去除日志前缀与多余空白，支持不同分隔符。
    例："正在处理文件夹：收藏夹栏 > chatGPT > github > MCP" -> ["收藏夹栏","chatGPT","github","MCP"]
    """
    s = raw.strip()
    # 去除可能混入的日志前缀或 ANSI 控制字符
    s = re.sub(r"\x1b\[[0-9;]*m", "", s)  # 清理颜色码
    s = re.sub(r"^(正在处理文件夹：|正在处理文件夹:|Processing folder:)", "", s)
    s = s.strip()

    # 统一分隔符：'>'、'／'、'/'、'|'、'→'、'>' 两侧可能有空格
    parts = re.split(r"\s*[>\/\|→＞]+\s*", s)
    # 过滤空项
    parts = [p.strip() for p in parts if p.strip()]
    return parts



def get_folder_container_by_h3(h3_tag):
    """从 h3 标签定位对应文件夹内容的 dl 容器，兼容 <DT><H3></H3></DT><DL> 和 <DT><H3></H3></DT><DD><DL> 两种结构"""
    if not h3_tag:
        return None

    parent = h3_tag.parent
    # 优先基于 DT 寻找兄弟节点
    if parent and getattr(parent, 'name', '').lower() == 'dt':
        # 遍历后续兄弟，跳过空白文本，直到遇到 dl 或 dd
        sib = parent.next_sibling
        while sib is not None and getattr(sib, 'name', None) is None:
            sib = sib.next_sibling
        if sib is not None:
            name = getattr(sib, 'name', '').lower()
            if name == 'dl':
                return sib
            if name == 'dd':
                dl = sib.find('dl', recursive=False)
                if dl:
                    return dl
    # 兜底：直接查找 h3 的下一个 dl 兄弟
    return h3_tag.find_next_sibling('dl')


def enumerate_child_folder_h3s(container_dl):
    """枚举当前 dl 容器下的直接子文件夹 h3 标签（只看一层，同时兼容 <p> 包裹的情况）"""
    if not container_dl:
        return []
    h3s = []
    # 1) 直接子级 DT
    for dt in container_dl.find_all('dt', recursive=False):
        h3 = dt.find('h3', recursive=False)
        if h3:
            h3s.append(h3)
    # 2) 直接子级 P 内的 DT
    for p in container_dl.find_all('p', recursive=False):
        for dt in p.find_all('dt', recursive=False):
            h3 = dt.find('h3', recursive=False)
            if h3:
                h3s.append(h3)
    return h3s


def export_links_from_folder_by_name(html_file_path, folder_name):
    """按文件夹名称搜索并导出链接"""
    try:
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        soup = BeautifulSoup(html_content, 'html.parser')

        target_folder_tags = soup.find_all('h3', string=lambda t: t and t.strip() == folder_name)

        if not target_folder_tags:
            logger.warning(f"未找到名为 '{folder_name}' 的文件夹")
            return

        for target_folder_tag in target_folder_tags:
            folder_path = get_folder_path(soup, target_folder_tag)
            logger.info(f"正在处理文件夹：{folder_path}")

            container = get_folder_container_by_h3(target_folder_tag)
            if container:
                all_links_in_folder = container.find_all('a')
                logger.info(f"找到 {len(all_links_in_folder)} 个链接")
                for link_tag in all_links_in_folder:
                    print(link_tag['href'])
            else:
                logger.warning("找到了文件夹标签，但其后没有找到预期的<DL>内容列表")
            print('-' * 200)

    except Exception as e:
        logger.error(f"处理文件时发生错误: {e}")


def export_links_from_folder_by_path(html_file_path, folder_path_list):
    """按完整路径搜索并导出链接"""
    try:
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        soup = BeautifulSoup(html_content, 'html.parser')

        def find_folder_by_path(current_element, path_parts, current_path="", depth=0):
            if not path_parts:
                return False

            target_name = path_parts[0]
            remaining_path = path_parts[1:]

            logger.debug(f"{'  ' * depth}搜索层级 {depth}: 查找 '{target_name}' 在路径 '{current_path}'")

            # 查找当前层级的所有 h3 标签
            h3_candidates = []

            if depth == 0:
                h3_candidates = current_element.find_all('h3', string=lambda t: t and t.strip() == target_name)
            else:
                # 子层级：只在当前 dl 容器的一层子级中查找 h3
                child_h3s = enumerate_child_folder_h3s(current_element)
                logger.debug(f"{'  ' * depth}本层子文件夹: {[h.get_text(strip=True) for h in child_h3s]}")
                for h3 in child_h3s:
                    if h3.get_text().strip() == target_name:
                        h3_candidates.append(h3)

            logger.debug(f"{'  ' * depth}找到 {len(h3_candidates)} 个候选文件夹")

            for folder_tag in h3_candidates:
                new_path = f"{current_path} > {target_name}" if current_path else target_name
                logger.debug(f"{'  ' * depth}检查文件夹: {new_path}")

                if not remaining_path:
                    # 找到目标文件夹
                    logger.info(f"正在处理文件夹：{new_path}")

                    # 查找对应的内容容器
                    container = get_folder_container_by_h3(folder_tag)

                    if container:
                        all_links_in_folder = container.find_all('a')
                        logger.info(f"找到 {len(all_links_in_folder)} 个链接")
                        for link_tag in all_links_in_folder:
                            print(link_tag['href'])
                        return True
                    else:
                        logger.warning("找到了文件夹标签，但其后没有找到预期的<DL>内容列表")
                        continue
                else:
                    # 继续在子文件夹中查找
                    container = get_folder_container_by_h3(folder_tag)

                    if container:
                        logger.debug(f"{'  ' * depth}进入子层级搜索")
                        if find_folder_by_path(container, remaining_path, new_path, depth + 1):
                            return True
                    else:
                        logger.debug(f"{'  ' * depth}该文件夹没有子内容")

            return False

        path_parts = [part.strip() for part in folder_path_list]
        logger.info(f"开始按路径搜索: {' > '.join(path_parts)}")

        if not find_folder_by_path(soup, path_parts):
            logger.warning(f"未找到路径为 '{' > '.join(path_parts)}' 的文件夹")

    except Exception as e:
        logger.error(f"处理文件时发生错误: {e}")


def analyze_folder_structure(html_file_path, target_folder_name):
    """分析指定文件夹的所有路径位置，帮助用户找到正确的路径"""
    try:
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        soup = BeautifulSoup(html_content, 'html.parser')

        target_folder_tags = soup.find_all('h3', string=lambda t: t and t.strip() == target_folder_name)

        if not target_folder_tags:
            print(f"未找到名为 '{target_folder_name}' 的文件夹")
            return

        print(f"找到 {len(target_folder_tags)} 个名为 '{target_folder_name}' 的文件夹：")
        print("=" * 80)

        for i, folder_tag in enumerate(target_folder_tags, 1):
            folder_path = get_folder_path(soup, folder_tag)
            container = get_folder_container_by_h3(folder_tag)

            if container:
                links_count = len(container.find_all('a'))
                print(f"{i}. 路径：{folder_path}")
                print(f"   链接数量：{links_count}")

                # 显示子文件夹
                child_h3s = enumerate_child_folder_h3s(container)
                if child_h3s:
                    child_names = [h.get_text().strip() for h in child_h3s]
                    print(f"   子文件夹：{child_names}")
                else:
                    print("   子文件夹：无")
            else:
                print(f"{i}. 路径：{folder_path}")
                print("   无内容容器")
            print("-" * 40)

    except Exception as e:
        print(f"分析时发生错误: {e}")


def show_folder_tree(html_file_path, max_depth=3):
    """显示文件夹树结构，限制深度避免输出过多"""
    try:
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        soup = BeautifulSoup(html_content, 'html.parser')

        def print_tree(element, prefix="", depth=0):
            if depth > max_depth:
                return

            if depth == 0:
                # 根层级：查找所有顶级文件夹
                root_h3s = soup.find_all('h3')
                # 过滤出真正的根层级文件夹（没有父级dl的）
                top_level_h3s = []
                for h3 in root_h3s:
                    parent_dl = h3.find_parent('dl')
                    if not parent_dl or not parent_dl.find_parent('dl'):
                        top_level_h3s.append(h3)

                print("文件夹树结构：")
                print("=" * 60)
                for h3 in top_level_h3s[:10]:  # 限制显示前10个避免过多
                    folder_name = h3.get_text().strip()
                    print(f"📁 {folder_name}")
                    container = get_folder_container_by_h3(h3)
                    if container:
                        print_tree(container, "  ", depth + 1)
            else:
                # 子层级
                child_h3s = enumerate_child_folder_h3s(element)
                for h3 in child_h3s[:5]:  # 每层最多显示5个
                    folder_name = h3.get_text().strip()
                    print(f"{prefix}📁 {folder_name}")
                    container = get_folder_container_by_h3(h3)
                    if container and depth < max_depth:
                        print_tree(container, prefix + "  ", depth + 1)

        print_tree(soup)
        print("=" * 60)
        print(f"注：为避免输出过多，每层最多显示5个文件夹，最大深度{max_depth}层")

    except Exception as e:
        print(f"显示树结构时发生错误: {e}")


def main():
    """主函数，提供用户选择处理模式"""
    favorites_file = '/Users/<USER>/Downloads/favorites_2025_8_16.html'

    print("请选择处理模式：")
    print("1. 按文件夹名称搜索")
    print("2. 按完整路径搜索")
    print("3. 启用调试模式的按路径搜索")
    print("4. 分析文件夹结构（帮助找到正确路径）")
    print("5. 显示完整文件夹树结构")

    choice = input("请输入选择 (1, 2, 3, 4 或 5): ").strip()

    if choice == '1':
        folder_name = input("请输入要搜索的文件夹名称: ").strip()
        if folder_name:
            export_links_from_folder_by_name(favorites_file, folder_name)
        else:
            logger.error("文件夹名称不能为空")
    elif choice == '2':
        folder_path = input("请输入完整的文件夹路径 (用 > 分隔): ").strip()
        if folder_path:
            path_parts = parse_folder_path_input(folder_path)
            logger.debug(f"解析路径: {path_parts}")
            export_links_from_folder_by_path(favorites_file, path_parts)
        else:
            logger.error("文件夹路径不能为空")
    elif choice == '3':
        # 启用调试模式
        logger.remove()
        logger.add(lambda msg: print(msg, end=''), level="DEBUG")
        folder_path = input("请输入完整的文件夹路径 (用 > 分隔): ").strip()
        if folder_path:
            path_parts = parse_folder_path_input(folder_path)
            logger.debug(f"解析路径: {path_parts}")
            export_links_from_folder_by_path(favorites_file, path_parts)
        else:
            logger.error("文件夹路径不能为空")
    elif choice == '4':
        folder_name = input("请输入要分析的文件夹名称: ").strip()
        if folder_name:
            analyze_folder_structure(favorites_file, folder_name)
        else:
            print("文件夹名称不能为空")
    elif choice == '5':
        show_folder_tree(favorites_file)
    else:
        logger.error("无效的选择，请输入 1, 2, 3, 4 或 5")


if __name__ == "__main__":
    main()
