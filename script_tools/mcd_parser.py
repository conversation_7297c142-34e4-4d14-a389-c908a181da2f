import pylab
import bs4


def get_mcd_content():
    path = r"C:\Users\<USER>\Downloads\1.mcd"
    mcd = ''
    with open(path, 'rb') as file:
        mcd = file.read()
    return str(mcd)


def parse_mcd_content(mcd):
    print(mcd)
    soup = bs4.BeautifulSoup(mcd, 'lxml')
    print(soup)
    events = soup.select('mouseevents')
    print(len(events))
    results = []
    for event in events:
        pos = event.find('d3p1:x')
        print(pos.text)
        results.append(int(pos.text))
    return results


def get_track(distance):
    track = [0]
    current = 0
    mid = distance * 4 / 5
    t = 0.2
    v = 0

    while current < distance:
        if current < mid:
            a = 2
        else:
            a = -3
        v0 = v
        v = v0 + a * t
        move = v0 * t + 1 / 2 * a * t * t
        current += move
        track.append(track[len(track) - 1] + round(move))
    return track


# content = get_mcd_content()
# results = parse_mcd_content(content)
# x = range(0, len(results))
# pylab.plot(x, results, 'r')
# pylab.show()

results = get_track(130)
# results = [0, 0, 8, 16, 23, 31, 38, 44, 51, 57, 63, 68, 74, 79, 84, 88, 92, 96, 100, 103, 106, 109, 112, 114, 116, 118,
#            120, 121, 122, 122, 123]
print(results)
x = range(0, len(results))
pylab.plot(x, results, 'r')
pylab.show()
