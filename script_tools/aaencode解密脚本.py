# -*- coding: utf-8 -*-
import re


class AAencode():
    b = [
        "(c^_^o)",
        "(ﾟΘﾟ)",
        "((o^_^o) - (ﾟΘﾟ))",
        "(o^_^o)",
        "(ﾟｰﾟ)",
        "((ﾟｰﾟ) + (ﾟΘﾟ))",
        "((o^_^o) +(o^_^o))",
        "((ﾟｰﾟ) + (o^_^o))",
        "((ﾟｰﾟ) + (ﾟｰﾟ))",
        "((ﾟｰﾟ) + (ﾟｰﾟ) + (ﾟΘﾟ))",
        "(ﾟДﾟ) .ﾟωﾟﾉ",
        "(ﾟДﾟ) .ﾟΘﾟﾉ",
        "(ﾟДﾟ) ['c']",
        "(ﾟДﾟ) .ﾟｰﾟﾉ",
        "(ﾟДﾟ) .ﾟДﾟﾉ",
        "(ﾟДﾟ) [ﾟΘﾟ]"
    ]

    def __init__(self):
        pass

    def encode(self, text):
        r = "ﾟωﾟﾉ= /｀ｍ´）ﾉ ~┻━┻   //*´∇｀*/ ['_']; o=(ﾟｰﾟ)  =_=3; c=(ﾟΘﾟ) =(ﾟｰﾟ)-(ﾟｰﾟ); "
        if re.match("ひだまりスケッチ×(365|３５６)\s*来週も見てくださいね[!！]", text):
            r += "X=_=3; "
            r += "\r\n\r\n    X / _ / X < \"来週も見てくださいね!\";\r\n\r\n"
        r += "(ﾟДﾟ) =(ﾟΘﾟ)= (o^_^o)/ (o^_^o);" + \
             "(ﾟДﾟ)={ﾟΘﾟ: '_' ,ﾟωﾟﾉ : ((ﾟωﾟﾉ==3) +'_') [ﾟΘﾟ] " + \
             ",ﾟｰﾟﾉ :(ﾟωﾟﾉ+ '_')[o^_^o -(ﾟΘﾟ)] " + \
             ",ﾟДﾟﾉ:((ﾟｰﾟ==3) +'_')[ﾟｰﾟ] }; (ﾟДﾟ) [ﾟΘﾟ] =((ﾟωﾟﾉ==3) +'_') [c^_^o];" + \
             "(ﾟДﾟ) ['c'] = ((ﾟДﾟ)+'_') [ (ﾟｰﾟ)+(ﾟｰﾟ)-(ﾟΘﾟ) ];" + \
             "(ﾟДﾟ) ['o'] = ((ﾟДﾟ)+'_') [ﾟΘﾟ];" + \
             "(ﾟoﾟ)=(ﾟДﾟ) ['c']+(ﾟДﾟ) ['o']+(ﾟωﾟﾉ +'_')[ﾟΘﾟ]+ ((ﾟωﾟﾉ==3) +'_') [ﾟｰﾟ] + " + \
             "((ﾟДﾟ) +'_') [(ﾟｰﾟ)+(ﾟｰﾟ)]+ ((ﾟｰﾟ==3) +'_') [ﾟΘﾟ]+" + \
             "((ﾟｰﾟ==3) +'_') [(ﾟｰﾟ) - (ﾟΘﾟ)]+(ﾟДﾟ) ['c']+" + \
             "((ﾟДﾟ)+'_') [(ﾟｰﾟ)+(ﾟｰﾟ)]+ (ﾟДﾟ) ['o']+" + \
             "((ﾟｰﾟ==3) +'_') [ﾟΘﾟ];(ﾟДﾟ) ['_'] =(o^_^o) [ﾟoﾟ] [ﾟoﾟ];" + \
             "(ﾟεﾟ)=((ﾟｰﾟ==3) +'_') [ﾟΘﾟ]+ (ﾟДﾟ) .ﾟДﾟﾉ+" + \
             "((ﾟДﾟ)+'_') [(ﾟｰﾟ) + (ﾟｰﾟ)]+((ﾟｰﾟ==3) +'_') [o^_^o -ﾟΘﾟ]+" + \
             "((ﾟｰﾟ==3) +'_') [ﾟΘﾟ]+ (ﾟωﾟﾉ +'_') [ﾟΘﾟ]; " + \
             "(ﾟｰﾟ)+=(ﾟΘﾟ); (ﾟДﾟ)[ﾟεﾟ]='\\\\'; " + \
             "(ﾟДﾟ).ﾟΘﾟﾉ=(ﾟДﾟ+ ﾟｰﾟ)[o^_^o -(ﾟΘﾟ)];" + \
             "(oﾟｰﾟo)=(ﾟωﾟﾉ +'_')[c^_^o];" + \
             "(ﾟДﾟ) [ﾟoﾟ]='\\\"';" + \
             "(ﾟДﾟ) ['_'] ( (ﾟДﾟ) ['_'] (ﾟεﾟ+"
        r += "(ﾟДﾟ)[ﾟoﾟ]+ "
        for i in text:
            n = ord(i)
            t = "(ﾟДﾟ)[ﾟεﾟ]+"
            if n <= 127:
                m = '%o' % n
                for j in m:
                    t += self.b[int(j)] + "+ "
            else:
                t += "(oﾟｰﾟo)+ "
                m = "%x" % n
                m = ("000" + m)[-4:]
                for j in m:
                    t += self.b[int(j, 16)] + "+ "
            r += t
        r += "(ﾟДﾟ)[ﾟoﾟ]) (ﾟΘﾟ)) ('_');"
        return r

    def decode(self, text):
        for i in range(len(self.b)):
            if i <= 7:
                text = text.replace(self.b[i] + "+ ", str(i))
            else:
                text = text.replace(self.b[i] + "+ ", "%x" % i)
        text = text.replace("(ﾟДﾟ)[ﾟoﾟ]) (ﾟΘﾟ)) ('_');", "")
        rs = text.split("(ﾟДﾟ)[ﾟεﾟ]+")
        print(rs[1:])
        t = ""
        for i in rs[1:]:
            if i.startswith("(oﾟｰﾟo)+ "):
                t += chr(int(i[9:], 16))
            else:
                t += chr(int(i, 8))
        return t


AA = AAencode()
# print(AA.decode())
