import json
import os

# 获取用户主目录下的 extensions 路径
extensions_dir = os.path.expanduser(os.path.join('~', '.vscode', 'extensions'))

if not os.path.exists(extensions_dir):
    print("Extensions 目录未找到。请确认 VS Code 安装路径。")
else:
    for folder in os.listdir(extensions_dir):
        package_path = os.path.join(extensions_dir, folder, 'package.json')
        if os.path.exists(package_path):
            try:
                with open(package_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    display_name = data.get('displayName', '未知名称')
                    publisher = data.get('publisher', '未知发布者')
                    version = data.get('version', '未知版本')
                    print(f"{display_name} - {folder}")
            except Exception as e:
                print(f"读取 {folder} 失败: {e}")
