from aligo import Aligo, BaseFile
import re


def handle(path: str, f: BaseFile):
    name = f.name[:-4]
    f = open(r'book.txt', 'a+', encoding='utf-8')
    print(name)
    f.write(name + '\n')
    f.close()

    # size = f.size / 1048576
    # if size > 100:
    #     print(f.name)
    #     ali.move_file_to_trash(f.file_id)


def remove():
    remove_list = open(r'remove_list.txt', 'r', encoding='utf-8').readlines()
    for i in remove_list:
        remove_one = i.strip()
        find = ali.search_files(name=remove_one)[0] if ali.search_files(name=remove_one) != [] else False
        if not not find:
            print(find.name)
            ali.move_file_to_trash(find.file_id)


def move(target_path_name: str):
    move_list = open(r'move.txt', 'r', encoding='utf-8').readlines()
    for i in move_list:
        move_list = i.strip()
        find = ali.search_files(name=move_list)[0] if ali.search_files(name=move_list) != [] else False
        target = ali.search_files(name=target_path_name)[0] if ali.search_files(name=target_path_name) != [] else False
        if (not not find) and (not not target):
            print(find.name)
            ali.move_file(find.file_id, target.file_id)


if __name__ == '__main__':
    ali = Aligo()
