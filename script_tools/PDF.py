from selenium import webdriver
import time
import json

options = webdriver.ChromeOptions()
# options.add_argument('--headless')

driver = webdriver.Chrome(options=options)
driver.get("https://mp.weixin.qq.com/s/d4ApAL447wti8Mxk8TE_Og")
driver.execute_script("window.print();")
driver.get("chrome://print/")
# content = driver.find_element_by_id('js_article').text
# driver.find_element_by_xpath('//*[@id="destinationSelect"]//print-preview-settings-section[1]/div').click()
time.sleep(5)
driver.quit()
