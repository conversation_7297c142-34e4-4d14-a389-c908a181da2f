#%%
address = r"C:\Users\<USER>\OneDrive\Pictures\壁纸\wallhaven-x8ye3z.jpg"
print(address.replace('\\', '/'))
#%%
def read_links(path):
    with open(path, 'r', encoding='utf-8') as f:
        links = f.readlines()

    for link in range(len(links)):
        links[link] = links[link].strip()

    return links

olds = read_links(r"E:\temp\学习\a.txt")
news = read_links(r'E:\demo_program\python\pycharm\temp.txt')
for new in news:
    if new not in olds:
        print(new)
#%%
